/*  custome font size */
.f-10 {
  font-size: 10px;
}

.f-11 {
  font-size: 11px;
}

.f-12 {
  font-size: 12px !important; 
}

.f-13 {
  font-size: 13px !important;
}

.f-14 {
  font-size: 14px !important;
}

.f-16 {
  font-size: 16px !important;
}

.f-17 {
  font-size: 17px;
}

.f-18 {
  font-size: 18px;
}

.f-19 {
  font-size: 19px;
}

.f-20 {
  font-size: 20px;
}

.f-21 {
  font-size: 21px;
}

.f-22 {
  font-size: 22px !important;
}

.f-23 {
  font-size: 23px;
}
.f-24 {
  font-size: 24px !important;
}
.f-25 {
  font-size: 25px !important;
}
.f-26 {
  font-size: 26px !important;
}
.f-27 {
  font-size: 27px !important;
}
.pt-10 {
  padding-top: 10px;
}

.mt-32 {
  padding-top: 32px !important;
}

.mt-35 {
  padding-top: 35px !important;
}

.mt-20 {
  padding-top: 20px !important;
}

.ml--15 {
  margin-left: -15px;
}

.ml--20 {
  margin-left: -20px;
}

.ml--25 {
  margin-left: -25px;
}

.mr--25 {
  margin-right: -25px;
}

.mr--35 {
  margin-right: -35px;
}

.mt--35 {
  margin-top: -35px !important;
}

.mt--30 {
  margin-top: -30px !important;
}

.mt--15 {
  margin-top: -15px !important;
}

.mt--18 {
  margin-top: -18px !important;
}

.ml--40 {
  margin-left: -40px;
}

.ml--45 {
  margin-left: -45px;
}

.mr--45 {
  margin-right: -45px;
}

.p-40 {
  padding: 40px;
}

.pl-35 {
  padding-left: 35px;
}

.pl-22 {
  padding-left: 22px;
}
.l-blue{
  background-color: #E1F5FA;
}
.text-white {
  color: white !important;
}

.bg-gray {
  background: #f5f6f8 !important;
}

.bg-white {
  background: #ffff;
}
.text-dark-blue{
  color:#224796
}
.text-light-blue{
  color:#53AEF9
}
.text-blue {
  color: #147afc !important;
}

.text-green {
  color: #7db519 !important;
}

.text-darkGreen {
  color: #3b803e !important;
}

.text-gray {
  color: #b7b1b1 !important;
}

.text-darkgray {
  color: #616060 !important;
}
.text-NewLightgray {
  color: #9ca3af !important;
}

.box-1 {.text-dark-blue{
  color:#224796
}
.text-light-blue{
  color:#53AEF9
}

  border: 1px solid #cacaca;
  padding: 14px 10px 14px 10px;
  text-align: center;
  border-radius: 2px;
}

.box-2 {
  border: 1px solid #cacaca;
  padding: 16px 10px 16px 10px;
  border-radius: 2px;
  font-size: 14px;
}

.box-3 {
  border: 1px solid #cacaca;
  padding: 10px 10px 10px 10px;
  text-align: center;
  border-radius: 2px;
}

.program-plus {
  margin-left: -14px;
  padding-top: 24px;
}

.program-plus1 {
  margin-left: -14px;
  padding-top: 31px;
}

.mt-20 {
  margin-top: 20px;
}
.searchWidth {
  width: 30%;
}

@media only screen and (min-width: 320px) and (max-width: 1024px) {
  .box-1 {
    border: 1px solid #cacaca;
    padding: 16px 10px 16px 10px;
    text-align: center;
    border-radius: 2px;
  }
  .searchWidth {
    width: 26%;
  }
}

/* custome font size */

@media only screen and (min-width: 320px) and (max-width: 767px) {
  .modal-header {
    font-size: 15px !important;
  }

  .form-radio {
    top: 5px !important;
    height: 17px !important;
    width: 17px !important;
  }

  .form-radio:checked::before {
    font: 12px/0.8 'Open Sans', sans-serif !important;
    left: 4px !important;
    top: 3px !important;
  }

  .radio-label {
    padding-right: 5px !important;
    padding-left: 0px !important;
    padding-top: 13px !important;
    font-size: 13px;
  }

  .date-pickers {
    width: 100% !important;
    padding: 10px !important;
  }

  /* .modal-dialog {
    max-width: 95% !important;
    margin: 1cm !important;
  } */

  .model-box {
    padding: 10px !important;
    border: 1px solid #ced4da;
    font-size: 11px;
  }

  .model-box1 {
    border: 1px solid #ced4da;
    height: calc(46px + 2px) !important;
    font-size: 11px;
  }
}

@media only screen and (min-width: 320px) and (max-width: 359px) {
  .dash-inner h3 b {
    font-size: 13px !important;
  }

  .dash-inner span {
    font-size: 12px !important;
  }

  .center-dropdown {
    padding: 2px 10px 0px 0px !important;
  }

  .dash-inner h3 {
    font-size: 21px !important;
  }

  .nav-item {
    padding-left: 0px !important;
  }

  .tables h3 {
    font-size: 14px !important;
  }

  .vu {
    padding: 7px 10px 1px 0px !important;
    font-size: 12px !important;
  }

  .btn-group-sm > .btn,
  .btn-sm {
    font-size: 11px !important;
  }

  b.pl-3 {
    padding-left: 0px !important;
  }
}

@media only screen and (min-width: 360px) and (max-width: 767px) {
  .dash-inner h3 b {
    font-size: 16px !important;
  }

  .m-ham {
    float: right;
  }

  .dash-inner span {
    font-size: 12px !important;
  }

  .center-dropdown {
    padding: 2px 10px 0px 0px;
  }

  .dash-inner h3 {
    font-size: 22px !important;
    margin-bottom: 5px;
  }

  .headerbar {
    padding: 10px 10px 23px 11px !important;
  }

  .nav-item {
    padding-left: 0px !important;
  }

  b.pl-3 {
    padding-left: 0px !important;
  }
}

@media only screen and (min-width: 768px) and (max-width: 900px) {
  .ml_250 {
    margin-left: 200px !important;
  }

  .wd_250 {
    width: 230px !important;
  }

  .dash-inner span {
    font-size: 10px !important;
  }

  .dash-inner h3 {
    font-size: 19px !important;
  }

  .dash-inner h3 b {
    font-size: 11px !important;
  }

  .center-dropdown {
    padding: 0px 10px 0px 0px !important;
  }

  b.pl-3 {
    padding-left: 0px !important;
  }

  .date-pickers {
    width: 104% !important;
  }

  /* .modal-dialog {
    max-width: 95% !important;
    margin: 1cm !important;
  } */
}

@media only screen and (min-width: 990px) and (max-width: 1200px) {
  .ml_250 {
    margin-left: 200px !important;
  }

  .wd_250 {
    width: 200px !important;
  }

  .date-pickers {
    width: 100% !important;
  }

  /* .modal-dialog {
    max-width: 95% !important;
    margin: 1cm !important;
  } */
}

.fr::after {
  content: '\f054';
  font-family: FontAwesome;
  padding-left: 52px;
}

.card-header::before {
  content: '\f107';
  font-family: FontAwesome;
  float: right;
  font-weight: bold;
  font-size: 17px;
}

/* Tabs view  start*/
.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  color: #ffffff;
  background-color: #fff0;
  border-color: #dee2e600 #dee2e600 #1758d1;
  /* border-bottom: 3.5px solid #007dff; */
  border-bottom: 2px solid #ffffff;
}

.nav-tabs .nav-link {
  border: 1px solid #65171700;
  /* border-top-left-radius: .25rem; */
  /* border-top-right-radius: .25rem; */
  /* color: #818ea3; */
  color: #ffffff;
}

#tab-example {
  padding: 14px 25px;
}

.nav-tabs .nav-item {
  padding: 17px 28px;
  font-size: 15px;
}

.program-icon::after {
  content: '\f107';
  font-family: FontAwesome;
  font-weight: bold;
  font-size: 17px;
  color: #0047cc;
}

.program-icon::after {
  content: '\f00c';
  font-family: FontAwesome;
  font-weight: bold;
  font-size: 22px;
}

.program-icon:hover::after {
  font-size: 50px;
  content: '\f00d';
  font-family: FontAwesome;
  font-weight: bold;
  font-size: 22px;
  margin-top: -2px;
  margin-right: 2px;
}

.remove_hover {
  cursor: pointer;
}

.card {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fafafa;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  margin-bottom: 13px;
}

/* .label-remove { // siddiq it is affecting rotation group setting popup design in student grouping
  margin-top: -22px;
} */

.label-remove1 {
  margin-top: -22px;
}

/* Tabs view end*/

.switch {
  position: relative;
  display: inline-block;
  width: 51px !important;
  height: 26px !important;
  float: right;
}

.switch-accordion {
  position: relative;
  display: inline-block;
  width: 51px !important;
  height: 26px !important;
  float: right;
  margin-right: 20px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider_check {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.slider_check:before {
  position: absolute;
  content: '';
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2.9px;
  background-color: white;
  transition: 0.4s;
}

input:checked + .slider_check {
  background-color: #2196f3;
}

input:focus + .slider_check {
  box-shadow: 0 0 1px #2196f3;
}

input:checked + .slider_check:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(26px);
}

/* Rounded sliders */
.slider_check.round {
  border-radius: 34px;
}

.slider_check.round:before {
  border-radius: 50%;
}

.nav-tabs .nav-link:hover {
  /* border-color: #ffffff #ffffff #007dff; */
  border-color: #fff0;
  border-bottom: 2px solid #fff;
}

.form-group {
  margin-bottom: 5px;
}

.select_height {
  height: 55px !important;
  border-radius: unset;
}

.float-right.cursor {
  cursor: pointer;
}

.form-control:disabled {
  cursor: no-drop;
  background-color: #e9ebec8f !important;
}

.schedule-outsideCampus-date-picker .react-datepicker-wrapper {
  width: 100%;
  display: block;
}

.pd-y-1 {
  padding-top: 1px !important;
  padding-bottom: 1px !important;
}

.export-button {
  padding: 9px 20px 9px 20px;
  border-radius: 15px !important;
  background: #f5f6f8 !important;
}

.list-button {
  padding: 6px 20px 5px 20px;
  border-radius: 15px !important;
  background: #f5f6f8 !important;
}

.customize_dropdown {
  /* height: 55px !important; */
  border-radius: 2px;
}

.label_none {
  margin-top: -13px;
}

/* .input-group-text {
 
  padding: 10px 28px;
} */

.hide_content {
  pointer-events: none;
  background: #e0e2e28a;
  opacity: 0.5;
}

.file-upload input[type='file'] {
  display: none;
  padding: 20px;
}

.rounded-lg {
  border-radius: 1rem;
}

.rounded-pill.shadow {
  padding: 9px !important;
  border-radius: 9px;
}

.custom-file-label.rounded-pill::after {
  border-radius: 0 50rem 50rem 0;
}

.login {
  /* background: url(Assets/bg.jpeg) fixed center; */
  height: 600px;
}

.otpcheck {
  border: 2px solid #007bff;
  padding: 0px 15px 0px 15px;
  border-radius: 3px;
}

.checkboxActive {
  background: #007bff;
  color: white;
}

.clickCheckBox {
  padding: 5px 0px 5px 0px;
  cursor: pointer;
}

.text-red {
  color: red !important;
}

.f-20 {
  font-size: 20px;
}

.text-skyblue {
  color: #147afc !important;
}

.text-gray {
  color: #666666 !important;
}
.text-inkBlue {
  color: #2159ba !important;
}

.side-nav-institution-name {
  word-break: break-all;
  white-space: normal;
}

.staffprofile {
  background: linear-gradient(
    36.76deg,
    rgba(26, 123, 220, 0.85) 13%,
    rgba(86, 184, 255, 0.85) 107.28%
  );
}

.floating-label {
  position: relative;
}

.floating-input {
  font-size: 17px;
  padding: 6px 5px 7px 5px;
  display: block;
  width: 100%;
  height: 30px;
  background-color: transparent;
  border: none;
  border-bottom: 1px solid #757575;
}

.floating-input:focus {
  outline: none;
  border-bottom: 2px solid #5264ae;
}

.floatinglabel {
  color: #999;
  font-size: 14px;
  font-weight: normal;
  position: absolute;
  pointer-events: none;
  left: 5px;
  top: 5px;
  transition: 0.2s ease all;
  -moz-transition: 0.2s ease all;
  -webkit-transition: 0.2s ease all;
}

.floating-input:focus ~ label,
.floating-input:not(:placeholder-shown) ~ label {
  top: -20px;
  font-size: 14px;
  color: #5264ae;
}

/* highlighter */

.highlights {
  position: absolute;
  height: 50%;
  width: 100%;
  top: 15%;
  left: 0;
  pointer-events: none;
  opacity: 0.5;
}

/* select starting stylings ------------------------------*/
.select {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  position: relative;
  /* width: 350px; */
}

.select-text {
  position: relative;
  font-family: inherit;
  background-color: transparent;
  width: 100%;
  padding: 0px 4px 5px 5px;
  font-size: 17px;
  border-radius: 0;
  border: none;
  border-bottom: 1px solid #757575;
  -moz-padding-end: 30px !important;
  -webkit-padding-end: 30px !important;
}

/* Remove focus */
.select-text:focus {
  outline: none;
  border-bottom: 2px solid #5264ae;
}

/* Use custom arrow */
.select .select-text {
  appearance: none;
  -webkit-appearance: none;
}

.select:after {
  position: absolute;
  top: 15px;
  right: 15px;

  width: 0;
  height: 0;
  padding: 0;
  content: '';
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #757575;
  pointer-events: none;
}

/* active state */
.select-text:focus ~ .select-label,
.select-text:valid ~ .select-label {
  color: #5264ae;
  top: -18px;
  transition: 0.2s ease all;
  font-size: 14px;
}

/* BOTTOM BARS ================================= */
.select-bar {
  position: relative;
  display: block;
  /* width: 350px; */
}

.select-bar:before,
.select-bar:after {
  content: '';
  height: 2px;
  width: 0;
  bottom: 1px;
  position: absolute;
  color: #5264ae;
  /* transition: 0.2s ease all; */
}

.select-bar:before {
  left: 0%;
}

.select-bar:after {
  right: 0%;
}

/* active state */
.select-text:focus ~ .select-bar:before,
.select-text:focus ~ .select-bar:after {
  width: 100%;
}

/* HIGHLIGHTER ================================== */
.select-highlight {
  position: absolute;
  height: 60%;
  width: 100px;
  top: 25%;
  left: 0;
  pointer-events: none;
  opacity: 0.5;
}

.toplabel {
  color: #5264ae;
  top: -18px;
  font-size: 14px;
}

.pr-30 {
  padding-right: 30px;
}

.radio-label2 {
  padding: 0px 10px 0px 6px;
  margin-bottom: 0px;
}

.floating-input:disabled {
  cursor: no-drop;
  background-color: #e9ebec8f !important;
}

.upload-btn-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.btn-upload {
  border: 2px solid #1775de;
  background-color: #1e7be2;
  padding: 5px 27px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  color: white;
}

.upload-btn-wrapper input[type='file'] {
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}

.img_border {
  border: 1px solid #007bff;
  border-radius: 11px;
  padding: 21px;
}

.flex-container {
  display: flex;
  align-items: stretch;
}

.flex-1 {
  flex-grow: 1;
}

.flex-10 {
  flex-grow: 10;
}

.border-radious-30 {
  border-radius: 30px;
}

.react-datepicker-popper {
  z-index: 999 !important;
}

thead.th-change {
  background: #fff !important;
  border-bottom: 3px solid #52a5ee;
}

.border-bottom-white-2px {
  border-bottom: 2px solid #fff;
}

.min-height-200 {
  min-height: 200px !important;
}

.nth_tabfirst:nth-child(1) {
  margin-left: -40px;
}

.no_wrap {
  white-space: nowrap;
}

.panel-scroll {
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

.rc-menu__submenu {
  list-style: none;
}

.warningAlert {
  color: #ff9800 !important;
  background-color: #fef3c7 !important;
}
.hide_icons {
  pointer-events: none;
  opacity: 0.5;
}

.padding-top-2px {
  padding-top: 2px;
}
.bg-mainBackground {
  background: #f5f6f8;
}
.digi-Shared-bg {
  background: #eff9fb;
}
