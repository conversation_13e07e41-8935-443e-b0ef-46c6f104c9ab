import React from 'react';
import PropTypes from 'prop-types';
import { Autocomplete, Divider, InputAdornment, MenuItem, Paper, TextField } from '@mui/material';
import { makeStyles } from '@mui/styles';
import SearchIcon from '@mui/icons-material/Search';
import { t } from 'i18next';

const useStylesFunction = makeStyles(() => {
  return {
    inputFocus: {
      '& .MuiOutlinedInput-root': {
        '&.Mui-focused fieldset': {
          borderColor: '#147AFC',
        },
      },
    },
  };
});

const StaffSearchInput = (props) => {
  const {
    options = [],
    onChange = () => {},
    onInputChange,
    value = [],
    loading = false,
    open,
    onClose = () => {},
    placeholder,
    freeSolo = false,
    inputValue,
  } = props;
  const classes = useStylesFunction();
  const selectedIds = value.map((o) => o.value);

  const handleClick = (option) => {
    const selectedValue = option.value;
    const updatedData = selectedIds.includes(selectedValue)
      ? value.filter((o) => o.value !== selectedValue)
      : [...value, option];
    onChange(updatedData);
  };

  return (
    <Autocomplete
      options={options}
      value={value}
      open={open}
      onClose={onClose}
      getOptionLabel={(option) => option.label}
      forcePopupIcon={true}
      onChange={(_, val) => onChange(val)}
      onInputChange={onInputChange}
      disableClearable
      id={'autoCompletePadding'}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder={placeholder || t('search_staff')}
          variant={'outlined'}
          className={classes.inputFocus}
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <>
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
                {params.InputProps.startAdornment}
              </>
            ),
            endAdornment: null,
          }}
        />
      )}
      renderOption={(props, option, { index }) => (
        <React.Fragment key={option.value}>
          <MenuItem
            {...props}
            key={option.value}
            selected={selectedIds.includes(option.value)}
            onClick={() => handleClick(option)}
          >
            <span>{option.label}</span>
          </MenuItem>
          {index < options.length - 1 && <Divider />}
        </React.Fragment>
      )}
      PaperComponent={(props) => (
        <Paper {...props} sx={{ boxShadow: '0px 4px 10px rgba(17, 24, 39, 0.2)' }}>
          <div className="p-2 mx-2">{props.children}</div>
        </Paper>
      )}
      filterOptions={(option) => option}
      loading={loading}
      multiple
      freeSolo={freeSolo}
      inputValue={inputValue}
    />
  );
};

StaffSearchInput.propTypes = {
  onChange: PropTypes.func,
  options: PropTypes.array,
  value: PropTypes.array,
  onInputChange: PropTypes.func,
  loading: PropTypes.bool,
  open: PropTypes.bool,
  onClose: PropTypes.func,
  placeholder: PropTypes.string,
  freeSolo: PropTypes.bool,
  inputValue: PropTypes.string,
};

export default StaffSearchInput;
