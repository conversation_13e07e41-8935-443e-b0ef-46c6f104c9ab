import React, { Fragment } from 'react';
import { connect } from 'react-redux';
import { useRouteMatch } from 'react-router-dom';
import styled from 'styled-components';

import Level from './Level';
import CourseContent from './CourseContent';
import CalenderWeeks from './CalenderWeeks';
import DateRanges from './DateRanges';
import AcademicWeeks from './AcademicWeeks';
import { find_sun_to_sat } from '../../../_utils/function';
import LevelOneCourses from './LevelOneCourses';
//  import AcademicEvents from "./AcademicEvents";
import { HeaderRowWrapper, Header } from '../Styled';
import useDataFromStore from '../UtilityComponents/useDataFromStore';
import { Trans } from 'react-i18next';

const Table = styled.div`
  display: grid;
  grid-template-columns: auto 75px;
  margin-left: 25px;
  margin-bottom: 50px;
`;
// overflow: auto;

const TableWOLevel = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 12fr;
  border-radius: 16px 16px 16px 16px;
  border: 1px solid rgba(0, 0, 0, 0.36);
  overflow: hidden;

  &.design {
    border-radius: 0px 16px 16px 16px;
    border: 3px solid #7145cd;
  }
`;

const Wrap = styled.div`
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  overflow: auto;
`;

//course table
const ContentWrap = styled.div`
  flex-grow: 1;
  display: grid;
  text-align: center;
  grid-template-rows: 80px repeat(${(props) => props.len}, 50px);
`;
//copy
const RotationWrapper = styled.div`
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: repeat(${(props) => props.len}, 1fr);
`;
//copy
const Rotation = styled.div`
  display: grid;
  grid-auto-flow: column;
  grid-template-rows: 80px auto;
  grid-template-columns: 1fr;
`;
// grid-template-rows: repeat(51, 50px);

const HeaderSplit = styled.div`
  display: grid;
  grid-template-rows: 80px auto;
`;
// grid-template-columns: 1fr;

const ForWeek = styled(HeaderSplit)``;
//min-width: 80px;
const ForCalWeek = styled(HeaderSplit)``;
//min-width: 50px;
const GridWrapper = styled.div`
  display: grid;
`;

const CourseHeader = styled.div`
  display: grid;
  background-color: #f3f3f3;
  border-left: 1px solid rgba(0, 0, 0, 0.36);
  border-bottom: 1px solid rgba(0, 0, 0, 0.36);
  text-align: center;

  &.left {
    border-left: none;
  }
`;
//  overflow: hidden;

const HeaderCells = styled.div`
  padding: 20px;
  font-size: 18px;
  line-height: 24px;
  letter-spacing: 0.015em;
  color: black;
`;

const Courses = (props) => {
  const {
    year,
    start,
    start1,
    end,
    end1,
    apply1,
    apply2,
    apply3,
    apply4,
    semester,
    is_rotation,
    is_rotation1,
    rotational_course,
    rotational_course1,
    electiveCourse,
    electiveCourse1,
  } = useDataFromStore();
  const { iframeShow, currentPGAccess } = props;
  const match = useRouteMatch();
  const active = match.params.year || 'year2';

  let dates = [];
  let dates1 = [];

  const check = year[active] && start && end ? true : false;

  if (check) {
    dates = find_sun_to_sat(start, end);
  }

  const check1 = year[active] && start1 && end1 ? true : false;

  if (check1) {
    dates1 = find_sun_to_sat(start1, end1);
  }
  return (
    <Fragment>
      {semester.length !== 0 &&
        semester.map((item, i) => {
          const common_rotation = i === 0 ? rotational_course : rotational_course1;
          //const common_elective = i === 0 ? electiveCourse : electiveCourse1;
          const common_is_rotation = i === 0 ? is_rotation : is_rotation1;
          console.log(
            'checking-----------9f',
            common_rotation,
            common_is_rotation,
            rotational_course,
            rotational_course1
          );
          return (
            <Fragment key={i}>
              {i === 1 && (
                <HeaderRowWrapper className={i === 0 ? apply1 : apply3}>
                  <Header className={i === 0 ? apply2 : apply4}>Interim</Header>
                </HeaderRowWrapper>
              )}
              <Table>
                <TableWOLevel className={i === 0 ? apply1 : apply3}>
                  <ForWeek>
                    <CourseHeader className="left">
                      <HeaderCells style={{ minWidth: '10em', paddingTop: '30px' }}>
                        <Trans i18nKey={'program_calendar.date_range'}></Trans>
                      </HeaderCells>
                    </CourseHeader>
                    {check && <DateRanges data={i === 0 ? dates : dates1} />}
                  </ForWeek>
                  <ForCalWeek>
                    <CourseHeader>
                      <HeaderCells>
                        <Trans i18nKey={'program_calendar.calendar_week'}></Trans>
                      </HeaderCells>
                    </CourseHeader>
                    {check && <CalenderWeeks data={i === 0 ? dates : dates1} />}
                  </ForCalWeek>
                  <ForCalWeek>
                    <CourseHeader>
                      <HeaderCells>
                        <Trans i18nKey={'program_calendar.academic_week'}></Trans>
                      </HeaderCells>
                    </CourseHeader>
                    {check && <AcademicWeeks data={i === 0 ? dates : dates1} />}
                  </ForCalWeek>
                  <Wrap>
                    {!year[active] || !common_is_rotation || common_is_rotation === 'no' ? (
                      <HeaderSplit>
                        <CourseHeader>
                          <HeaderCells style={{ paddingTop: '30px' }}>
                            <Trans i18nKey={'program_calendar.courses'}></Trans>
                          </HeaderCells>
                        </CourseHeader>
                        <CourseContent
                          length={i === 0 ? dates.length : dates1.length}
                          choose={i}
                          iframeShow={iframeShow}
                          currentPGAccess={currentPGAccess}
                        />
                      </HeaderSplit>
                    ) : common_rotation && common_rotation?.length !== 0 ? (
                      <ContentWrap len={i === 0 ? dates.length : dates1.length}>
                        <RotationWrapper len={common_rotation.length}>
                          {common_rotation.map((item, num) => (
                            <Rotation key={num}>
                              <CourseHeader>
                                {num === 0 ? (
                                  <HeaderCells style={{ minWidth: '10em' }}>
                                    Rotational 1 Courses
                                  </HeaderCells>
                                ) : (
                                  <HeaderCells style={{ minWidth: '10em' }}>{`Rotational ${
                                    num + 1
                                  } Courses`}</HeaderCells>
                                )}
                              </CourseHeader>
                              <GridWrapper>
                                <LevelOneCourses
                                  number={num}
                                  rotational={true}
                                  choose={i}
                                  iframeShow={iframeShow}
                                  currentPGAccess={currentPGAccess}
                                />
                              </GridWrapper>
                            </Rotation>
                          ))}
                        </RotationWrapper>
                        {/* <AcademicEvents rotate={true} choose={i} /> */}
                        {/* <AcademicEvents
                          rotate={true}
                          elective={true}
                          elective_data={
                            common_elective === true ? [] : common_elective
                          }
                          choose={i}
                        /> */}
                      </ContentWrap>
                    ) : (
                      <HeaderSplit>
                        <CourseHeader>
                          <HeaderCells style={{ paddingTop: '30px' }}>
                            Rotational Courses
                          </HeaderCells>
                        </CourseHeader>
                        <CourseContent
                          length={i === 0 ? dates.length : dates1.length}
                          choose={i}
                          iframeShow={iframeShow}
                          currentPGAccess={currentPGAccess}
                        />
                      </HeaderSplit>
                    )}
                  </Wrap>
                  {/* <RotationalWrapper
            rep={
              props[active]["number_of_rotation"] !== 0
                ? props[active]["number_of_rotation"]
                : 1
            }
          >
            {check_rotational(
              props[active]["level_one_is_rotation"],
              props[active]["number_of_rotation"],
              dates.length
            )}
          </RotationalWrapper> */}
                </TableWOLevel>
                <HeaderSplit>
                  <div></div>
                  <Level choose={i} />
                </HeaderSplit>
              </Table>
            </Fragment>
          );
        })}
    </Fragment>
  );
};

const mapStateToProps = ({ calender }) => ({
  // start: calender.academic_year_start,
  // end: calender.academic_year_end,
  // active: calender.active_year,
});

export default connect(mapStateToProps)(Courses);
