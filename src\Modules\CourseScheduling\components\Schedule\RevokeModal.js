import React from 'react';
import PropTypes from 'prop-types';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';

function RevokeModal({ open, setRevokeOpen, handleSave }) {
  function handleClose() {
    setRevokeOpen(false);
  }

  function handleSubmit() {
    handleSave({ isRevoke: true });
  }
  return (
    <Dialog fullWidth={true} maxWidth={'sm'} open={open} onClose={handleClose}>
      <div className="m-3">
        <div className="h5 pt-1">Revoke</div>
        <div className="f-14 text-muted">
          Are you sure you want to revoke the missed to completed session?
        </div>
      </div>
      <DialogActions className="pb-2">
        <Button variant="outlined" onClick={handleClose}>
          CLOSE
        </Button>
        <Button variant="contained" color="primary" onClick={handleSubmit}>
          SAVE
        </Button>
      </DialogActions>
    </Dialog>
  );
}

RevokeModal.propTypes = {
  open: PropTypes.bool,
  setRevokeOpen: PropTypes.func,
  handleSave: PropTypes.func,
};

export default RevokeModal;
