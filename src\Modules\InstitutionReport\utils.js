import moment from 'moment';
import { List } from 'immutable';

export const checkPendingStatus = (item) => {
  const scheduleStartDate = Date.parse(item.get('scheduleStartDateAndTime', new Date()));
  const scheduleEndDate = Date.parse(item.get('scheduleEndDateAndTime', new Date()));
  const utcDate = Date.parse(moment(new Date()).utc().format());
  if (utcDate < scheduleStartDate) {
    return {
      color: 'text-dark',
      name: 'Upcoming',
    };
  } else if (scheduleStartDate < utcDate && utcDate < scheduleEndDate) {
    return {
      color: 'text-warning',
      name: 'Not Started',
    };
  } else {
    return {
      color: 'text-warning',
      name: 'Not Started',
    };
  }
};

export const getStatusColor = (item) => {
  const status = item.get('status', '');
  const isActive = item.get('isActive', false);
  if (!isActive) {
    return {
      color: 'text-red',
      name: 'Cancelled',
    };
  }
  switch (status) {
    case 'pending':
      return checkPendingStatus(item);
    case 'completed':
      return {
        color: 'text-darkGreen',
        name: status,
      };
    case 'missed':
      return {
        color: 'text-missed',
        name: status,
      };
    case 'ongoing':
      return {
        color: 'text-primary',
        name: 'InProgress',
      };
    default:
      return {
        color: 'text-warning',
        name: status,
      };
  }
};

function getCount(sessionReport, status, mode = '') {
  const scheduleList = sessionReport
    .get('scheduleList', List())
    .filter((item) => item.get('status', '') === 'pending')
    .filter((item) => (mode !== '' ? item.get('mode', '') === mode : item))
    .filter((item) =>
      status === 'not-started' && item.get('isActive', false) === true
        ? item
        : item.get('isActive', false) === true
    )
    .filter((item) => {
      const scheduleStartDate = Date.parse(item.get('scheduleStartDateAndTime', new Date()));
      const scheduleEndDate = Date.parse(item.get('scheduleEndDateAndTime', new Date()));
      const utcDate = Date.parse(moment(new Date()).utc().format());
      return status === 'not-started'
        ? scheduleStartDate < utcDate && utcDate < scheduleEndDate
        : utcDate < scheduleStartDate;
    });
  return scheduleList.size;
}

export function getCurricularDelivery(sessionReport) {
  return [
    {
      name: 'Not Started',
      count: getCount(sessionReport, 'not-started'),
      color: 'text-warning',
    },
    {
      name: 'Missed',
      count: sessionReport.get('missedSchedule', 0),
      color: 'text-missed',
    },
    {
      name: 'Cancelled',
      count: sessionReport.get('cancelledSchedule', 0),
      color: 'text-red',
    },
  ];
}

export function getAllDelivery(sessionReport) {
  return [
    {
      name: 'Completed',
      count: sessionReport.get('completedSchedule', 0),
      color: 'text-darkGreen',
    },
    {
      name: 'InProgress',
      count: sessionReport.get('ongoingSchedule', 0),
      color: 'text-primary',
    },
    {
      name: 'Not Started',
      count: getCount(sessionReport, 'not-started'),
      color: 'text-warning',
    },
    {
      name: 'Missed',
      count: sessionReport.get('missedSchedule', 0),
      color: 'text-missed',
    },
    {
      name: 'Cancelled',
      count: sessionReport.get('cancelledSchedule', 0),
      color: 'text-red',
    },

    {
      name: 'Upcoming',
      count: getCount(sessionReport, 'upcoming'),
      color: 'text-dark',
    },
  ];
}

const deliveryLabels = [
  {
    value: 'Completed',
    color: '#16A34A',
    countName: 'completedSchedule',
  },
  {
    value: 'InProgress',
    color: '#4F46E5',
    countName: 'ongoingSchedule',
  },
  {
    value: 'Not Started',
    color: '#F59E0B',
    countName: 'not-started',
  },
  {
    value: 'Missed',
    color: '#850000',
    countName: 'missedSchedule',
  },
  {
    value: 'Cancelled',
    color: '#EF4444',
    countName: 'cancelledSchedule',
  },
  {
    value: 'Upcoming',
    color: '#374151',
    countName: 'upcoming',
  },
];

function getDeliveryData() {
  return deliveryLabels.map((item) => {
    return {
      value: item.value,
      textStyle: {
        color: item.color,
      },
    };
  });
}

function getDeliveryOptionData(sessionReport) {
  return deliveryLabels.map((item) => {
    return {
      value: ['not-started', 'upcoming'].includes(item.countName)
        ? getCount(sessionReport, item.countName)
        : sessionReport.get(item.countName, 0),
      itemStyle: {
        color: item.color,
      },
    };
  });
}

function getLateAttendanceCount(sessionReport, lateStarted) {
  const scheduleList = sessionReport.get('scheduleList', List());
  const getStatus = (type) => {
    return scheduleList
      .filter((item) => item.get('status', '') === type)
      .filter((item) => {
        if (item.getIn(['sessionDetail', 'start_time'], '') !== '') {
          const lateStartCheckTime = moment(item.get('scheduleStartDateAndTime', new Date()))
            .utc()
            .add(lateStarted, 'minutes')
            .format();
          return (
            Date.parse(lateStartCheckTime) <
            Date.parse(item.getIn(['sessionDetail', 'start_time'], ''))
          );
        }
        return false;
      }).size;
  };
  const completedLate = getStatus('completed');
  const inProgressLate = getStatus('ongoing');
  return [
    {
      value: completedLate,
      itemStyle: {
        color: '#e144ef',
      },
    },
    {
      value: inProgressLate,
      itemStyle: {
        color: '#e144ef',
      },
    },
  ];
}

function getEarlyAttendanceCount(sessionReport, earlyEnded) {
  const scheduleList = sessionReport.get('scheduleList', List());
  const getStatus = (type) => {
    return scheduleList
      .filter((item) => item.get('status', '') === type)
      .filter((item) => {
        if (
          item.getIn(['sessionDetail', 'stop_time'], '') !== '' &&
          item.get('status', '') === 'completed'
        ) {
          const earlyEndCheckTime = moment(item.get('scheduleEndDateAndTime', new Date()))
            .utc()
            .subtract(earlyEnded, 'minutes')
            .format();
          return (
            Date.parse(earlyEndCheckTime) >
            Date.parse(new Date(item.getIn(['sessionDetail', 'stop_time'], '')))
          );
        }
        return false;
      }).size;
  };
  const completedEarly = getStatus('completed');
  //const inProgressEarly = getStatus('ongoing');
  return [
    {
      value: completedEarly,
      itemStyle: {
        color: '#f50b89',
      },
    },
    // {
    //   value: inProgressEarly,
    //   itemStyle: {
    //     color: '#f50b89',
    //   },
    // },
  ];
}

function toolTipFormatter(params, hasLate, hasEarly, lateArray, earlyArray) {
  const name = params.name;
  if (name === 'Completed' || name === 'InProgress') {
    const arrayPosition = name === 'Completed' ? 0 : 1;
    let remarks = '';
    if (
      (hasLate && lateArray?.[arrayPosition]?.value !== undefined) ||
      (hasEarly && earlyArray?.[arrayPosition]?.value !== undefined)
    ) {
      remarks = `<br/>Remarks <br/>`;
    }
    let lateString = '';
    if (hasLate && lateArray?.[arrayPosition]?.value !== undefined) {
      lateString = `<span style="color:${lateArray?.[arrayPosition]?.itemStyle?.color}"> Late Started : <b>${lateArray?.[arrayPosition]?.value}</b></span><br/>`;
    }
    let earlyString = '';
    if (hasEarly && earlyArray?.[arrayPosition]?.value !== undefined) {
      earlyString = `<span style="color:${earlyArray?.[arrayPosition]?.itemStyle?.color}"> Early Ended : <b>${earlyArray?.[arrayPosition]?.value}</b></span>`;
    }
    return `<span style="color:${params.color}"> ${name} : <b>${params.value}</b></span>${remarks}${lateString}${earlyString}`;
  } else {
    return `<span style="color:${params.color}">${params.name} : <b>${params.value}</b></span>`;
  }
}

export function getDeliveryOption(sessionReport, lateStarted, earlyEnded, sessionStatus) {
  const lateArray = sessionStatus.includes('late')
    ? getLateAttendanceCount(sessionReport, lateStarted)
    : [];
  const earlyArray = sessionStatus.includes('early')
    ? getEarlyAttendanceCount(sessionReport, earlyEnded)
    : [];
  const hasLate = sessionStatus.includes('late') && lateArray.length > 0;
  const hasEarly = sessionStatus.includes('early') && earlyArray.length > 0;
  let seriesArray = [
    {
      name: '',
      barGap: 0,
      barWidth: '20%',
      label: {
        normal: {
          show: true,
          position: 'top',
        },
      },
      tooltip: {
        formatter: (params) => {
          return toolTipFormatter(params, hasLate, hasEarly, lateArray, earlyArray);
        },
      },
      data: getDeliveryOptionData(sessionReport),
      type: 'bar',
    },
  ];
  if (hasLate) {
    seriesArray = [
      ...seriesArray,
      {
        name: 'Late Started',
        barGap: 0,
        type: 'bar',
        barWidth: '20%',
        data: lateArray,
        tooltip: {
          formatter: (params) => {
            return `<span style="color:${params.color}">${params.seriesName} : <b>${params.value}</b></span>`;
          },
        },
      },
    ];
  }
  if (hasEarly) {
    seriesArray = [
      ...seriesArray,
      {
        name: 'Early Ended',
        barGap: 0,
        type: 'bar',
        barWidth: '20%',
        data: earlyArray,
        tooltip: {
          formatter: (params) => {
            return `<span style="color:${params.color}">${params.seriesName} : <b>${params.value}</b></span>`;
          },
        },
      },
    ];
  }
  return {
    title: {
      text: '',
      subtext: 'No. of Session',
    },
    tooltip: {
      trigger: 'item',
      axisPointer: {
        type: 'shadow',
      },
      // formatter: '{b} : {c}',
    },
    xAxis: {
      type: 'category',
      data: getDeliveryData(),
    },
    yAxis: {
      type: 'value',
    },
    // series: [
    //   {
    //     barWidth: '20%',
    //     label: {
    //       normal: {
    //         show: true,
    //         position: 'top',
    //       },
    //     },
    //     data: getDeliveryOptionData(sessionReport),
    //     type: 'bar',
    //   },
    // ],
    series: seriesArray,
  };
}

const getGraphData = (name, sessionReport) => {
  const array = sessionReport
    .get('sessionMode', List())
    .map((data) => data.get(name, ''))
    .toJS();
  return array;
};

function getModeCount(mode, sessionReport) {
  const array = sessionReport
    .get('sessionMode', List())
    .filter((item) => item.get('mode', '') === mode);
  return array.getIn([0, 'totalSchedule'], 0);
}

function getModeData(sessionReport) {
  return [
    `Remote   ${getModeCount('remote', sessionReport)}`,
    `Onsite   ${getModeCount('onsite', sessionReport)}`,
  ];
}

function getModeSeries(sessionReport) {
  return deliveryLabels.map((item) => {
    return {
      name: item.value,
      type: 'bar',
      stack: 'total',
      barWidth: '20%',
      emphasis: {
        focus: 'series',
      },
      itemStyle: {
        color: item.color,
      },
      data: ['not-started', 'upcoming'].includes(item.countName)
        ? [
            getCount(sessionReport, item.countName, 'remote'),
            getCount(sessionReport, item.countName, 'onsite'),
          ]
        : getGraphData(item.countName, sessionReport),
    };
  });
}

export function getModeOption(sessionReport) {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        // Use axis to trigger tooltip
        type: 'shadow', // 'shadow' as default; can also be 'line' or 'shadow'
      },
    },
    legend: {},
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      show: false,
    },
    yAxis: {
      type: 'category',
      data: getModeData(sessionReport),
    },
    series: getModeSeries(sessionReport),
  };
}

export function get60MinTime() {
  let timing = [];
  let i = 5;
  while (i <= 60) {
    timing.push({
      name: `${i === 5 ? '05' : i} Mins`,
      value: i,
    });
    i = i + 5;
  }
  return timing;
}

export function getStatus() {
  return [
    {
      label: 'Completed',
      name: 'completed',
      checked: false,
    },
    {
      label: 'Not Started',
      name: 'notstarted',
      checked: false,
    },
    {
      label: 'InProgress',
      name: 'inprogress',
      checked: false,
    },
    {
      label: 'Upcoming',
      name: 'upcoming',
      checked: false,
    },
    {
      label: 'Missed',
      name: 'missed',
      checked: false,
    },
    {
      label: 'Cancelled',
      name: 'cancelled',
      checked: false,
    },
    {
      label: 'Merged',
      name: 'merged',
      checked: false,
    },
    {
      label: 'Late Started',
      name: 'late',
      checked: false,
    },
    {
      label: 'Early Ended',
      name: 'early',
      checked: false,
    },
  ];
}

export const convertTime12to24 = (time12h) => {
  const [time, modifier] = time12h.split(' ');
  let [hours, minutes] = time.split(':');
  if (hours === '12') {
    hours = '00';
  }
  if (modifier === 'PM') {
    hours = parseInt(hours, 10) + 12;
  }
  return `${hours}:${minutes}`;
};

export function getNotStartedUpcoming(item) {
  if (item.get('status', '') === 'pending') {
    const scheduleStartDate = Date.parse(item.get('scheduleStartDateAndTime', new Date()));
    const scheduleEndDate = Date.parse(item.get('scheduleEndDateAndTime', new Date()));
    const utcDate = Date.parse(moment(new Date()).utc().format());
    return scheduleStartDate < utcDate && utcDate < scheduleEndDate ? 'not-started' : 'upcoming';
  } else {
    return item.get('status', '');
  }
}

export function getNonStartedSchedule(item) {
  const scheduleStartDate = Date.parse(item.get('scheduleStartDateAndTime', new Date()));
  const scheduleEndDate = Date.parse(item.get('scheduleEndDateAndTime', new Date()));
  const utcDate = Date.parse(moment(new Date()).utc().format());
  return scheduleStartDate < utcDate && utcDate < scheduleEndDate;
}

export const getInfraName = (item) => {
  if (item.get('scheduleStartFrom', '') === 'web') return 'Onsite (Web)';
  if (item.get('classModeType', '') === 'offline') return 'Onsite (Offline)';
  return item.get('mode', '');
};

export const extensionColors = {
  pdf: 'red',
  doc: 'blue',
  docx: 'blue',
  xls: 'green',
  xlsx: 'green',
  ppt: 'orange',
  pptx: 'orange',
  txt: 'gray',
  csv: 'purple',
  exe: 'black',
  default: 'black',
};
