import React, { Suspense } from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import LocalStorageService from 'LocalStorageService';
//import { getSiteUrl } from 'utils';

const LandingModal = React.lazy(() => import('../Modules/Dashboard/modal/landing'));

function withLandingHocHooks(Component) {
  const InjectedCurrentCalendar = function (props) {
    const { loggedInUserData, history } = props;
    // const siteName = LocalStorageService.getCustomCookie('site-id-name');
    // useEffect(() => {
    //   if (siteName !== '' && siteName === 'dc') {
    //     const siteURL = getSiteUrl();
    //     const siteOrigin = LocalStorageService.getCustomCookie('site-origin');
    //     window.location = siteURL.DC_URL === undefined ? siteOrigin : siteURL.DC_URL;
    //   }
    // }, [siteName]);
    const hasLandedAlready = LocalStorageService.getCustomCookie('landed') === 'false';
    return (
      <React.Fragment>
        {!loggedInUserData.isEmpty() && hasLandedAlready ? (
          <Suspense fallback="">
            <LandingModal history={history} callBack={() => {}} />
          </Suspense>
        ) : (
          ''
        )}
        <Component {...props} />
      </React.Fragment>
    );
  };

  InjectedCurrentCalendar.propTypes = {
    loggedInUserData: PropTypes.instanceOf(Map),
    history: PropTypes.object,
  };

  return InjectedCurrentCalendar;
}

export default withLandingHocHooks;
