import React, { useState } from 'react';
import PropTypes from 'prop-types';
import MButton from 'Widgets/FormElements/material/Button';
import { Menu, MenuItem, IconButton } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { useHistory } from 'react-router-dom';
import { List, Map } from 'immutable';
import { CheckPermission } from 'Modules/Shared/Permissions';
import { getURLParams, indVerRename } from 'utils';

const RegulationsList = ({ showSettingsModal, data, handleView }) => {
  const history = useHistory();
  const programName = getURLParams('pname', true);
  const programId = getURLParams('pid', true);

  const MenuOptions = ({ details }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const handleMenuClose = () => setAnchorEl(null);

    return (
      <div>
        <IconButton
          aria-label="more"
          aria-controls="long-menu"
          aria-haspopup="true"
          onClick={(e) => setAnchorEl(e.currentTarget)}
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          id="long-menu"
          anchorEl={anchorEl}
          keepMounted
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          PaperProps={{
            style: {
              maxHeight: 48 * 4.5,
              width: '20ch',
            },
          }}
        >
          {CheckPermission(
            'tabs',
            'Attainment Calculator',
            'Attainment Setting',
            '',
            'Regulations',
            'Edit'
          ) && <MenuItem onClick={() => showSettingsModal(details)}>Edit</MenuItem>}
          {CheckPermission(
            'tabs',
            'Attainment Calculator',
            'Attainment Setting',
            '',
            'Regulations',
            'Delete'
          ) && <MenuItem onClick={() => showSettingsModal(details, true)}>Delete</MenuItem>}
        </Menu>
      </div>
    );
  };
  MenuOptions.propTypes = {
    details: PropTypes.instanceOf(Map),
  };

  return (
    <div className="course_master mb-5">
      <div className="d-flex justify-content-between mb-3 align-items-center">
        <h6 className="pl-2 bold f-20">
          <span className="f-18">
            <i
              className="fa fa-arrow-left pr-3 remove_hover"
              aria-hidden="true"
              onClick={() => history.goBack()}
            ></i>
          </span>
          {programName} Program / Regulations
        </h6>
        {CheckPermission(
          'tabs',
          'Attainment Calculator',
          'Attainment Setting',
          '',
          'Regulations',
          'Add'
        ) && (
          <MButton variant="contained" color="primary" clicked={() => showSettingsModal(Map())}>
            Create New Regulation
          </MButton>
        )}
      </div>

      <div className="program_table">
        <table align="left">
          <thead>
            <tr>
              <th>Regulation Name</th>
              <th>Root Node</th>
              {CheckPermission(
                'tabs',
                'Attainment Calculator',
                'Attainment Setting',
                '',
                'Regulations',
                'View'
              ) && <th>Action</th>}
              <th></th>
            </tr>
          </thead>

          <tbody>
            {data.map((attainment, index) => (
              <tr key={index} className="tr-change">
                <td className="text-capitalize">
                  {attainment.get('regulationName', '')} - {attainment.get('regulationYear', '')}{' '}
                  {attainment.get('curriculumName', '')}
                </td>
                <td className="d-flex pb-3">
                  {attainment.get('outcomes', List()).map((item) => (
                    <div key={item} className="digi-node-bg">
                      {indVerRename(item, programId)}
                    </div>
                  ))}
                </td>
                {CheckPermission(
                  'tabs',
                  'Attainment Calculator',
                  'Attainment Setting',
                  '',
                  'Regulations',
                  'View'
                ) && (
                  <td>
                    <span
                      className="remove_hover f-15 bold AssessmentActive"
                      onClick={() => handleView(attainment.get('_id', ''))}
                    >
                      View
                    </span>
                  </td>
                )}

                {(CheckPermission(
                  'tabs',
                  'Attainment Calculator',
                  'Attainment Setting',
                  '',
                  'Regulations',
                  'Edit'
                ) ||
                  CheckPermission(
                    'tabs',
                    'Attainment Calculator',
                    'Attainment Setting',
                    '',
                    'Regulations',
                    'Delete'
                  )) && (
                  <td>
                    <MenuOptions details={attainment} />
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

RegulationsList.propTypes = {
  showSettingsModal: PropTypes.func,
  data: PropTypes.instanceOf(List),
  handleView: PropTypes.func,
};

export default RegulationsList;
