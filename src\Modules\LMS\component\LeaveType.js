import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import { Col, Row } from 'react-bootstrap';

// utils
import MaterialInput from 'Widgets/FormElements/material/Input';

// utils
import { useStylesFunction } from '../designUtils';

// components
import HexColorPicker from './HexColorPicker';
import { FormControl, Radio, RadioGroup } from '@mui/material';

const LeaveType = ({
  typeDetails,
  setPermissionTypes,
  permissionTypes,
  activeIndex,
  isPopup,
  disabled,
  typeDetailsRef = {},
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [leaveTypeDetails, setLeaveTypeDetails] = useState(Map({ attendanceType: 'absent' }));
  const { MarginBottom, checkedDisabled } = useStylesFunction();

  useEffect(() => {
    typeDetailsRef.current = permissionTypes.set(activeIndex, leaveTypeDetails);
  });

  useEffect(() => {
    if (typeDetails && typeDetails.size > 0) {
      const updatedTypeDetails = typeDetails.get('attendanceType', Map())
        ? typeDetails
        : typeDetails.set('attendanceType', 'absent');
      setLeaveTypeDetails(updatedTypeDetails);
    } else {
      setLeaveTypeDetails(Map({ attendanceType: 'absent' }));
    }
  }, [typeDetails]);

  const handleClick = (event) => setAnchorEl(event.currentTarget);

  const handleClose = () => setAnchorEl(null);

  const handleChange = (e, type) => {
    const { value, checked } = e.target;
    const checkedValues = ['isReasonFromApplicant', 'isProofToBeAttached'];
    if (type === 'allowedDuringExam')
      setLeaveTypeDetails((previousValue) =>
        previousValue
          .set('allowedDuringExam', checked)
          .set('isReasonFromApplicant', checked)
          .set('isProofToBeAttached', checked)
      );
    else
      setLeaveTypeDetails(
        leaveTypeDetails.set(type, checkedValues.includes(type) ? checked : value)
      );
  };

  const handleChangeColor = (e) => setLeaveTypeDetails(leaveTypeDetails.set('typeColor', e));

  return (
    <div>
      <Row>
        <Col lg={7}>
          <div className="mb-3">
            <div className="digi-light-gray mb-1">Type Name:</div>
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              placeholder={'Enter type name here'}
              variant={'outlined'}
              size={'small'}
              value={leaveTypeDetails.get('typeName', '')}
              changed={(e) => handleChange(e, 'typeName')}
              disabled={disabled}
            />
          </div>
        </Col>
      </Row>

      <Row>
        <Col lg={7}>
          <div className="digi-light-gray mb-1">Description (Optional):</div>
          <div>
            <MaterialInput
              bgWhite={isPopup}
              elementType={'materialTextArea'}
              placeholder={'Add your description here...'}
              name={'description'}
              minRows={'3'}
              value={leaveTypeDetails.get('typeDescription', '')}
              changed={(e) => handleChange(e, 'typeDescription')}
              disabled={disabled}
            />
          </div>
        </Col>
      </Row>

      <div className="mb-2 mt-2">
        <div className="text-nowrap digi-light-gray mb-1">Color:</div>
        <div
          className={`color-swatch ${isPopup ? '' : 'digi-pointer-events'}`}
          style={{ backgroundColor: leaveTypeDetails.get('typeColor', '') }}
          onClick={(e) => handleClick(e)}
        />
        <HexColorPicker
          anchorEl={anchorEl}
          onClose={handleClose}
          color={leaveTypeDetails.get('typeColor', '')}
          handleChange={handleChangeColor}
          index={0}
          type={'typeColor'}
          disabled={disabled}
        />
      </div>

      <div className="d-flex align-items-center hight-35">
        <div className="mr-2">
          <FormControlLabel
            className={`${MarginBottom}`}
            label={
              <div className={`${disabled ? 'digi-text-darkGrey' : ''}`}>Allow During Exams</div>
            }
            control={
              <Checkbox
                size="small"
                checked={leaveTypeDetails.get('allowedDuringExam', false)}
                onChange={(e) => handleChange(e, 'allowedDuringExam')}
                inputProps={{ 'aria-label': 'controlled' }}
                disabled={disabled}
                color="primary"
                className={disabled ? checkedDisabled : ''}
              />
            }
          />
        </div>
      </div>

      <div className="d-flex align-items-center hight-35">
        <div className="mr-2">
          <FormControlLabel
            className={`${MarginBottom}`}
            label={
              <div className={`${disabled ? 'digi-text-darkGrey' : ''}`}>
                Requires Reason from the Applicant
              </div>
            }
            control={
              <Checkbox
                size="small"
                checked={
                  leaveTypeDetails.get('allowedDuringExam', false)
                    ? leaveTypeDetails.get('allowedDuringExam', false)
                    : leaveTypeDetails.get('isReasonFromApplicant', false)
                }
                onChange={(e) =>
                  !leaveTypeDetails.get('allowedDuringExam', false) &&
                  handleChange(e, 'isReasonFromApplicant')
                }
                inputProps={{ 'aria-label': 'controlled' }}
                disabled={disabled}
                color="primary"
                className={
                  disabled || leaveTypeDetails.get('allowedDuringExam', false)
                    ? checkedDisabled
                    : ''
                }
              />
            }
          />
        </div>
      </div>

      <div className="d-flex align-items-center hight-35">
        <div className="mr-2">
          <FormControlLabel
            className={`${MarginBottom}`}
            label={
              <div className={`${disabled ? 'digi-text-darkGrey' : ''}`}>
                Requires Proof to be Attached
              </div>
            }
            control={
              <Checkbox
                size="small"
                checked={
                  leaveTypeDetails.get('allowedDuringExam', false)
                    ? leaveTypeDetails.get('allowedDuringExam', false)
                    : leaveTypeDetails.get('isProofToBeAttached', false)
                }
                onChange={(e) =>
                  !leaveTypeDetails.get('allowedDuringExam', false) &&
                  handleChange(e, 'isProofToBeAttached')
                }
                inputProps={{ 'aria-label': 'controlled' }}
                disabled={disabled}
                color="primary"
                className={
                  disabled || leaveTypeDetails.get('allowedDuringExam', false)
                    ? checkedDisabled
                    : ''
                }
              />
            }
          />
        </div>
      </div>

      <div className="mt-2">
        <div className="digi-light-gray d-flex align-items-center">
          <span className="mr-1">Attendance Type:</span>
        </div>
        <FormControl>
          <RadioGroup
            row
            aria-labelledby="demo-row-radio-buttons-group-label"
            name="row-radio-buttons-group"
            onChange={(e) => handleChange(e, 'attendanceType')}
            value={leaveTypeDetails.get('attendanceType', 'absent')}
          >
            <FormControlLabel
              value="absent"
              control={
                <Radio
                  size="small"
                  disabled={disabled}
                  color="primary"
                  className={
                    disabled || leaveTypeDetails.get('attendanceType', 'absent')
                      ? checkedDisabled
                      : ''
                  }
                />
              }
              label={<div className="f-18">Absent </div>}
            />
            <FormControlLabel
              value="present"
              control={
                <Radio
                  size="small"
                  disabled={disabled}
                  color="primary"
                  className={
                    disabled || leaveTypeDetails.get('attendanceType', 'absent')
                      ? checkedDisabled
                      : ''
                  }
                />
              }
              label={<div className="f-18">Present </div>}
            />
            <FormControlLabel
              value="exclude"
              control={
                <Radio
                  size="small"
                  disabled={disabled}
                  color="primary"
                  className={
                    disabled || leaveTypeDetails.get('attendanceType', 'absent')
                      ? checkedDisabled
                      : ''
                  }
                />
              }
              label={<div className="f-18">Exclude </div>}
            />
          </RadioGroup>
        </FormControl>
      </div>
    </div>
  );
};

LeaveType.propTypes = {
  typeDetails: PropTypes.instanceOf(Map),
  isPopup: PropTypes.bool,
  setPermissionTypes: PropTypes.func,
  changeTypeDetails: PropTypes.func,
  permissionTypes: PropTypes.instanceOf(List),
  activeIndex: PropTypes.number,
  disabled: PropTypes.bool,
  typeDetailsRef: PropTypes.object,
};

export default LeaveType;
