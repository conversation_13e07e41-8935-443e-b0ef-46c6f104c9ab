import React, { Fragment } from 'react';
import PropTypes, { oneOfType } from 'prop-types';

import { WeekWrapper, WeekRowWrapper } from '../Styled';

const CalenderWeeks = ({ data }) => {
  return (
    <Fragment>
      <WeekWrapper len={data.length}>
        <Fragment>
          {data.map((item, i) => {
            return (
              <WeekRowWrapper key={i}>
                <div className="pdfFont">{item['calender_week']}</div>
              </WeekRowWrapper>
            );
          })}
        </Fragment>
      </WeekWrapper>
    </Fragment>
  );
};

CalenderWeeks.propTypes = {
  data: oneOfType([PropTypes.array, PropTypes.object]),
};
export default CalenderWeeks;
