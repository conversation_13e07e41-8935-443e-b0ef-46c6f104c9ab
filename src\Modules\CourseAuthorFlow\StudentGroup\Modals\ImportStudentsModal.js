import React, { lazy, Suspense, useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Badge,
  Box,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
// import CloseIcon from '@mui/icons-material/Close';
import { fromJS, List, Map } from 'immutable';
import FileUpload from '../FileUpload';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { DeleteRow, useCallGroupSettings } from '../Dashboard';
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { formatToTwoDigitString } from 'utils';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';
import { useDispatch, useSelector } from 'react-redux';
import { Trans } from 'react-i18next';
import { selectInvalidEntries } from '_reduxapi/studentGroupV2/selectors';
import { setData } from '_reduxapi/studentGroupV2/action';
import InfoIcon from '@mui/icons-material/Info';
import SearchInput from '../SearchInput';
import MuiPagination from '../Pagination';
import FilterListIcon from '@mui/icons-material/FilterList';

const ImportConfirmModal = lazy(() => import('./ImportConfirmModal'));

const titleSx = {
  padding: '12px 16px',
  color: '#374151',
  borderTop: '4px solid #0064C8',
};
// const closeIconSx = {
//   position: 'absolute',
//   right: 8,
//   top: 11,
// };
// const menuProps = {
//   PaperProps: {
//     sx: {
//       maxHeight: 250,
//     },
//   },
// };
const checkboxSx = {
  padding: 0,
  marginRight: '8px',
  color: '#6B7280',
};
const buttonSx = {
  minWidth: 120,
  minHeight: 40,
};
const exportButtonSx = {
  padding: '4px 16px',
  color: '#0064C8',
};
const infoIconSx = {
  marginRight: '4px',
  fontSize: 16,
};
const templateButtonSx = {
  padding: 0,
  color: '#0064C8',
  '&:hover': {
    backgroundColor: 'transparent',
  },
};
const searchInputSx = {
  '& .MuiInputBase-root': {
    fontSize: 14,
    paddingRight: '8px',
  },
};
const badgeSx = {
  '& .MuiBadge-badge': {
    backgroundColor: '#EB5757',
    color: '#fff',
  },
};

const templateHeaders = [
  { header: 'Academic No', key: 'academicId' },
  { header: 'First Name', key: 'first_name' },
  { header: 'Middle Name', key: 'middle_name' },
  { header: 'Last Name', key: 'last_name' },
  { header: 'Mark', key: 'mark' },
  { header: 'Gender', key: 'gender' },
];
const headers = templateHeaders.map(({ header }) => header);

const genderOptions = [
  { label: 'All', value: '' },
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' },
];

const getFullName = (student) => {
  const firstName = student.get('first_name', '');
  const middleName = student.get('middle_name', '');
  const lastName = student.get('last_name', '');
  return [firstName, middleName, lastName].filter((item) => item).join(' ');
};

const updateEntries = (newObj, key, student, errorEntry) => {
  if (errorEntry) student = student.merge(errorEntry);
  return newObj.update(key, List(), (prev) => prev.push(student));
};

// const validateStudents = (students) => {
//   const academicIds = new Set();
//   return students.reduce((newObj, student) => {
//     const id = student.get('academicId', '');
//     if (academicIds.has(id))
//       return updateEntries(newObj, 'invalid', student, 'Duplicate academic id');
//     else academicIds.add(id);

//     const mark = Number(student.get('mark', 0));
//     if (mark < 1 || mark > 5)
//       return updateEntries(newObj, 'invalid', student, 'CGPA value should be between 1 and 5');

//     return updateEntries(newObj, 'valid', student);
//   }, Map());
// };

const getCourseWiseImportedList = (level, yearSettings, isYearWise = false) => {
  const levelNo = level.get('level_no', '');
  const yearCourseIds = yearSettings.getIn(['settings', 'courseIds'], List());
  const levelCourseIds = yearSettings.getIn(['levels', levelNo, 'courseIds'], List());
  let importedList = [];

  level.get('course', List()).forEach((course) => {
    if (!course.get('isImportedCourse')) return;
    if (course.get('courseNumberOfGroup')) return;

    const courseId = course.get('_course_id');
    const courseSettingId = yearSettings.getIn(['courses', `${levelNo}+${courseId}`, '_id']);
    if (isYearWise) {
      if (yearCourseIds.size && !yearCourseIds.includes(courseId)) return;
      if (levelCourseIds.includes(courseId)) return;
    } else if (levelCourseIds.size && !levelCourseIds.includes(courseId)) return;

    importedList.push({ id: courseSettingId, label: course.get('courses_number', '') });
  });

  return importedList;
};

export const getImportedList = (data, yearSettings) => {
  const selectedType = data.get('type', '');
  if (selectedType === 'course') return Map();

  if (selectedType === 'level') {
    const level = data.get('selectedLevel', Map());
    const importedList = getCourseWiseImportedList(level, yearSettings);
    return fromJS({ ...(importedList.length && { courses: importedList }) });
  }

  return data.get('level', List()).reduce((acc, level) => {
    const levelNo = level.get('level_no');
    const levelSettingId = yearSettings.getIn(['levels', levelNo, '_id']);
    if (level.get('isImportedLevel') && !level.get('levelNumberOfGroup')) {
      acc = acc.update('levels', List(), (prev) =>
        prev.push(Map({ id: levelSettingId, label: levelNo }))
      );
    }

    const importedList = getCourseWiseImportedList(level, yearSettings, true);
    if (importedList.length) {
      return acc.update('courses', List(), (prev) => {
        return importedList.reduce(
          (newList, item) => newList.push(Map({ ...item, label: `${levelNo} ${item.label}` })),
          prev
        );
      });
    }

    return acc;
  }, Map());
};

const StudentsTable = ({ students, updateStudents, paginationData, selectedCount }) => {
  const { pagination, handlePageChange, handleLimitChange } = paginationData;
  const pageNo = pagination.get('pageNo', '');
  const limit = pagination.get('limit', '');
  const totalStudents = students.size;
  const startIndex = (pageNo - 1) * limit;
  const paginatedStudents = students.slice(startIndex, startIndex + limit);

  useEffect(() => {
    if (totalStudents > 0 && !paginatedStudents.size) handlePageChange(null, pageNo - 1);
  }, [paginatedStudents]);

  const { isAllChecked, isIndeterminate } = useMemo(() => {
    const selectedCount = students.filter((student) => student.get('checked')).size;
    const isAllChecked = selectedCount === students.size;
    return {
      isAllChecked,
      isIndeterminate: selectedCount > 0 && !isAllChecked,
    };
  }, [students]);

  const handleCheckboxChange = (index, studentData) => (e) => {
    const value = index === 'all' ? e.target.checked : !studentData.get('checked');
    const studentIds = index === 'all' ? students.map((s) => s.get('rowIndex')) : List([index]);

    updateStudents((prev) =>
      prev.map((student) => {
        const rowIndex = student.get('rowIndex');
        if (!studentIds.includes(rowIndex)) return student;

        return student.set('checked', value);
      })
    );
  };

  const handleDelete = (e, index) => {
    e.stopPropagation();
    if (index === 'all') return updateStudents((prev) => prev.filter((s) => !s.get('checked')));
    updateStudents((prev) => prev.filter((s) => s.get('rowIndex') !== index));
  };

  return (
    <>
      {selectedCount > 0 && (
        <div className="mt-2">
          <DeleteRow count={selectedCount} handleDelete={(e) => handleDelete(e, 'all')} />
        </div>
      )}
      <div className="border-radious-8 mt-2">
        <div className="table-responsive border-radious-8 pb-2">
          <table className="table student-grouping-table import-table">
            <thead>
              <tr>
                <th>
                  <div className="d-flex align-items-center">
                    {paginatedStudents.size > 0 && (
                      <Checkbox
                        sx={checkboxSx}
                        checked={isAllChecked}
                        indeterminate={isIndeterminate}
                        onChange={handleCheckboxChange('all')}
                      />
                    )}
                    Academic ID
                  </div>
                </th>
                <th>Student Name</th>
                <th>Gender</th>
                <th>CGPA</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {paginatedStudents.size ? (
                paginatedStudents.map((student, index) => (
                  <tr
                    key={index}
                    className={`cursor-pointer ${student.get('checked') ? 'selected' : ''}`}
                    onClick={handleCheckboxChange(student.get('rowIndex'), student)}
                  >
                    <td>
                      <div className="d-flex align-items-center">
                        <Checkbox sx={checkboxSx} checked={student.get('checked', false)} />
                        {student.get('academicId', '')}
                      </div>
                    </td>
                    <td>{getFullName(student)}</td>
                    <td>{student.get('gender', '')}</td>
                    <td>{student.get('mark', '')}</td>
                    <td>
                      <div className="d-flex align-items-center justify-content-center">
                        <IconButton
                          sx={{ p: '2px' }}
                          className="text-light-grey"
                          onClick={(e) => handleDelete(e, student.get('rowIndex'))}
                        >
                          <DeleteOutlineIcon fontSize="small" />
                        </IconButton>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5}>
                    <p className="text-center">No data found</p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {totalStudents > 10 && (
          <div className="my-2 px-2 gray-neutral">
            <MuiPagination
              totalItems={totalStudents}
              limit={limit}
              page={pageNo}
              handleLimitChange={handleLimitChange}
              handlePageChange={handlePageChange}
              sx={{ fontSize: 14 }}
            />
          </div>
        )}
      </div>
    </>
  );
};
StudentsTable.propTypes = {
  students: PropTypes.instanceOf(List),
  updateStudents: PropTypes.func,
  paginationData: PropTypes.object,
  selectedCount: PropTypes.number,
};

const InvalidEntriesTable = ({ students }) => {
  return (
    <div className="border-radious-8 mt-2">
      <div className="table-responsive border-radious-8 pb-2">
        <table className="table student-grouping-table import-table">
          <thead>
            <tr>
              <th>S.No</th>
              <th>Academic ID</th>
              <th>Student Name</th>
              <th>CGPA</th>
              <th>Error Message</th>
            </tr>
          </thead>
          <tbody>
            {students.map((student, index) => (
              <tr key={index}>
                <td>{index + 1}</td>
                <td>{student.get('academicId', '')}</td>
                <td>{getFullName(student)}</td>
                <td>{student.get('mark', '')}</td>
                <td>{student.get('message', '')}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
InvalidEntriesTable.propTypes = {
  students: PropTypes.instanceOf(List),
};

const FilterDropdown = ({ selected, handleChange }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (e) => setAnchorEl(e.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const handleMenuClick = (val) => {
    handleChange(val);
    handleClose();
  };

  return (
    <div className="ml-2">
      <Badge variant="dot" invisible={selected === ''} sx={badgeSx}>
        <IconButton className="border rounded" onClick={handleClick}>
          <FilterListIcon fontSize="small" />
        </IconButton>
      </Badge>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{ style: { width: '125px' } }}
        keepMounted
      >
        {genderOptions.map(({ label, value }, index) => (
          <MenuItem
            key={index}
            selected={value === selected}
            onClick={() => handleMenuClick(value)}
          >
            {label}
          </MenuItem>
        ))}
      </Menu>
    </div>
  );
};
FilterDropdown.propTypes = {
  selected: PropTypes.string,
  handleChange: PropTypes.func,
};

const ImportStudentsModal = ({ open, data, handleClose, handleSave, hasSettings }) => {
  const dispatch = useDispatch();
  const activeInstitutionCalendar = useSelector(selectActiveInstitutionCalendar);
  const invalidData = useSelector(selectInvalidEntries);
  const { yearSettings, groupSettings, callGroupSettings } = useCallGroupSettings({
    selectedData: data,
  });
  const [show, setShow] = useState(open);
  // const [level, setLevel] = useState('');
  const [filename, setFilename] = useState('');
  const [students, setStudents] = useState(List());
  const [validatedEntries, setValidatedEntries] = useState(Map());
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isConfirmed, setIsConfirmed] = useState(false);
  const [filters, setFilters] = useState(Map());
  const [pagination, setPagination] = useState(Map());
  const programId = data.get('programId', '');
  const invalidEntries = validatedEntries.get('invalid', List());

  const clearInvalidEntries = () => {
    dispatch(setData(fromJS({ invalidEntries: [] })));
  };

  useEffect(() => {
    if (hasSettings && groupSettings.isEmpty()) callGroupSettings();
    return () => clearInvalidEntries();
  }, []);

  useEffect(() => {
    if (!students.size) {
      setFilename('');
      resetFilters();
    }
  }, [students]);

  useEffect(() => {
    if (!students.size || !invalidData.size) return;

    const errorEntries = invalidData.reduce((acc, student) => {
      if (!Map.isMap(student)) return acc;
      return acc.set(student.get('user_id', ''), student);
    }, Map());

    const academicIds = new Set();
    const validatedStudents = students.reduce((acc, student) => {
      const id = student.get('academicId', '').toString();
      if (
        errorEntries.has(id) &&
        (!errorEntries.getIn([id, 'isDuplicate'], false) || academicIds.has(id))
      )
        return updateEntries(acc, 'invalid', student, errorEntries.get(id));

      academicIds.add(id);
      return updateEntries(acc, 'valid', student);
    }, Map());

    setValidatedEntries(validatedStudents);
  }, [students, invalidData]);

  const importedList = useMemo(() => getImportedList(data, yearSettings), [data, yearSettings]);

  // const levelOptions = useMemo(() => {
  //   if (data.get('type') === 'level') return List();
  //   return data.get('level', List()).map((lvl) => {
  //     return Map({
  //       name: levelRename(lvl.get('level_no', ''), programId),
  //       value: lvl.get('level_no', ''),
  //     });
  //   });
  // }, []);

  const { maleCount, femaleCount } = useMemo(() => {
    let maleCount = 0;
    let femaleCount = 0;
    students.forEach((student) => {
      const gender = student.get('gender', '').toLowerCase();
      if (gender === 'male') maleCount += 1;
      else femaleCount += 1;
    });
    return { maleCount, femaleCount };
  }, [students]);

  const selectedCount = useMemo(() => {
    return students.filter((student) => student.get('checked')).size;
  }, [students]);

  const filteredStudents = useMemo(() => {
    const searchKey = filters.get('search', '').toLowerCase();
    const selectedGender = filters.get('gender', '');
    if (!searchKey && !selectedGender) return students;

    return students.filter((s) => {
      const academicId = s.get('academicId', '').toString().toLowerCase();
      const name = getFullName(s).toLowerCase();
      const gender = s.get('gender', '').toLowerCase();
      return (
        (!searchKey || academicId.includes(searchKey) || name.includes(searchKey)) &&
        (!selectedGender || gender === selectedGender)
      );
    });
  }, [students, filters]);

  const sortedStudents = useMemo(() => {
    return filteredStudents.sort((a, b) => {
      // Sort checked first
      return b.get('checked', false) - a.get('checked', false);
    });
  }, [filteredStudents]);

  const { duplicates, unregistered, otherErrors } = useMemo(() => {
    let duplicates = 0;
    let unregistered = 0;
    let otherErrors = 0;

    invalidEntries.forEach((student) => {
      if (student.get('isDuplicate')) duplicates += 1;
      else if (student.get('unRegister')) unregistered += 1;
      else otherErrors += 1;
    });

    return { duplicates, unregistered, otherErrors };
  }, [invalidEntries]);

  const resetFilters = () => {
    setFilters(Map());
    setPagination(Map({ pageNo: 1, limit: 10 }));
  };

  const isDisabled = () => {
    return validatedEntries.size
      ? validatedEntries.get('valid', List()).isEmpty()
      : students.isEmpty();
  };

  const downloadTemplate = () => {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('import-students');
    worksheet.columns = templateHeaders.map((item) => ({ ...item, width: 20 }));
    workbook.xlsx
      .writeBuffer()
      .then((buffer) => saveAs(new Blob([buffer]), 'import-students-template.xlsx'));
  };

  const setImportedData = ({ filename, data }) => {
    const importedData = data.reduce((newArr, row, rowIndex) => {
      let rowObject = Map({ rowIndex });
      row.forEach((value, index) => {
        rowObject = rowObject.set(templateHeaders[index].key, value);
      });
      return newArr.push(rowObject);
    }, List());
    setFilename(filename);
    setStudents(importedData);
    resetFilters();
  };

  const handleFilters = (key) => (value) => {
    setFilters((prev) => prev.set(key, value));
    setPagination((prev) => prev.set('pageNo', 1));
  };

  const handlePageChange = (_, pageNo) => {
    setPagination((prev) => prev.set('pageNo', pageNo));
  };

  const handleLimitChange = (e) => {
    const limit = parseInt(e.target.value, 10);
    setPagination(Map({ pageNo: 1, limit }));
  };

  const exportInvalidEntries = () => {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('invalid-entries');
    const headers = [...templateHeaders, { header: 'Message', key: 'message' }];
    worksheet.columns = headers.map((item) => ({ ...item, width: 20 }));
    worksheet.addRows(invalidEntries.toJS());
    workbook.xlsx
      .writeBuffer()
      .then((buffer) => saveAs(new Blob([buffer]), 'invalid-entries.xlsx'));
  };

  const hasDuplicateEntries = () => {
    return invalidEntries.some((item) => item.get('isDuplicate'));
  };

  const handleClickSave = (confirm = false) => {
    if (importedList.size && !isConfirmed && !confirm) {
      setShow(false);
      return setShowConfirmModal(true);
    }
    if (confirm) {
      setIsConfirmed(true);
      setShowConfirmModal(false);
      setShow(true);
    }

    const studentsList = validatedEntries.size ? validatedEntries.get('valid', List()) : students;
    // const validatedStudents = validateStudents(studentsList);
    // if (validatedStudents.get('invalid', List()).size)
    //   return setValidatedEntries(validatedStudents);

    const groupSettingId = groupSettings.get('_id', '');
    const selectedType = data.get('type');
    const level = data.getIn(['selectedLevel', 'level_no']);
    const courseId = data.getIn(['selectedCourse', '_course_id']);
    const levelSettingIds = importedList.get('levels', List()).map((level) => level.get('id'));
    const courseSettingIds = importedList.get('courses', List()).map((course) => course.get('id'));
    const removedImportedGroupingIds = levelSettingIds.merge(courseSettingIds);
    const requestBody = Map({
      ...(groupSettingId
        ? { groupSettingId }
        : {
            institutionCalendarId: activeInstitutionCalendar.get('_id'),
            programId,
            term: data.get('term'),
          }),
      selectedType,
      year: data.get('year'),
      ...(level && { level }),
      ...(courseId && { courseIds: List([courseId]) }),
      importedStudents: studentsList.map((student) => {
        return Map({
          academicId: student.get('academicId', '').toString(),
          mark: student.get('mark', '').toString(),
        });
      }),
      ...(removedImportedGroupingIds.size && { removedImportedGroupingIds }),
    });

    handleSave(requestBody, handleClose);
  };

  const handleClickBack = () => {
    setValidatedEntries(Map());
    clearInvalidEntries();
  };

  return (
    <>
      <Dialog open={show} maxWidth={validatedEntries.isEmpty() ? 'sm' : 'md'} fullWidth>
        <DialogTitle className="border-bottom" sx={titleSx}>
          Import Students
        </DialogTitle>
        {/* <IconButton aria-label="close" onClick={handleClose} sx={closeIconSx}>
          <CloseIcon />
        </IconButton> */}
        <DialogContent className="p-3">
          {/* {data.get('type') === 'year' && (
          <div className="mb-4">
            <MaterialInput
              elementType="MuiSelect"
              size="small"
              labelclass="mb-1 f-14 text-gray"
              label={
                <>
                  {levelRename('Level', programId)}
                  <span className="text-danger">*</span>
                </>
              }
              placeholder="- Select -"
              elementConfig={levelOptions}
              value={level}
              changed={(e) => setLevel(e.target.value)}
              MenuProps={menuProps}
              displayEmpty
            />
          </div>
        )} */}
          {validatedEntries.size ? (
            <div className="f-14 gray-neutral">
              <p className="f-15 mb-3">
                {duplicates > 0 && (
                  <>
                    <span>Duplicates: {formatToTwoDigitString(duplicates)}</span>
                    <span className="mx-1">•</span>
                  </>
                )}
                {unregistered > 0 && (
                  <>
                    <span>Unregistered: {formatToTwoDigitString(unregistered)}</span>
                    <span className="mx-1">•</span>
                  </>
                )}
                {otherErrors > 0 && (
                  <>
                    <span>Others: {formatToTwoDigitString(otherErrors)}</span>
                    <span className="mx-1">•</span>
                  </>
                )}
                <span>
                  To be imported:{' '}
                  {formatToTwoDigitString(validatedEntries.get('valid', List()).size)}
                </span>
              </p>
              <p className="f-16">
                <Trans i18nKey="data_check" />
              </p>
              <div className="d-flex align-items-center justify-content-between mb-2">
                <p>
                  <Trans i18nKey="list_error_entity" />
                </p>
                <MButton variant="text" sx={exportButtonSx} clicked={exportInvalidEntries}>
                  <Trans i18nKey="export_entry" />
                </MButton>
              </div>
              <InvalidEntriesTable students={invalidEntries} />
              <div className="f-14 text-red mt-1">
                <div className="d-flex align-items-center">
                  <InfoIcon sx={infoIconSx} />
                  <Trans i18nKey="student_grouping.entities_error" />
                </div>
                {hasDuplicateEntries() && (
                  <div className="d-flex align-items-center">
                    <InfoIcon sx={infoIconSx} />
                    <Trans i18nKey="student_grouping.duplicates_import" />
                  </div>
                )}
              </div>
            </div>
          ) : (
            <>
              <p className="f-14 mb-1">
                Download a template here to upload:{' '}
                <MButton variant="text" sx={templateButtonSx} onClick={downloadTemplate}>
                  Download Template
                </MButton>
              </p>
              <FileUpload
                filename={filename}
                templateHeaders={headers}
                setImportedData={setImportedData}
              />
              {students.size > 0 && (
                <>
                  <div className="d-flex align-items-center justify-content-between text-uppercase f-14 gray-neutral mt-4">
                    <p>Imported students ({formatToTwoDigitString(students.size)})</p>
                    <p>
                      Male: {maleCount}, Female: {femaleCount}
                    </p>
                  </div>
                  <div className="d-flex align-items-center justify-content-end mt-2">
                    <Box sx={{ paddingTop: '5px' }}>
                      <SearchInput
                        placeholder="Search student"
                        value={filters.get('search', '')}
                        changed={handleFilters('search')}
                        sx={searchInputSx}
                      />
                    </Box>
                    <FilterDropdown
                      selected={filters.get('gender', '')}
                      handleChange={handleFilters('gender')}
                    />
                  </div>
                  <StudentsTable
                    students={sortedStudents}
                    updateStudents={setStudents}
                    paginationData={{ pagination, handlePageChange, handleLimitChange }}
                    selectedCount={selectedCount}
                  />
                </>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions className="p-3 border-top">
          {!validatedEntries.isEmpty() && (
            <MButton variant="text" color="primary" clicked={handleClickBack}>
              Back
            </MButton>
          )}
          <div className="ml-auto">
            <MButton variant="outlined" color="gray" clicked={handleClose} sx={buttonSx}>
              Cancel
            </MButton>
            <MButton
              variant="contained"
              color="primary"
              className="ml-2"
              clicked={() => handleClickSave()}
              disabled={isDisabled()}
              sx={buttonSx}
            >
              {validatedEntries.isEmpty() ? 'Save' : 'Continue'}
            </MButton>
          </div>
        </DialogActions>
      </Dialog>

      {showConfirmModal && (
        <Suspense fallback="">
          <ImportConfirmModal
            open
            data={data}
            importedList={importedList}
            handleClose={handleClose}
            handleConfirm={() => handleClickSave(true)}
          />
        </Suspense>
      )}
    </>
  );
};

ImportStudentsModal.propTypes = {
  open: PropTypes.bool,
  data: PropTypes.instanceOf(Map),
  handleClose: PropTypes.func,
  handleSave: PropTypes.func,
  hasSettings: PropTypes.bool,
};

export default ImportStudentsModal;
