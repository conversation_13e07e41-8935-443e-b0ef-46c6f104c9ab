const reportsAndAnalyticsState = (state) => state.reportsAndAnalytics;

const selectLoading = (state) => reportsAndAnalyticsState(state).get('loading');
const selectMessage = (state) => reportsAndAnalyticsState(state).get('message');
const selectDashboard = (state) => reportsAndAnalyticsState(state).get('dashboard');
const selectAcademicYearLevel = (state) => reportsAndAnalyticsState(state).get('academicYearLevel');
const selectAllStudentDetails = (state) => reportsAndAnalyticsState(state).get('allStudentDetails');
const selectStudentDetails = (state) => reportsAndAnalyticsState(state).get('studentDetails');
const selectDeptSubOverview = (state) => reportsAndAnalyticsState(state).get('deptSubOverview');
const selectDeptSubOverviewCourse = (state) =>
  reportsAndAnalyticsState(state).get('deptSubOverviewCourse');
const selectAllStaffDetails = (state) => reportsAndAnalyticsState(state).get('allStaffDetails');
const selectStaffDetails = (state) => reportsAndAnalyticsState(state).get('staffDetails');
const selectCourseOverviewData = (state) => reportsAndAnalyticsState(state).get('courseOverview');
const selectCourseSessionStatusData = (state) =>
  reportsAndAnalyticsState(state).get('courseSessionStatus');
const selectCourseStudentDetails = (state) =>
  reportsAndAnalyticsState(state).get('courseStudentDetails');
const selectCourseStaffDetails = (state) =>
  reportsAndAnalyticsState(state).get('courseStaffDetails');
const selectUserAttendanceReport = (state) =>
  reportsAndAnalyticsState(state).get('userAttendanceReport');
const selectAttendanceLog = (state) => reportsAndAnalyticsState(state).get('attendanceLog');
const selectActivityInfo = (state) => reportsAndAnalyticsState(state).get('activityInfo');
const selectActivityThemeData = (state) => reportsAndAnalyticsState(state).get('activityThemeData');
const selectCurriculumData = (state) => reportsAndAnalyticsState(state).get('curriculumData');
const selectReviewQuizList = (state) => reportsAndAnalyticsState(state).get('reviewQuizList');
const selectActivitiesData = (state) => reportsAndAnalyticsState(state).get(`activitiesData`);
const selectStudentActivityData = (state) =>
  reportsAndAnalyticsState(state).get('studentActivityData');
const selectSloListData = (state) => reportsAndAnalyticsState(state).get('sloListData');
const selectPloListData = (state) => reportsAndAnalyticsState(state).get('ploListData');
const selectFrameworkData = (state) => reportsAndAnalyticsState(state).get('frameworkData');
const selectDashboardProgramList = (state) =>
  reportsAndAnalyticsState(state).get('dashboardProgramList');
const selectDashboardPlo = (state) => reportsAndAnalyticsState(state).get('dashboardPlo');
const selectDashboardCreditHours = (state) =>
  reportsAndAnalyticsState(state).get('dashboardCreditHours');
const selectDashboardStudent = (state) => reportsAndAnalyticsState(state).get('dashboardStudent');
const selectDashboardStaff = (state) => reportsAndAnalyticsState(state).get('dashboardStaff');
const selectCourseDetail = (state) => reportsAndAnalyticsState(state).get('courseDetail');
const selectDashboardLoader = (state) => reportsAndAnalyticsState(state).get('dashboardLoader');
const selectSummary = (state) => reportsAndAnalyticsState(state).get('summary');
const selectAcademicReportYearLevel = (state) =>
  reportsAndAnalyticsState(state).get('academicReportYearLevel');

export {
  selectLoading,
  selectMessage,
  selectDashboard,
  selectAcademicYearLevel,
  selectAllStudentDetails,
  selectStudentDetails,
  selectDeptSubOverview,
  selectDeptSubOverviewCourse,
  selectAllStaffDetails,
  selectStaffDetails,
  selectCourseOverviewData,
  selectCourseSessionStatusData,
  selectCourseStudentDetails,
  selectCourseStaffDetails,
  selectUserAttendanceReport,
  selectAttendanceLog,
  selectActivityInfo,
  selectActivityThemeData,
  selectCurriculumData,
  selectReviewQuizList,
  selectActivitiesData,
  selectStudentActivityData,
  selectSloListData,
  selectPloListData,
  selectFrameworkData,
  selectDashboardProgramList,
  selectDashboardPlo,
  selectDashboardCreditHours,
  selectDashboardStudent,
  selectDashboardStaff,
  selectCourseDetail,
  selectDashboardLoader,
  selectSummary,
  selectAcademicReportYearLevel,
};
