import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import Loader from '../../Widgets/Loader/Loader';
import { Button } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
//import Input from '../../Widgets/FormElements/Input/Input';
import './review.css';
import axios from '../../axios';
import moment from 'moment';
import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import { formatFullName } from '../../utils';

class Review extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      // reviewlist: [],
      // createrName: '',
    };
  }

  onChangeReply = (option, replyindex, index) => {
    const reviewlist = this.props.reviewlist;
    reviewlist[index].replyRadio = replyindex;
    this.setState({
      reviewlist,
    });
  };

  handleChange = (e, index) => {
    const reviewlist = [...this.props.reviewlist];
    reviewlist[index].createrReply = e.target.value;
    this.setState({
      reviewlist,
    });
  };

  viceDeanSubmit = (e, index) => {
    const reviewlist = this.props.reviewlist[index];
    const data = {
      _calendar_id: this.props.id,
      reviewer_type: 'creator',
      _reviewer_id: reviewlist._reviewer_id,
      review: reviewlist.replyRadio === 0 ? true : false,
      review_comment: reviewlist.createrReply,
    };

    axios
      .post(`program_calendar_review/add_reviewer_review`, data)
      .then((res) => {
        NotificationManager.success(`Comment Sent Successfully`);
        this.setState(
          {
            isLoading: false,
          },
          () => {
            this.props.refresh();
          }
        );
      })
      .catch((ex) => {});
  };

  render() {
    const items = [{ to: '#', label: 'Program Calendar > Review' }];
    return (
      <React.Fragment>
        <Loader isLoading={this.state.isLoading} />

        <Breadcrumb>
          {items &&
            items.map(({ to, label }, index) => (
              <Link
                className="breadcrumb-icon"
                style={{ color: '#fff', marginTop: '-68px' }}
                key={index}
                to={to}
              >
                {label}
              </Link>
            ))}
        </Breadcrumb>
        <div className="main pt-3 pb-5 bg-white">
          <Loader isLoading={this.state.isLoading} />
          <div className="container-fluid">
            {this.props.deanDetail.status !== 'waiting' && (
              <div className="row mt-1 pb-3">
                <div className="col-md-6 pt-2 pb-2">
                  <h4 className="f-16 pt-4">Dean Responses</h4>
                  <React.Fragment>
                    <div className="pb-3">
                      <div className="reviewer-status">
                        <div className="row">
                          <div className="col-md-8">
                            <p className=" mb-0">
                              {this.props.deanDetail.name !== undefined && (
                                <React.Fragment>
                                  Dr. {formatFullName(this.props.deanDetail.name)}
                                </React.Fragment>
                              )}
                              <small>
                                {/* {moment(this.props.deanDetail.timestamp).format(
                                  "MMMM Do YYYY, h:mm:ss a"
                                )}{" "} */}
                              </small>
                            </p>
                          </div>
                          {this.props.deanDetail.status === 'pending' && (
                            <div className="col-md-4 ">
                              <div className="float-right">
                                <p className="text-right mb-0">Pending</p>
                              </div>
                            </div>
                          )}
                          <div className="col-md-4 ">
                            <div className="float-right">
                              {this.props.deanDetail.review === true && (
                                <Button variant="outline-success border_cuve" size="sm">
                                  Approve
                                </Button>
                              )}
                              {this.props.deanDetail.review === false && (
                                <Button variant="outline-danger border_cuve" size="sm">
                                  Disapprove
                                </Button>
                              )}
                            </div>
                          </div>

                          <div className="col-md-10">
                            <small>{this.props.deanDetail.comment}</small>
                          </div>
                        </div>
                        {this.props.deanDetail.review === false && (
                          <div
                            style={{
                              display: 'flex',
                              flexDirection: 'row-reverse',
                            }}
                          >
                            <Button
                              variant="primary"
                              onClick={() => {
                                this.props.backURL();
                              }}
                            >
                              Edit Calendar
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </React.Fragment>
                </div>
              </div>
            )}

            {this.props.reviewlist.length > 0 && (
              <div className="row mt-1 pb-3">
                <div className="col-md-6 pt-2 pb-2">
                  <h4 className="f-16 pt-4"> Responses</h4>

                  {this.props.reviewlist.map((data, index) => {
                    return (
                      <React.Fragment key={index}>
                        {data.status === 'Pending' && (
                          <div className="pb-3">
                            <div className="reviewer-status">
                              <div className="row">
                                <div className="col-md-8">
                                  <p className=" mb-0">{data.reviewerName}</p>
                                </div>

                                <div className="col-md-4 ">
                                  <div className="float-right">
                                    <p className="text-right mb-0">Pending</p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        {data.status === 'Reviewed' && (
                          <div className="reply_comment mb-4">
                            <div className="row">
                              <div className="col-md-9">
                                <p className="mb-2">
                                  {data.reviewerName}{' '}
                                  <small>
                                    - {moment(data.reviewedTime).format('MMMM Do YYYY, h:mm:ss a')}
                                  </small>
                                </p>
                              </div>

                              <div className="col-md-3 ">
                                <div className="float-right">
                                  {data.reviews === false && (
                                    <Button variant="outline-danger border_cuve" size="sm">
                                      No
                                    </Button>
                                  )}
                                  {data.reviews === true && (
                                    <Button variant="outline-success border_cuve" size="sm">
                                      Yes
                                    </Button>
                                  )}
                                </div>
                              </div>

                              {data.reviewerComment !== undefined && (
                                <div className="col-md-10 reviewer_comment">
                                  <small>{data.reviewerComment}</small>
                                </div>
                              )}

                              {data.reviews === false && (
                                <React.Fragment>
                                  <div className="col-md-12 pt-2">
                                    <p className="mb-1"> Reply</p>

                                    <div className="float-left pl-2">
                                      <span className="radio-label1">
                                        {data.reply.map((option, replyindex) => (
                                          <div key={replyindex}>
                                            <input
                                              type="radio"
                                              className={'form-control form-radio1'}
                                              value={option.name}
                                              checked={
                                                data.replyRadio === replyindex ? true : false
                                              }
                                              onChange={(e) =>
                                                this.onChangeReply(option, replyindex, index)
                                              }
                                            />
                                            <span className="radio-label1">{option.name}</span>
                                          </div>
                                        ))}
                                      </span>
                                    </div>

                                    <div className="pt-3">
                                      <textarea
                                        value={data.createrReply}
                                        onChange={(e) => this.handleChange(e, index)}
                                        maxLength="150"
                                        className={'form-control'}
                                        placeholder={'Add a comment'}
                                      />
                                    </div>
                                  </div>
                                  <div className="col-md-12 pt-3 ">
                                    <div className="float-right">
                                      <Button
                                        variant="outline-primary"
                                        onClick={(e) => this.viceDeanSubmit(e, index)}
                                      >
                                        Send{' '}
                                      </Button>
                                    </div>
                                  </div>
                                </React.Fragment>
                              )}
                            </div>
                          </div>
                        )}

                        {data.status === 'Done' && (
                          <div className="pb-3">
                            <div className="reviewer-status">
                              <div className="row">
                                <div className="col-md-8">
                                  <p className=" mb-0">
                                    {data.reviewerName}{' '}
                                    <small>
                                      -{' '}
                                      {moment(data.reviewedTime).format('MMMM Do YYYY, h:mm:ss a')}{' '}
                                    </small>
                                  </p>
                                </div>

                                <div className="col-md-4 ">
                                  <div className="float-right">
                                    {data.reviews === false && (
                                      <Button variant="outline-danger border_cuve" size="sm">
                                        No
                                      </Button>
                                    )}
                                    {data.reviews === true && (
                                      <Button variant="outline-success border_cuve" size="sm">
                                        Yes
                                      </Button>
                                    )}
                                  </div>
                                </div>

                                <div className="col-md-10">
                                  <small>{data.reviewerComment}</small>
                                </div>
                              </div>

                              <div className="row pt-3">
                                <div className="col-md-9 pl-4">
                                  <p className=" mb-0 side_quote">
                                    {this.props.createrName}{' '}
                                    <small>
                                      -{' '}
                                      {moment(data.creatertimestamp).format(
                                        'MMMM Do YYYY, h:mm:ss a'
                                      )}{' '}
                                    </small>
                                  </p>
                                  <p className=" mb-0 side_quote">
                                    <small>{data.createrComment}</small>
                                  </p>
                                </div>

                                <div className="col-md-3 ">
                                  <div className="float-right">
                                    {data.createrFeedback === false && (
                                      <Button variant="outline-danger border_cuve_normal" size="sm">
                                        Disagree
                                      </Button>
                                    )}
                                    {data.createrFeedback === true && (
                                      <Button
                                        variant="outline-success border_cuve_normal"
                                        size="sm"
                                      >
                                        Agree
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </React.Fragment>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </React.Fragment>
    );
  }
}

Review.propTypes = {
  reviewlist: PropTypes.array,
  refresh: PropTypes.func,
  history: PropTypes.object,
  deanDetail: PropTypes.object,
  id: PropTypes.string,
  backURL: PropTypes.string,
  createrName: PropTypes.string,
};

export default withRouter(Review);
