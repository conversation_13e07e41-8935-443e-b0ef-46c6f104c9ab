import React from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import { t } from 'i18next';

function CourseSessionStatusFilters({ options, filters, handleChange, resetLabelName = '' }) {
  function getOptions(type) {
    return options.get(type, List());
  }

  function handleFilterChange(name, value) {
    const updatedFilters = filters.merge(Map({ [name]: value }));
    handleChange(updatedFilters);
  }
  return (
    <div className="p-3 border-bottom border-top">
      <div className="row text-left">
        <div className="col-md-2 px-2">
          <div className="f-14">{t('reports_analytics.status').toUpperCase()}</div>
          <FormControl fullWidth variant="outlined" size="small">
            <Select
              native
              value={filters.get('status', '')}
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="">{resetLabelName}</option>
              {getOptions('statuses').map((option) => (
                <option key={option.get('value')} value={option.get('value')}>
                  {option.get('name')}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>
        <div className="col-md-2 px-2">
          <div className="f-14">{t('reports_analytics.subject').toUpperCase()}</div>
          <FormControl fullWidth variant="outlined" size="small">
            <Select
              native
              value={filters.get('subject', '')}
              onChange={(e) => handleFilterChange('subject', e.target.value)}
            >
              <option value="">{resetLabelName}</option>
              {getOptions('subjects').map((option) => (
                <option key={option.get('value')} value={option.get('value')}>
                  {option.get('name')}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>
        <div className="col-md-2 px-2">
          <div className="f-14">{t('reports_analytics.delivery_type').toUpperCase()}</div>
          <FormControl fullWidth variant="outlined" size="small">
            <Select
              native
              value={filters.get('deliveryType', '')}
              onChange={(e) => handleFilterChange('deliveryType', e.target.value)}
            >
              <option value="">{resetLabelName}</option>
              {getOptions('deliveryTypes').map((option) => (
                <option key={option.get('value')} value={option.get('value')}>
                  {option.get('name')}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>
        <div className="col-md-2 px-2">
          <div className="f-14">{t('reports_analytics.mode').toUpperCase()}</div>
          <FormControl fullWidth variant="outlined" size="small">
            <Select
              native
              value={filters.get('mode', '')}
              onChange={(e) => handleFilterChange('mode', e.target.value)}
            >
              <option value="">{resetLabelName}</option>
              {getOptions('modes').map((option) => (
                <option key={option.get('value')} value={option.get('value')}>
                  {option.get('name')}
                </option>
              ))}
            </Select>
          </FormControl>
        </div>
      </div>
    </div>
  );
}

CourseSessionStatusFilters.propTypes = {
  options: PropTypes.instanceOf(Map),
  filters: PropTypes.instanceOf(Map),
  handleChange: PropTypes.func,
  resetLabelName: PropTypes.string,
};

export default CourseSessionStatusFilters;
