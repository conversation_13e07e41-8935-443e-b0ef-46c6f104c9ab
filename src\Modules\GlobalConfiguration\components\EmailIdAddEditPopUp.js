import Checkbox from '@mui/material/Checkbox';
import { Modal } from 'react-bootstrap';
import React, { useState, useEffect } from 'react';
import MButton from 'Widgets/FormElements/material/Button';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import * as actions from '_reduxapi/global_configuration/actions';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { Map, fromJS } from 'immutable';
import Tooltips from '_components/UI/Tooltip/Tooltip';
import { BodyInputReuse, EditPopUpValidation } from './EmailIdAddEditPopUpComponents';
import i18n from '../../../i18n';

const EmailIdAddEditPopUp = (props) => {
  const {
    emailIdConfig,
    setData,
    handleClose,
    mailIsLoading = Map({}),
    settingId,
    open = false,
    emailIdConFiguration,
    sendingTestMail,
    id,
    header,
    openCreate = false,
    manageType,
  } = props;
  const [userName, setUserName] = useState('');
  const [fromEmail, setFromEmail] = useState('');
  const [toEmail, setToEmail] = useState('');
  const [passwd, setPasswd] = useState('');
  const [Re_passwd, setRe_Passwd] = useState('');
  const [name, setName] = useState('');
  const [smtpClient, setSmtpClient] = useState('');
  const [portNo, setPortNo] = useState('');
  const [tslChecked, setTslChecked] = useState(true);
  const type = emailIdConfig.size ? 'update' : 'create';

  const UpdateResponseData = () => {
    setUserName(emailIdConfig.get('userName', ''));
    setPasswd(emailIdConfig.get('password', ''));
    setRe_Passwd(emailIdConfig.get('password', ''));
    setName(emailIdConfig.get('displayName', ''));
    setSmtpClient(emailIdConfig.get('smtpClient', ''));
    setPortNo(emailIdConfig.get('portNumber', ''));
    setTslChecked(emailIdConfig.get('ttl_ssl', true));
    setToEmail(emailIdConfig.get('toEmail', ''));
    setFromEmail(emailIdConfig.get('fromEmail', ''));
  };

  useEffect(() => {
    UpdateResponseData();
  }, [emailIdConfig]); //eslint-disable-line

  const requestData = (ForValidation = false) => {
    const requestBody = {
      settingId: settingId,
      emailIdConfig: {
        displayName: name,
        fromEmail: fromEmail,
        toEmail: toEmail,
        ...(userName && { userName: userName }),
        ...(ForValidation && { bothPassword: { passwd: passwd, Re_passwd: Re_passwd } }),
        ...(ForValidation && { bothEmail: { fromEmail: fromEmail, toEmail: toEmail } }),
        password: passwd,
        reEnterPassword: Re_passwd,
        smtpClient: smtpClient,
        portNumber: parseInt(portNo),
        ttl_ssl: tslChecked,
      },
    };
    return requestBody;
  };

  const handleSubmit = (type) => {
    if (EditPopUpValidation([requestData(true)], setData)) {
      emailIdConFiguration({
        requestData: requestData(false),
        operation: type === 'create' ? 'create' : 'update',
        settingId,
        id,
        callBack: () => handleClose(UpdateResponseData),
        header,
        manageType,
      });
    }
  };

  const handleTestMail = () => {
    sendingTestMail(requestData(false));
  };
  const handleCancel = () => {
    handleClose(UpdateResponseData);
    setData(
      Map({
        isMailLoading: fromJS({
          loading: false,
          countValue: 0,
        }),
      })
    );
  };
  return (
    <Modal
      show={open['emailCreate'] || open['emailEdit'] || openCreate}
      dialogClassName="model-800"
      centered
      onHide={() => handleCancel()}
    >
      <Modal.Body>
        <div className="p-2">
          <div className="d-flex justify-content-between">
            <p className="f-20 mb-2 ">
              <Trans i18nKey={'global_configuration.email_configuration'}></Trans>
            </p>
          </div>

          <div className="row">
            <div className="col-sm-6 col-md-4 col-lg-6">
              {BodyInputReuse(
                name,
                i18n.t('global_configuration.display_name'),
                setName,
                <Trans i18nKey={'global_configuration.display_name'}></Trans>
              )}

              {BodyInputReuse(
                fromEmail,
                i18n.t('global_configuration.enter_email'),
                setFromEmail,
                i18n.t('global_configuration.From_Email')
              )}

              {BodyInputReuse(
                passwd,
                i18n.t('global_configuration.enter_password'),
                setPasswd,
                <Trans i18nKey={'password'}></Trans>,
                i18n.t('password')
              )}

              {BodyInputReuse(
                smtpClient,
                i18n.t('global_configuration.enter_smtp_client'),
                setSmtpClient,
                <Trans i18nKey={'global_configuration.smtp_client'}></Trans>
              )}
            </div>
            <div className="col-sm-6 col-md-4 col-lg-6">
              {BodyInputReuse(
                userName,
                i18n.t('global_configuration.enter_userName'),
                setUserName,
                <>
                  {' '}
                  <Trans i18nKey={'global_configuration.userName'}></Trans>{' '}
                  <span>
                    {' '}
                    <Tooltips
                      title={<Trans i18nKey={'global_configuration.userName_info'}></Trans>}
                    >
                      <i className="fa fa-info-circle pl-2 remove_hover"></i>
                    </Tooltips>
                  </span>
                </>
              )}

              {BodyInputReuse(
                toEmail,
                i18n.t('global_configuration.enter_to_email'),
                setToEmail,
                i18n.t('global_configuration.to_email')
              )}
              {BodyInputReuse(
                Re_passwd,
                i18n.t('global_configuration.re_enter_password'),
                setRe_Passwd,
                <Trans i18nKey={'global_configuration.re_enter_password'}></Trans>,
                i18n.t('password')
              )}
              {BodyInputReuse(
                portNo,
                i18n.t('global_configuration.enter_port_number'),
                setPortNo,
                <Trans i18nKey={'global_configuration.port_number'}></Trans>,
                i18n.t('number')
              )}
            </div>

            <FormGroup className="pl-3">
              <FormControlLabel
                control={
                  <Checkbox
                    value={tslChecked}
                    onChange={(e) => {
                      setTslChecked(e.target.checked);
                    }}
                    color="primary"
                  />
                }
                label="TLS/SSL"
              />
            </FormGroup>
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer className="border-none">
        <div className="col-md-6 col-xl-7 col-lg-6 col-6">
          {mailIsLoading?.get('loading', false) ? (
            <span className={`f-18 ${mailIsLoading?.get('countValue', 0) ? '' : 'pl-3'}`}>
              <Trans i18nKey={'sending_mail'}></Trans>
            </span>
          ) : mailIsLoading?.get('countValue', 0) ? (
            <>
              <i
                className="fa fa-check-circle tick f-22 ml-3 icon-pos text-darkGreen"
                aria-hidden="true"
              ></i>
              <span className="text-darkGreen f-18 pl-3">
                <Trans i18nKey={'mail_send'}></Trans>
              </span>
            </>
          ) : (
            ''
          )}
        </div>

        <MButton variant="outlined" color="gray" clicked={handleCancel}>
          <Trans i18nKey={'cancel'}></Trans>
        </MButton>
        <MButton variant="outlined" className="digi-blue-button" clicked={handleTestMail}>
          {mailIsLoading?.get('countValue', 0) ? (
            <Trans i18nKey={'test_again'}></Trans>
          ) : (
            <Trans i18nKey={'global_configuration.send_test_email'}></Trans>
          )}
        </MButton>

        <b className="pr-2">
          <MButton
            variant="contained"
            clicked={(e) => {
              handleSubmit(type);
            }}
            color="primary"
          >
            <Trans i18nKey={'save'}></Trans>
          </MButton>
        </b>
      </Modal.Footer>
    </Modal>
  );
};
EmailIdAddEditPopUp.propTypes = {
  emailIdConfig: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  handleClose: PropTypes.func,
  mailIsLoading: PropTypes.object,
  settingId: PropTypes.string,
  manageType: PropTypes.string,
  open: PropTypes.bool,
  emailIdConFiguration: PropTypes.func,
  sendingTestMail: PropTypes.func,
  id: PropTypes.string,
  header: PropTypes.object,
  openCreate: PropTypes.bool,
};
export default connect(null, actions)(EmailIdAddEditPopUp);
