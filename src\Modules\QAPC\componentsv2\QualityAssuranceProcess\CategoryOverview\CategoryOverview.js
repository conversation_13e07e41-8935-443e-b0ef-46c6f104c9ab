/* eslint-disable no-unused-vars */
import React, { useRef, useState } from 'react';
import Header, { GuideResource } from './Header';
import SectionWIseRendering from './SectionWIseRendering';
import { List, Map } from 'immutable';
import Tags from './Tags';
import CaptureOptionWise from '../DisplayCapture/CaptureOptionWIse';
import DisplayScreenModal from '../DisplayCapture/DisplayScreenModal';
import { selectSingleFormList } from '_reduxapi/q360/selectors';
import { useSelector } from 'react-redux';
import { useSearchParams } from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import IncorporateSection from './IncorporateSection/IncorporateSection';
import { dString } from 'utils';
import GlobalViewDocuments from './GlobalViewDocuments';
import ReferenceDocumentV2 from './ReferenceDocument/ReferenceDocumentV2';

export default function CategoryOverview() {
  const [showReferenceDoc, setShowReferenceDoc] = useState(false);
  const [searchParams] = useSearchParams();
  const show_reference_document = searchParams.get('reference_document') === 'true';
  const edit = searchParams.get('edit')?.trim() === 'true';
  const formInitiatorId = searchParams.get('formId');
  const categoryFormId = searchParams.get('categoryFormId');
  const show_incorporation_settings = searchParams.get('incorporation_settings') === 'true';
  const show_display_creation = searchParams.get('display_creation') === 'true';
  const [imageManager, setImageManager] = useState(
    Map({
      showSelectImageModal: false,
      uploadedImages: List(),
      currentSelectedImage: '',
      // uploadedImages => Schema:{
      //   sections:[], if template wise sections key should be empty
      //   displayName:"",
      //   image''
      // }
    })
  );
  // const [showDisplayCapturePopOver,setShowDisplayCapturePopOver]=useState(false) //we should open popOver in two different places
  // so we are maintaining in the state in parent component.
  const singleFormList = useSelector(selectSingleFormList);
  const formName = singleFormList.get('formName', '');
  const level = singleFormList.get('level', '');
  const categoryFormCourseId = singleFormList.get('categoryFormCourseId', '');
  const isUserPerformedActionWithDocuments = dString(searchParams.get('formAttachment')) === 'true';
  const categoryFormType = singleFormList.getIn(['categoryFormId', 'categoryFormType'], '');
  const isSectionWise = singleFormList.getIn(['categoryFormId', 'formType'], '') === 'section';
  const isTemplateBased = categoryFormType === 'template';
  const [showDisplayCapture, setShowDisplayCapture] = useState(false);
  const [selectParticularArea, setSelectParticularArea] = useState(false);

  const fetchChildComponentData = useRef(null);
  const fetchTagsRef = useRef(Map());
  const fetchIncorporateFromRef = useRef(List());
  const handleDocumentClick = () => {
    setShowReferenceDoc((prev) => !prev);
  };
  const [documents, setDocuments] = useState(Map());
  const fileInputRef = useRef();
  const [pdfDocuments, setPdfDocuments] = useState(Map());

  const handleDocType = (type) => (e) => {
    e.stopPropagation();
    if (type === 'pdf') {
      fileInputRef.current.click();
    }
  };

  const handleUserSelect = (image) => {
    setImageManager((prev) =>
      prev.set('showSelectImageModal', true).set('currentSelectedImage', image)
    );
    setShowDisplayCapture(false);
  };

  const pdfFunction = {
    handleDocType,
    fileInputRef,
    setPdfDocuments,
    pdfDocuments,
  };
  return (
    <div className="white_shade_2">
      <CaptureOptionWise
        onSelect={handleUserSelect}
        setSelectParticularArea={setSelectParticularArea}
        particularAreaSelect={selectParticularArea}
      >
        {(onDivClick) => {
          return (
            <>
              <Header
                show_reference_document={show_reference_document}
                formInitiatorId={formInitiatorId}
                isTemplateBased={isTemplateBased}
                documents={documents}
                handleUserSelect={handleUserSelect}
                setShowDisplayCapture={setShowDisplayCapture}
                showDisplayCapture={showDisplayCapture}
                onDivClick={onDivClick}
                setSelectParticularArea={setSelectParticularArea}
                ref={{ fetchChildComponentData, fetchTagsRef, fetchIncorporateFromRef }}
                handleDocumentClick={handleDocumentClick}
                showReferenceDoc={showReferenceDoc}
                edit={edit}
                pdfDocuments={pdfDocuments}
              />
              <Header.Step />
              <div className="px-4">
                <div className="grid_section_with_resource">
                  <GuideResource ref={fetchIncorporateFromRef} edit={edit} />
                  <GlobalViewDocuments
                    documents={documents}
                    setDocuments={setDocuments}
                    isUserPerformedActionWithDocuments={isUserPerformedActionWithDocuments}
                    ref={fetchChildComponentData}
                    edit={edit}
                    show_display_creation={show_display_creation}
                  />
                </div>
                {/* Fixed layout started here */}
                <div className="row mt-3">
                  <div className={`${showReferenceDoc ? 'col-8 set_reference_doc_height' : 'col'}`}>
                    <SectionWIseRendering
                      show_display_creation={show_display_creation}
                      categoryFormId={categoryFormId}
                      isSectionWise={isSectionWise}
                      formInitiatorId={formInitiatorId}
                      setShowReferenceDoc={setShowReferenceDoc}
                      ref={fetchChildComponentData}
                      documents={documents}
                      setDocuments={setDocuments}
                      isTemplateBased={isTemplateBased}
                      isUserPerformedActionWithDocuments={isUserPerformedActionWithDocuments}
                      showReferenceDoc={showReferenceDoc}
                      edit={edit}
                      level={level}
                      pdfFunction={pdfFunction}
                    />
                    <Tags
                      ref={fetchTagsRef}
                      categoryFormCourseId={categoryFormCourseId}
                      edit={!edit}
                      level={level}
                    />
                    {show_incorporation_settings && (
                      <IncorporateSection
                        formName={formName}
                        formInitiatorId={formInitiatorId}
                        isTemplateBased={isTemplateBased}
                        edit={edit}
                        documents={documents}
                      />
                    )}
                  </div>
                  {showReferenceDoc && (
                    <div className="col-4">
                      {/* <ReferenceDocumentV1 setShowReferenceDoc={setShowReferenceDoc} /> */}
                      <ReferenceDocumentV2 setShowReferenceDoc={setShowReferenceDoc} />
                    </div>
                  )}
                </div>
              </div>
            </>
          );
        }}
      </CaptureOptionWise>
      {imageManager.get('showSelectImageModal', false) && (
        <DisplayScreenModal
          isSectionWise={isSectionWise}
          isTemplateBased={isTemplateBased}
          setShowDisplayCapture={setShowDisplayCapture}
          setSelectParticularArea={setSelectParticularArea}
          showDisplayCapture={showDisplayCapture}
          setImageManager={setImageManager}
          imageManager={imageManager}
        />
      )}
    </div>
  );
}
