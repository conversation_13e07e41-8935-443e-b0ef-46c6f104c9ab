import React, { useState, useRef, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import config from 'config';

/*---------------------------UtilsStart-------------------------*/
const iframeStyle = {
  position: 'fixed',
  bottom: '1.8rem',
  left: '1rem',
  border: 'none',
  zIndex: 99999,
  backgroundColor: 'transparent',
};
/*---------------------------UtilsEnd---------------------------*/

/*---------------------------CustomHookStart-------------------------*/

const useHelpComp = () => {
  const [imageUrl, setImageUrl] = useState(null);
  const [resize, setResize] = useState(true);
  const iframeRef = useRef(null);
  const authData = useSelector(selectUserInfo);
  const { HELP_WIDGET_URL, CLIENT_NAME } = config;

  const iframeLink = `${HELP_WIDGET_URL}/help?client=${CLIENT_NAME}&clientType=${authData.get(
    'user_type',
    'LoinPage'
  )}`;

  const takeScreenShot = async () => {
    try {
      iframeRef.current.contentWindow.postMessage('readyToTakeScreenShot', HELP_WIDGET_URL);
      const stream = await navigator.mediaDevices.getDisplayMedia({
        preferCurrentTab: true,
      });
      const imageCapture = new ImageCapture(stream.getVideoTracks()[0]);
      const bitmap = await imageCapture.grabFrame();
      const canvas = document.createElement('canvas');
      canvas.width = bitmap.width;
      canvas.height = bitmap.height;
      const context = canvas.getContext('2d');
      context.drawImage(bitmap, 0, 0);
      const dataURL = canvas.toDataURL('image/png');
      setImageUrl(dataURL);
      await stream.getTracks().forEach((track) => track.stop());
      iframeRef.current.contentWindow.postMessage('readyImage', HELP_WIDGET_URL);
    } catch (err) {
      iframeRef.current.contentWindow.postMessage('failureToTakeImageOperation', HELP_WIDGET_URL);
    }
  };

  useEffect(() => {
    window.addEventListener('message', (e) => {
      if (e.origin !== HELP_WIDGET_URL) {
        return;
      }
      if (e.data === 'captureScreenShot') {
        takeScreenShot();
      }
      if (typeof e.data.iframe === 'boolean') {
        setResize(e.data.iframe);
      }
    });
  }, []); //eslint-disable-line

  useEffect(() => {
    if (imageUrl) {
      if (!iframeRef.current) return;
      iframeRef.current.contentWindow.postMessage({ image: imageUrl }, HELP_WIDGET_URL);
    }
  }, [imageUrl]); //eslint-disable-line

  return [iframeRef, resize, iframeLink];
};

/*---------------------------CustomHookEnd---------------------------*/

const HelpWidget = () => {
  const [iframeRef, resize, iframeLink] = useHelpComp();

  return (
    <iframe
      ref={iframeRef}
      title="Embedded Page"
      src={iframeLink}
      width={resize ? '98px' : '351px'}
      height={resize ? '48px' : '652px'}
      scrolling="no"
      style={iframeStyle}
    ></iframe>
  );
};

export default HelpWidget;
