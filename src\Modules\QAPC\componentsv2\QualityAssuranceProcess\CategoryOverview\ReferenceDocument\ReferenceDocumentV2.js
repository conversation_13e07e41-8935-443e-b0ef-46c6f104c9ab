import React, { useEffect, useRef, useState } from 'react';
import {
  useCallApiHook,
  useDebounce,
  useInputHook,
  useIsReduxEmpty,
  useSearchParams,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import {
  getReferDocAcademicYear as getAcademic,
  getReferenceFormAttachment,
  searchReferenceDocument,
  setData,
  getSearchDocumentList,
} from '_reduxapi/q360/actions';
import {
  selectReferenceDocumentDetails as DetailsList,
  selectReferenceFromAttachments,
  selectSearchProgramDetails,
} from '_reduxapi/q360/selectors';
import { fromJS, List, Map } from 'immutable';
import { useSelector } from 'react-redux';
import document_with_outline from 'Assets/q360_dashboard/category_overview/document_with_outline.svg';
import UserGlobalIcon from 'Assets/UserGlobalIcon.svg';
import FormControl from '@mui/material/FormControl';
import AspectRatioIcon from '@mui/icons-material/AspectRatio';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CloseIcon from '@mui/icons-material/Close';
import {
  Autocomplete,
  Box,
  Button,
  // CardHeader,
  Drawer,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { CloseOutlined } from '@mui/icons-material';
// import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CkEditor5 from 'Widgets/CkEditor/CkEditor';
import CategoryFormSelectOption from './CategoryFormSelectOption';
// import { Card } from 'react-bootstrap';
import { PUBLIC_PATH } from 'constants';
import { getShortString } from 'Modules/Shared/v2/Configurations';
/*--------------------------UtilsStart-------------------------------*/

const muiPaperSx = { '& .MuiDrawer-paper': { overflow: 'visible', width: '78vw' } };

const textFieldSX = {
  borderRadius: '4px',
  backgroundColor: '#F9FAFB',
  '& .MuiOutlinedInput-notchedOutline': {
    borderColor: '#E5E7EB',
  },
  '& .MuiInputBase-input::placeholder': {
    color: '#9CA3AF',
    fontSize: '14px',
  },
};
/*--------------------------UtilsEnd---------------------------------*/

/*--------------------------CustomHooksStart---------------------------------*/

const useDispatchData = ({ level }) => {
  const [referenceDetails] = useIsReduxEmpty(DetailsList, getAcademic, { level });
  const [getFromAttachment] = useCallApiHook(getReferenceFormAttachment);
  const [updateMessage] = useCallApiHook(setData);
  return [referenceDetails, getFromAttachment, updateMessage];
};

const useSelectorData = () => {
  const programDetails = useSelector(selectSearchProgramDetails);
  const referenceAttachment = useSelector(selectReferenceFromAttachments);
  return [programDetails, referenceAttachment];
};

const useSearchProgramDetails = ({
  formInitiatedIds = List(),
  setPage,
  category,
  updateMessage,
  level,
  callBack,
}) => {
  const [selectProgram, setSelectProgram] = useState('');
  const [search, handleSearchChange] = useInputHook();
  const [searchReference] = useCallApiHook(searchReferenceDocument);
  const paramsDetails = { searchKey: search, formInitiatedIds, formLevel: level };
  const selectedParams = { searchKey: selectProgram, formInitiatedIds };
  useDebounce(getSearchDocumentList, search, true, paramsDetails, callBack);
  const searchDocumentsByUserAction = () => {
    searchReference(selectedParams);
    setPage('ListOutTemplate');
  };
  const handleChange = (_, newValue) => setSelectProgram(newValue);
  const onChangeSearch = (event) => {
    if (category.get('categoryFormId', '')) {
      handleSearchChange(event);
    } else {
      const query = event.target.value;
      if (query === '') return;
      updateMessage(Map({ message: 'Please Choose Category From' }));
    }
  };
  const handleSearch = (e) => {
    onChangeSearch(e);
    setSelectProgram(e.target.value);
  };
  return {
    search,
    onChangeSearch,
    searchDocumentsByUserAction,
    handleChange,
    handleSearch,
    selectProgram,
  };
};

const useAcademicYears = ({ updateMessage }) => {
  const [academicYear, setAcademicYear] = useState(Map());
  const [category, setCategory] = useState(
    Map({
      categoryId: '',
      categoryFormId: '',
      formName: '',
      formInitiatedIds: List(),
    })
  );
  const handleChangeAcademicYear = (event) => {
    setAcademicYear((prev) => prev.set('value', event.target.value).set('name', event.target.name));
    setCategory(Map());
  };
  const handleChangeCategory = (updatedCategory) => {
    if (academicYear.size) {
      setCategory(updatedCategory);
    } else {
      updateMessage(Map({ message: 'Please Choose Academic Calender Year' }));
    }
  };
  return [academicYear, category, handleChangeAcademicYear, handleChangeCategory];
};

const useAccordionState = ({ getFromAttachment, setDisplay, referenceAttachment }) => {
  const [expanded, handleChangeAccordion] = useState(0);
  const handleFoundForms = (formInitiatorId, index, callBackParam) => {
    const data = referenceAttachment.get(formInitiatorId, Map());
    const callBack = () => {
      setDisplay((prev) => prev.setIn([index, 'referenceAttachment'], data));
      callBackParam && callBackParam();
    };
    handleChangeAccordion((previous) => (previous === index + 1 ? 0 : index + 1));
    if (data.size === 0) {
      getFromAttachment({ formInitiatorId, callBack });
    } else {
      callBack();
    }
  };
  return [expanded, handleFoundForms, handleChangeAccordion];
};

/*--------------------------CustomHooksEnd---------------------------------*/

/*--------------------------ConstructFunctionStart---------------------------------*/
const academicYearsAndCategory = ({ referenceDetails }) => {
  let academicYears = List();
  let categoryDetails = Map();
  referenceDetails.forEach((details) => {
    const category = details.get('category', List());
    const institutionCalenderId = details.get('institutionCalenderId', '');
    academicYears = academicYears.push(
      Map({
        value: institutionCalenderId,
        name: details.get('calendar_name', ''),
      })
    );
    category.forEach((details) => {
      const categoryId = details.get('categoryId', '');
      const forms = details.get('forms', List());
      const categoryName = details.get('categoryName', '');
      categoryDetails = categoryDetails.setIn(
        [institutionCalenderId, categoryId],
        fromJS({ categoryName, forms, categoryId })
      );
    });
  });
  return [academicYears, categoryDetails];
};

const constructProgramDetails = ({ programDetails }) => {
  let programOptions = List();
  let courseOptions = List();
  programDetails.forEach((details) => {
    const _id = details.get('_id', '');

    const programName = details.get('programName', '');
    const courseName = details.get('courseName', '');
    programOptions = programOptions.push(
      Map({ id: _id, label: programName, formName: details.get('formName', '') })
    );
    courseOptions = courseOptions.push(
      Map({ id: _id, label: courseName, formName: details.get('formName', '') })
    );
  });
  return [programOptions, courseOptions];
};
/*--------------------------ConstructFunctionEnd---------------------------------*/

const CustomIconButton = ({ onClick, children }) => {
  const customStyles = {
    height: '24px',
    width: '24px',
    '&:hover': {
      backgroundColor: '#fecaca',
      color: '#ef4444',
    },
  };

  return (
    <IconButton onClick={onClick} sx={customStyles}>
      {children}
    </IconButton>
  );
};

export const SearchTextField = ({
  placeholder,
  onChange,
  value,
  disabled = false,
  isClearIcon = false,
  onClear,
}) => {
  const endAdornment =
    isClearIcon && value ? (
      <CustomIconButton onClick={onClear}>
        <CloseIcon className="f-18 cursor-pointer" />
      </CustomIconButton>
    ) : null;

  const InputProps = {
    startAdornment,
    endAdornment,
    sx: textFieldSX,
  };
  return (
    <TextField
      fullWidth
      size="small"
      placeholder={placeholder}
      InputProps={InputProps}
      onChange={onChange}
      value={value}
      disabled={disabled}
    />
  );
};
const startAdornment = (
  <InputAdornment position="start">
    <SearchIcon className="forum_gray" />
  </InputAdornment>
);

const ConditionRender = ({ thisType, setPage, display, setDisplay, setShowReferenceDoc }) => {
  const [listOptions, setListOptions] = useState(List());
  const [searchParams] = useSearchParams();
  const level = searchParams.get('level');
  const [referenceDetails, getFromAttachment, updateMessage] = useDispatchData({ level });
  const [programDetails, referenceAttachment] = useSelectorData();
  const [
    academicYear,
    category,
    handleChangeAcademicYear,
    handleChangeCategory,
  ] = useAcademicYears({ updateMessage });
  const [academicYears, categoryDetails] = academicYearsAndCategory({ referenceDetails });
  const [programOptions, courseOptions] = constructProgramDetails({ programDetails });
  const callBack = (data) => setListOptions(fromJS(data));
  const {
    search,
    onChangeSearch: handleSearchInputChange,
    searchDocumentsByUserAction,
    handleChange,
    selectProgram,
    handleSearch,
  } = useSearchProgramDetails({
    formInitiatedIds: category.get('formInitiatedIds', List()).toJS(),
    getFromAttachment,
    setPage,
    category,
    updateMessage,
    level,
    callBack,
  });
  const [expanded, handleFoundForms, handleChangeAccordion] = useAccordionState({
    getFromAttachment,
    setDisplay,
    referenceAttachment,
  });
  const isProgramOrCourseOrInstitution = level === 'course' ? courseOptions : programOptions;
  const [drawer, setDrawer] = useState(false);
  const compounds = {
    SearchReferencePage,
    ExpandedDrawer,
    ListOutTemplate,
    IndividualFormView,
  };
  const props = {
    handleChange,
    listOptions,
    isProgramOrCourseOrInstitution,
    handleSearchInputChange,
    searchDocumentsByUserAction,
    search,
    academicYears,
    categoryDetails,
    academicYear,
    category,
    handleChangeAcademicYear,
    handleChangeCategory,
    referenceAttachment,
    setShowReferenceDoc,
    handleChangeAccordion,
    programDetails,
    selectProgram,
    handleSearch,
  };
  const ComponentToRender = compounds[thisType];
  return thisType ? (
    <ComponentToRender
      setPage={setPage}
      open={drawer}
      programDetails={programDetails}
      setDrawer={setDrawer}
      display={display}
      setDisplay={setDisplay}
      referenceAttachment={referenceAttachment}
      props={props}
      expanded={expanded}
      handleFoundForms={handleFoundForms}
    />
  ) : null;
};

export const IframeForm = ({ doc = Map() }) => {
  const form = doc.get('formTemplate', Map());
  const iframeRef = useRef(null);
  const sendMessageToIframe = () => {
    const iframe = iframeRef.current;
    if (iframe) {
      const iframeWindow = iframe.contentWindow;
      iframe.style.height = iframeWindow.document.body.scrollHeight + 10 + 'px';
      iframeRef.current.contentWindow.postMessage(
        { values: form.set('isReadOnly', true).toJS(), from: 'fromDC' },
        '*'
      );
    }
  };
  const handleIframeLoad = () => sendMessageToIframe();
  return (
    <iframe
      ref={iframeRef}
      className="m-3"
      src={PUBLIC_PATH + '/course-spec/course_spec.html'}
      title="Face Anomaly Report"
      width="100%"
      style={{ border: 'none', overflow: 'auto' }}
      onLoad={handleIframeLoad}
      id="myIframe"
    ></iframe>
  );
};

const ShowCkEditor = ({ doc }) => {
  const sections = doc.get('sectionAttachments', List());
  const formName = doc.getIn(['categoryFormId', 'formName'], '');
  return (
    <div>
      {sections.map((data, index) => {
        return (
          <div key={index} className="my-2 p-2">
            <TextField
              value={formName}
              id="outlined-basic"
              label="Section Name"
              variant="outlined"
              size="small"
              fullWidth
            />
            <div className="mt-1">
              <CkEditor5
                data={data.get('description', '')}
                key={index}
                onChange={() => {}}
                isReadOnly={true}
              />
            </div>
          </div>
        );
      })}
      {sections.size === 0 && <div className="m-3">No Forms Available !</div>}
    </div>
  );
};

const SectionShowCkEditor = ({ doc }) => {
  const sections = doc.get('sectionAttachments', List());
  return (
    <div>
      {sections.map((data, index) => {
        const description = data.get('description', '');
        const sectionName = data.get('sectionName', '');
        return (
          <div key={index} className="my-2 p-2">
            <TextField
              value={sectionName}
              id="outlined-basic"
              label="Section name"
              variant="outlined"
              size="small"
              fullWidth
            />
            <div className="mt-1">
              <CkEditor5 data={description} isReadOnly={true} />
            </div>
          </div>
        );
      })}
      {sections.size === 0 && <div className="m-3">No Forms Available !</div>}
    </div>
  );
};

const ListOutTemplate = ({
  props,
  programDetails,
  expanded,
  handleFoundForms,
  setPage,
  setDisplay,
  setDrawer,
}) => {
  const {
    isProgramOrCourseOrInstitution,
    academicYear,
    academicYears,
    category,
    handleChangeAccordion,
  } = props;
  const selectedId = academicYear.get('value', '');
  const selectedAcademicYearName = academicYears.find(
    (data) => data.get('value', '') === selectedId
  );
  // const formName = category.get('formName', '');
  const categoryName = category.get('categoryName', '');
  const academicYearLabel = selectedAcademicYearName.get('name', '');
  const goBack = () => {
    setPage('SearchReferencePage');
    handleChangeAccordion(0);
  };
  const openIndividualFormView = (index, formInitiatorId, name) => {
    const callBack = () => {
      setPage('IndividualFormView');
      setDrawer(true);
      setDisplay((prev) =>
        prev.set('clickedIndex', index).set('id', formInitiatorId).set('name', name)
      );
    };
    handleFoundForms(formInitiatorId, index, callBack);
  };
  useEffect(() => {
    let updatedDisplay = Map();
    isProgramOrCourseOrInstitution.map((level, index) => {
      const name = level.get('label', '');
      updatedDisplay = updatedDisplay.set(
        index,
        Map({ formName: level.get('formName', ''), categoryName, academicYearLabel, name })
      );
    });
    setDisplay(updatedDisplay);
  }, [programDetails]);

  return (
    <Box className="height_580px bg-white box-shadow-static rounded pb-3 sticky_header">
      <div className="bottom_border_grey p-3 d-flex align-items-center justify-content-between">
        <div className="d-flex align-items-center">
          <ArrowBackIcon onClick={goBack} className="cursor-pointer" />
          <div className="px-2 f-20 fw-500">Reference Document</div>
        </div>
      </div>
      {isProgramOrCourseOrInstitution.size === 0 && (
        <div className="f6 d-flex justify-content-center py-3" style={{ height: '50vh' }}>
          No Data Found
        </div>
      )}
      {isProgramOrCourseOrInstitution.map((level, index) => {
        const name = level.get('label', '');
        const formInitiatorId = level.get('id', '');
        return (
          <>
            <div
              className="p-2 m-2 border_grey_solid cursor-pointer"
              onClick={() => openIndividualFormView(index, formInitiatorId, name)}
            >
              <div className="f-16 bold">{name}</div>
              <div className="f-15 ">
                {getShortString(
                  `${academicYearLabel} • ${categoryName} / ${level.get('formName', '')}`,
                  48,
                  false
                )}
              </div>
            </div>
          </>
        );
      })}
    </Box>
  );
};
const SearchReferencePage = ({ props }) => {
  const {
    listOptions,
    handleSearchInputChange,
    searchDocumentsByUserAction,
    search,
    academicYears,
    categoryDetails,
    academicYear,
    category,
    handleChangeAcademicYear,
    handleChangeCategory,
    setShowReferenceDoc,
    handleChange,
    handleSearch,
    selectProgram,
  } = props;

  const handleClose = () => setShowReferenceDoc(false);
  const makeSearchEmpty = () => handleSearchInputChange({ target: { value: '' } });
  const handleAcademic = (e) => {
    handleChangeAcademicYear(e);
    makeSearchEmpty();
  };
  return (
    <Box className=" bg-white rounded border pb-3 height_580px">
      <div className="sticky_header bottom_border_grey p-2 d-flex align-items-center justify-content-between">
        <div className="d-flex align-items-center ">
          <img src={document_with_outline} width={40} height={40} />
          <span className="px-2 f-20 fw-500">Reference Document</span>
        </div>
        <CloseOutlined className="text-danger remove_hover" onClick={handleClose} />
      </div>
      <div className="d-flex flex-column py-2 px-4 gap-13">
        <div className="text-center pt-5 pb-3">
          <img
            src={UserGlobalIcon}
            className="rounded UserGlobalIcon"
            width={'450px'}
            height={'324.89px'}
            alt="UserGlobalIcon"
          />
        </div>

        <div className="row  mb-2 ">
          {/* Category FIlter */}
          <div className="col-6 col-md-12 col-lg-6 col-xl-6">
            {/* academic_year */}
            <div className="grey_shade_1 mb-1 fw-500 text-mGrey f-14">Academic Year</div>
            <FormControl fullWidth size="small">
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={academicYear.get('value', '')}
                placeholder="Select Calendar"
                onChange={handleAcademic}
              >
                {academicYears.map((academic) => (
                  <MenuItem
                    value={academic.get('value', '')}
                    key={academic.get('value', '')}
                  >{`${academic.get('name', '')}`}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
          <CategoryFormSelectOption
            categoryDetails={categoryDetails.get(academicYear.get('value', ''), Map())}
            filterState={category}
            setFIlterState={handleChangeCategory}
            makeEmptyStates={() => makeSearchEmpty()}
          />
        </div>
        <div>
          <FormControl fullWidth>
            <div className="fw-500 text-mGrey f-14">Search</div>
            <Autocomplete
              disablePortal
              value={selectProgram}
              onChange={handleChange}
              options={listOptions.toJS()}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder="Search Course Name/ID..."
                  onChange={handleSearch}
                  size="small"
                  value={search}
                />
              )}
            />
          </FormControl>
        </div>
        <Button
          className={`py-2  ${
            !selectProgram ? 'bg-secondary' : 'bg-primary cursor-pointer'
          } text-white`}
          disabled={!selectProgram}
          onClick={searchDocumentsByUserAction}
        >
          SEARCH NOW
        </Button>
      </div>
    </Box>
  );
};
const ExpandedDrawer = ({ open, setDrawer, setPage, display, props }) => {
  const { referenceAttachment } = props;
  const expandIndex = display.get('clickedIndex', '');
  const id = display.get('id', '');
  const handleCloseDrawer = () => {
    setDrawer(false);
    setPage('IndividualFormView');
  };
  const formName = display.getIn([expandIndex, 'formName'], '');
  const categoryName = display.getIn([expandIndex, 'categoryName'], '');
  const academicYearLabel = display.getIn([expandIndex, 'academicYearLabel'], '');
  const name = display.getIn([expandIndex, 'name'], '');
  const doc = referenceAttachment.get(id, Map());
  const categoryFormType = doc.getIn(['categoryFormId', 'categoryFormType'], '');
  const formType = doc.getIn(['categoryFormId', 'formType'], '');
  const IsIFrame =
    categoryFormType === 'template'
      ? IframeForm
      : formType === 'section'
      ? SectionShowCkEditor
      : ShowCkEditor;
  return (
    <Drawer anchor="right" open={open} className="rightDrawer" sx={muiPaperSx}>
      <div className="close-btn" onClick={handleCloseDrawer}>
        <CloseIcon />
        <div className="close-btn-bottom"></div>
      </div>
      <div className="border-bottom f-18 py-2 px-2 ">
        <div className="f-18 fw-500">{name}</div>
        <div className="f-12">
          {`Academic Year: ${academicYearLabel} • ${categoryName} / ${formName}`}
        </div>
      </div>
      <div className="my-2 g-config-scrollbar more-details-accordion-scroll ">
        <IsIFrame doc={doc} />
      </div>
    </Drawer>
  );
};

const IndividualFormView = ({ display, referenceAttachment, setPage, setDrawer }) => {
  const expandIndex = display.get('clickedIndex', '');
  const academicYearLabel = display.getIn([expandIndex, 'academicYearLabel'], '');
  const categoryName = display.getIn([expandIndex, 'categoryName'], '');
  const formName = display.getIn([expandIndex, 'formName'], '');
  const id = display.get('id', '');
  const cardName = display.get('name', '');
  const doc = referenceAttachment.get(id, Map());
  const categoryFormType = doc.getIn(['categoryFormId', 'categoryFormType'], '');
  const formType = doc.getIn(['categoryFormId', 'formType'], '');
  const IsIFrame =
    categoryFormType === 'template'
      ? IframeForm
      : formType === 'section'
      ? SectionShowCkEditor
      : ShowCkEditor;
  const openDrawer = () => {
    setPage('ExpandedDrawer');
    setDrawer(true);
  };
  return (
    <Box className="height_580px bg-white box-shadow-static rounded pb-3">
      {/* <Card sx={{ maxWidth: 345, background: 'white', marginBottom: '0px' }}>
        <CardHeader
          avatar={<ArrowBackIcon onClick={() => setPage('ListOutTemplate')} />}
          title={<div className="">{cardName}</div>}
          subheader={`${academicYearLabel} ${categoryName} / ${formName}`}
          action={
            <Button
              onClick={openDrawer}
              variant="container"
              className="bg-white box-shadow-static px-4"
            >
              Expand
              <AspectRatioIcon className="text-primary ml-1" />
            </Button>
          }
        />
      </Card> */}
      <div className="d-flex background_white sticky_header pt-2 align-items-center bottom_border_grey">
        <div className="ml-2">
          <ArrowBackIcon className="cursor-pointer" onClick={() => setPage('ListOutTemplate')} />
        </div>
        <div
          className="p-2"
          // onClick={() => openIndividualFormView(index, formInitiatorId, name)}
        >
          <div className="f-16 bold">{getShortString(cardName, 40)}</div>
          <div className="f-15 ">
            {getShortString(`${academicYearLabel} • ${categoryName} / ${formName}`, 25)}
          </div>
        </div>
        <div className="mr-2">
          <Button
            onClick={openDrawer}
            variant="container"
            className="bg-white box-shadow-static px-4"
          >
            Expand
            <AspectRatioIcon className="text-primary ml-1" />
          </Button>
        </div>
      </div>
      <IsIFrame doc={doc} />
    </Box>
  );
};
/*---------------------------MainComponent Start---------------------------------*/
export default function ReferenceDocumentV2({ setShowReferenceDoc }) {
  const [page, setPage] = useState('SearchReferencePage');
  const [display, setDisplay] = useState(Map());
  return (
    <div className="set_reference_doc_height height_580px">
      <ConditionRender
        thisType={page}
        setPage={setPage}
        display={display}
        setDisplay={setDisplay}
        setShowReferenceDoc={setShowReferenceDoc}
      />
    </div>
  );
}
/*--------------------------------MainComponent End--------------------------------*/
