import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Modal } from 'react-bootstrap';
import DatePicker from 'react-datepicker';
import Checkbox from '@mui/material/Checkbox';
import FormGroup from '@mui/material/FormGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormLabel from '@mui/material/FormLabel';
import FormControl from '@mui/material/FormControl';
import OutlinedInput from '@mui/material/OutlinedInput';
import { Map, List, fromJS } from 'immutable';
import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

import { daysShort, formatDateObject, formatDateTime, checkBreakExisted } from '../utils';
import '../styles.css';
import { isAlphaNumericWithSpace } from 'v2/utils';

function AddEditBreakModal(props) {
  const {
    open,
    handleClose,
    title,
    item,
    updateBreak,
    settingId,
    getMinMaxTime,
    addedMinutes,
    setData,
    totalBreaks,
    defaultValues,
    institutionHeader,
    updateProgramBasicDetails,
    getProgramID,
    programID,
    CallingProgramSettings,
    institutionId,
  } = props;
  let id = item.get('_id', '');
  let parsedValue = {
    start: { hour: 9, minute: 0, format: t('date.am') },
    end: { hour: 10, minute: 0, format: t('date.am') },
  };
  if (defaultValues.size) {
    parsedValue = defaultValues.toJS();
    parsedValue.start.minute = parseInt(parsedValue.start.minute);
    parsedValue.end.minute = parseInt(parsedValue.end.minute);
  }

  const [modalState, setState] = useState(
    fromJS({
      session: parsedValue,
    })
  );
  const [checked, setChecked] = useState(List());
  const [days, setDays] = useState([]);
  useEffect(() => {
    if (item.get('session', List()).size) {
      let request = item.toJS();
      request.session.start.minute = parseInt(request.session.start.minute);
      request.session.end.minute = parseInt(request.session.end.minute);
      setState(fromJS(request));
      setChecked(item.get('workingDays', List()).toJS());
    }
  }, [item]);

  const getDateFormat = (type) => {
    if (title === 'edit') {
      if (modalState.getIn(['session', 'start', 'hour'], '')) {
        return formatDateTime(modalState.getIn(['session', `${type}`], ''));
      }
    } else {
      return formatDateTime(modalState.getIn(['session', type], parsedValue[`${type}`]));
    }
  };
  const handleCheck = (event) => {
    if (event) {
      if (title === 'edit') {
        let updatedDays = [...checked];
        event.target.checked
          ? (updatedDays = [...checked, event.target.value])
          : updatedDays.splice(checked.indexOf(event.target.value), 1);
        setChecked(updatedDays);
      } else {
        let updatedDays = [...days];
        event.target.checked
          ? (updatedDays = [...updatedDays, event.target.value])
          : updatedDays.splice(updatedDays.indexOf(event.target.value), 1);
        setDays(updatedDays);
      }
    }
  };
  const handleChange = (name, event) => {
    if (name === 'name') {
      setState(modalState.set('name', event));
    } else if (event && name === 'start') {
      setState(
        modalState
          .setIn(['session', 'start'], fromJS(formatDateObject(event)))
          .setIn(['session', 'end'], fromJS(addedMinutes(event)))
      );
    } else {
      setState(modalState.setIn(['session', 'end'], fromJS(formatDateObject(event))));
    }
  };

  const handleSubmit = () => {
    const checkValidation = () => {
      if (!isAlphaNumericWithSpace(modalState.get('name', '').trim())) {
        setData(Map({ message: t('global_configuration.break_name_alpha_numeric') }));
        return false;
      }
      let selectedDays =
        title === 'create'
          ? days
          : title === 'edit'
          ? checked
          : item.get('workingDays', List()).toJS();
      if (checkBreakExisted(totalBreaks, modalState, selectedDays, item.get('_id', ''))) {
        setData(Map({ message: t('global_configuration.brkEventExisted') }));
        return false;
      }

      return true;
    };
    if (checkValidation()) {
      const breaks = {
        name: modalState.get('name', '').length
          ? modalState.get('name', '').trim()
          : item.get('name', ''),
        days:
          title === 'create'
            ? days
            : title === 'edit'
            ? checked
            : item.get('workingDays', List()).toJS(),
      };
      const session = {
        start: modalState.getIn(['session', 'start'], Map()).size
          ? modalState.getIn(['session', 'start'], Map())
          : title === 'edit'
          ? item.getIn(['session', 'start'], Map())
          : Map({ hour: 9, minute: 0, format: t('date.am') }),
        end: modalState.getIn(['session', 'end'], Map()).size
          ? modalState.getIn(['session', 'end'], Map())
          : title === 'edit'
          ? item.getIn(['session', 'end'], Map())
          : Map({ hour: 10, minute: 0, format: t('date.am') }),
      };
      !programID
        ? updateBreak({
            operation: title === 'edit' ? 'update' : 'create',
            ...(id !== '' && { id }),
            requestBody: {
              breaks: breaks,
              session: session,
              settingId,
            },
            callBack: () => handleClose(),
            header: institutionHeader,
          })
        : updateProgramBasicDetails({
            operation: title === 'edit' ? 'update' : 'create',
            programId: getProgramID,
            urlBreakAdd: `/program-input/add-breaks`,
            urlBreakEdit: `/program-input/edit-break/${id}`,
            breaks: breaks,
            breakValue: true,
            session: session,
            id: id,
            callBack: () => handleClose(),
            CallingProgramSettings: (props) => CallingProgramSettings({ ...props }),
            header: institutionHeader,
            institutionId: institutionId,
          });
    }
  };
  const getDisabled = () => {
    if (modalState.get('name', '') === '') return true;
    if ((title === 'create' ? days.length : checked.length) === 0) return true;
    return false;
  };
  return (
    <div>
      <Modal show={open} dialogClassName="modal-555w" centered onHide={handleClose}>
        <Modal.Header className="border-none pb-0">
          <Modal.Title className="f-20">
            {title === 'edit'
              ? t('global_configuration.edit_break')
              : t('global_configuration.add_break')}
          </Modal.Title>
        </Modal.Header>

        <Modal.Body className="pt-4">
          <div className="mb-4">
            <FormLabel component="legend">
              <Trans i18nKey={'global_configuration.break_name'}></Trans>
            </FormLabel>
            <FormControl variant="outlined" className="wd-100">
              <OutlinedInput
                defaultValue={title === 'edit' ? item.get('name', '') : ''}
                id="outlined-adornment-weight"
                className="outline-text-input"
                onChange={(e) => handleChange('name', e.target.value)}
                labelWidth={0}
                placeholder={t('global_configuration.break_name')}
                inputProps={{ maxLength: 60 }}
              />
            </FormControl>
          </div>
          <div>
            <FormLabel component="legend">
              <Trans i18nKey={'global_configuration.occurence_days'}></Trans>
            </FormLabel>
            <FormControl component="fieldset">
              <FormGroup aria-label="position" row>
                {daysShort.map((day, key) => (
                  <FormControlLabel
                    className="f-11"
                    key={key}
                    value={day.value}
                    control={
                      <Checkbox
                        color="primary"
                        defaultChecked={
                          title === 'edit'
                            ? item.get('workingDays', List()).includes(day.value)
                            : false
                        }
                      />
                    }
                    label={day.label}
                    labelPlacement="end"
                    onChange={(e) => {
                      handleCheck(e);
                    }}
                  />
                ))}
              </FormGroup>
            </FormControl>
          </div>
          <div>
            <FormLabel component="legend">
              <Trans i18nKey={'global_configuration.time'}></Trans>
            </FormLabel>
            <div className="schedule-date-picker-container d-flex">
              <div>
                <DatePicker
                  selected={getDateFormat('start')}
                  onChange={(date) => handleChange('start', date)}
                  showTimeSelect
                  showTimeSelectOnly
                  timeIntervals={15}
                  timeCaption="Time"
                  dateFormat="h:mm aa"
                  className="global-date-picker-input"
                />
              </div>
              <div className="mt-2 mb-2 ml-3 mr-3 ">
                <Trans i18nKey={'global_configuration.to'}></Trans>
              </div>
              <div>
                <DatePicker
                  {...getMinMaxTime('breakEnd', { key: 0 }, modalState)}
                  selected={getDateFormat('end')}
                  onChange={(date) => handleChange('end', date)}
                  showTimeSelect
                  showTimeSelectOnly
                  timeIntervals={15}
                  timeCaption="Time"
                  dateFormat="h:mm aa"
                  className="global-date-picker-input"
                />
              </div>
            </div>
          </div>
        </Modal.Body>

        <Modal.Footer className="border-none">
          <b className="pr-2">
            <MButton color="inherit" variant="outlined" clicked={handleClose}>
              <Trans i18nKey={'cancel'}></Trans>
            </MButton>
          </b>

          <b className="pr-2">
            <MButton
              variant="contained"
              color="primary"
              clicked={handleSubmit}
              disabled={getDisabled()}
            >
              <Trans i18nKey={'save'}></Trans>
            </MButton>
          </b>
        </Modal.Footer>
      </Modal>
    </div>
  );
}

AddEditBreakModal.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  handleSubmit: PropTypes.func,
  title: PropTypes.string,
  settingId: PropTypes.string,
  updateBreak: PropTypes.func,
  getMinMaxTime: PropTypes.func,
  addedMinutes: PropTypes.func,
  item: PropTypes.object,
  setData: PropTypes.func,
  totalBreaks: PropTypes.instanceOf(List),
  defaultValues: PropTypes.instanceOf(Map),
  institutionHeader: PropTypes.object,
  updateProgramBasicDetails: PropTypes.func,
  getProgramID: PropTypes.string,
  programID: PropTypes.string,
  institutionId: PropTypes.string,
  CallingProgramSettings: PropTypes.func,
};

export default AddEditBreakModal;
