import React from 'react';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { Video, Audio } from './udUtil';

function ShowMultiMedia({ uploadDetails }) {
  const fileFormat = uploadDetails.get('mediaURL', '').split('.').pop();
  return (
    <div className="col-md-6">
      {['flv', 'mp4', 'm3u8', 'ts', '3gp', 'mov', 'avi', 'wmv'].includes(fileFormat) ? (
        <Video url={uploadDetails.get('presignedMediaURL', '')} />
      ) : ['mp3'].includes(fileFormat) ? (
        <Audio url={uploadDetails.get('presignedMediaURL', '')} />
      ) : (
        <img
          alt={'uploadedImg'}
          src={uploadDetails.get('presignedMediaURL', '')}
          className="w-100 rounded"
        />
      )}
    </div>
  );
}

ShowMultiMedia.propTypes = {
  uploadDetails: PropTypes.instanceOf(Map),
};
export default ShowMultiMedia;
