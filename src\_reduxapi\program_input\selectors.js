const programInputState = (state) => state.programInput;

const selectIsLoading = (state) => programInputState(state).get('isLoading');
const selectSnackbarShow = (state) => programInputState(state).get('snackbarShow');
const selectMessage = (state) => programInputState(state).get('message');
const selectBreadCrumb = (state) => programInputState(state).get('breadcrumbs');
const selectAddStudent = (state) => programInputState(state).get('addStudentStatus');
const selectCollegeList = (state) => programInputState(state).get('collegeList');
const selectProgramList = (state) => programInputState(state).get('programList');
const selectProgram = (state) => programInputState(state).get('program');
const selectProgramError = (state) => programInputState(state).get('programError');
const selectProgramDeptList = (state) => programInputState(state).get('programDeptList');
const selectDepartmentList = (state) => programInputState(state).get('departmentList');
const selectShowAddDepartmentModal = (state) =>
  programInputState(state).get('showAddDepartmentModal');
const selectShowAddSubjectModal = (state) => programInputState(state).get('showAddSubjectModal');
const selectSessionTypeList = (state) => programInputState(state).get('sessionTypeList');
const selectShowAddSessionDeliveryTypeModal = (state) =>
  programInputState(state).get('showAddSessionDeliveryTypeModal');
const selectCurriculum = (state) => programInputState(state).get('curriculum');
const selectEditedCurriculum = (state) => programInputState(state).get('editedCurriculum');
const selectEditedCourse = (state) => programInputState(state).get('editedCourse');
const selectCurriculumList = (state) => programInputState(state).get('curriculumList');
const selectPreRequisiteList = (state) => programInputState(state).get('preRequisiteList');
const selectFrameworkList = (state) => programInputState(state).get('frameworkLists');
const selectImpactList = (state) => programInputState(state).get('impactLists');
const selectImpactMapList = (state) => programInputState(state).get('impactMapList');
const selectContentMapList = (state) => programInputState(state).get('contentMapList');
const selectActiveStep = (state) => programInputState(state).get('activeStep');
const selectCourse = (state) => programInputState(state).get('course');
const selectSessionFlow = (state) => programInputState(state).get('sessionFlow');
const selectEditedSessionFlow = (state) => programInputState(state).get('editedSessionFlow');
const selectEditedAdditionalSessionFlow = (state) =>
  programInputState(state).get('editedAdditionalSessionFlow');

const selectCourseList = (state) => programInputState(state).get('courseList');
const selectAssignedCourse = (state) => programInputState(state).get('assignedCourse');
const selectAssignedCourseList = (state) => programInputState(state).get('assignedCourseList');
const selectGroupedCourseList = (state) => programInputState(state).get('groupedCourseList');
const selectUngroupedCourseList = (state) => programInputState(state).get('ungroupedCourseList');
const selectSelectedCoursesToGroup = (state) =>
  programInputState(state).get('selectedCoursesToGroup');
const selectCourseRecurringLevelList = (state) =>
  programInputState(state).get('courseRecurringLevelList');
const selectProgramCurriculumList = (state) =>
  programInputState(state).get('programCurriculumList');
const selectShowImpactModal = (state) => programInputState(state).get('showImpactModal');
const selectEditedImpact = (state) => programInputState(state).get('editedImpact');

const selectProgramInputList = (state) => programInputState(state).get('programInputList');
const selectFrameworkDashboard = (state) => programInputState(state).get('frameworkDashboard');
const selectFrameworkDashboardList = (state) =>
  programInputState(state).get('frameworkDashboardList');
const selectMappingTree = (state) => programInputState(state).get('mappingTree');
const selectMappingMatrix = (state) => programInputState(state).get('mappingMatrix');
const selectIsDepartmentsLoading = (state) => programInputState(state).get('isDepartmentsLoading');
const selectCourseListById = (state) => programInputState(state).get('courseListById');
const selectDeliveringSubject = (state) => programInputState(state).get('deliveringSubject');
const selectFrameworkStandardSettings = (state) =>
  programInputState(state).get('frameworkStandardSettings');
const selectFrameworkProgramSettings = (state) =>
  programInputState(state).get('frameworkProgramSettings');
const selectInvalidDataList = (state) => programInputState(state).get('InvalidDataList');
const selectValidData = (state) => programInputState(state).get('validDataList');
const selectInvalidDataCourseList = (state) =>
  programInputState(state).get('InvalidCourseDataList');
const selectvalidDataCourseList = (state) => programInputState(state).get('validCourseDataList');
const selectInvalidDataSessionList = (state) =>
  programInputState(state).get('InvalidSessionDataList');

const selectvalidDataSessionList = (state) => programInputState(state).get('validSessionDataList');
const selectInvalidDataDeptList = (state) => programInputState(state).get('InvalidDeptDataList');
const selectvalidDataDeptList = (state) => programInputState(state).get('validDeptDataList');
const selectInvalidDataDeliveryTypeList = (state) =>
  programInputState(state).get('InvalidDeliveryList');
const selectvalidDataDeliverypeList = (state) => programInputState(state).get('ValidDeliveryList');
const selectInvalidDataSessionTypeList = (state) =>
  programInputState(state).get('InvalidSessionList');
const selectvalidDataSessionTypeList = (state) => programInputState(state).get('ValidSessionList');
const selectInvalidCurricullumList = (state) =>
  programInputState(state).get('InvalidCurricullumList');
const selectvalidCurricullumList = (state) => programInputState(state).get('validCurricullumList');
const selectFrameworkMappingGraph = (state) =>
  programInputState(state).get('frameworkMappingGraph');
const selectCurriculumsWithFramework = (state) =>
  programInputState(state).get('curriculumsWithFramework');
const selectIsCourseMasterListLoading = (state) =>
  programInputState(state).get('isCourseMasterListLoading');
const selectIsFrameworksDashboardLoading = (state) =>
  programInputState(state).get('isFrameworksDashboardLoading');
const selectIsMappingGraphLoading = (state) =>
  programInputState(state).get('isMappingGraphLoading');
const selectCloSloMappingGraph = (state) => programInputState(state).get('cloSloMappingGraph');
const selectMasterGraphData = (state) => programInputState(state).get('masterGraphData');
const selectInvalidDataSLOList = (state) => programInputState(state).get('InvalidSLODataList');
const selectValidDataSLOList = (state) => programInputState(state).get('validSLODataList');

const selectConfigureSettings = (state) => programInputState(state).get('configureSettings');
const selectCourseSessionOrder = (state) =>
  programInputState(state).get('courseSessionOrderModules');
const selectMaxHours = (state) => programInputState(state).get('max_hours');
const selectVersionCourseDetail = (state) => programInputState(state).get('versionCourseDetail');
const selectVersionCourseId = (state) => programInputState(state).get('versionCourseId');
const selectGetSignedUrl = (state) => programInputState(state).get('getSignedUrl');

export {
  selectIsLoading,
  selectSnackbarShow,
  selectMessage,
  selectBreadCrumb,
  selectAddStudent,
  selectCollegeList,
  selectProgramList,
  selectProgram,
  selectProgramError,
  selectProgramDeptList,
  selectDepartmentList,
  selectShowAddDepartmentModal,
  selectShowAddSubjectModal,
  selectSessionTypeList,
  selectShowAddSessionDeliveryTypeModal,
  selectCurriculum,
  selectEditedCurriculum,
  selectEditedCourse,
  selectCurriculumList,
  selectPreRequisiteList,
  selectFrameworkList,
  selectImpactList,
  selectActiveStep,
  selectCourse,
  selectSessionFlow,
  selectMappingTree,
  selectEditedSessionFlow,
  selectCourseList,
  selectProgramCurriculumList,
  selectAssignedCourse,
  selectAssignedCourseList,
  selectCourseRecurringLevelList,
  selectGroupedCourseList,
  selectUngroupedCourseList,
  selectSelectedCoursesToGroup,
  selectProgramInputList,
  selectFrameworkDashboard,
  selectFrameworkDashboardList,
  selectShowImpactModal,
  selectEditedImpact,
  selectMappingMatrix,
  selectIsDepartmentsLoading,
  selectCourseListById,
  selectDeliveringSubject,
  selectFrameworkStandardSettings,
  selectFrameworkProgramSettings,
  selectInvalidDataList,
  selectValidData,
  selectInvalidDataCourseList,
  selectvalidDataCourseList,
  selectInvalidDataSessionList,
  selectvalidDataSessionList,
  selectInvalidDataDeptList,
  selectvalidDataDeptList,
  selectImpactMapList,
  selectContentMapList,
  selectInvalidDataDeliveryTypeList,
  selectvalidDataDeliverypeList,
  selectInvalidDataSessionTypeList,
  selectvalidDataSessionTypeList,
  selectInvalidCurricullumList,
  selectvalidCurricullumList,
  selectFrameworkMappingGraph,
  selectCurriculumsWithFramework,
  selectIsCourseMasterListLoading,
  selectIsFrameworksDashboardLoading,
  selectIsMappingGraphLoading,
  selectCloSloMappingGraph,
  selectMasterGraphData,
  selectInvalidDataSLOList,
  selectValidDataSLOList,
  selectConfigureSettings,
  selectCourseSessionOrder,
  selectMaxHours,
  selectEditedAdditionalSessionFlow,
  selectVersionCourseDetail,
  selectVersionCourseId,
  selectGetSignedUrl,
};
