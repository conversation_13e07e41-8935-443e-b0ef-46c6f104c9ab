.create_form_input_grid {
  display: grid;
  grid-gap: 10px;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  width: 450px;
}

.create_form_input_grid_child:nth-child(2n + 1):last-child {
  grid-column: span 2; /* The last column takes up the remaining space */
}

.width_500px {
  width: 500px;
}
.width_100px {
  width: 100px;
}
.file-gif-container{
  position: relative;
  height: 300px;

}
.file_gif_width_cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file_gif__background {
  position: absolute;
  top: 30px;
  left: 0;
  width: 100%;
  height: 245px;
}

.padding_12_0_px {
  padding: 12px 6px;
}
.break-word-CkEditor-comment {
  overflow-wrap: break-word;
}
.comment-ckEditor-width {
  width: 100%;
}

.padding_8_0_px {
  padding: 8px 6px;
}

.custom_static_mui_button {
  border: 0.5px solid #c4c4c4;
}
.custom_static_mui_button:active,
.custom_static_mui_button:hover {
  border: 1px solid #6c757d;
}

.file_gif__background_sub {
  position: absolute;
  top: 16px;
  right: 41px;
}

.file_gif__background_cross {
  position: absolute;
  top: 96px;
  right: 145px;
}

.height_45px {
  height: 45px;
}

.blue_background_gif {
  background-color: #e1f5fa;
}

.curved_top_border {
  border-radius: 17px / 17px;
}

.white_background {
  background-color: white;
}
.bottom-shadow {
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}
.approver-ckeditor .ck-editor__main {
  border-top: 1px solid #d1d5db;
}

.approver-ckeditor .ck-editor__editable {
  border: none !important;
  box-shadow: none !important;
}
.approver-ckeditor .ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content {
  border: none !important;
}
.approver-email-grid {
  width: 100%;
  display: grid;
  grid-template-columns: 4% 88% 8%;
  gap: 5px;
}
.toMail-scroll {
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
}
.approver-to {
  background-color: #e6e7e9;
  border-radius: 15px;
  padding: 0 5px;
}
.toMail-scroll::-webkit-scrollbar {
  width: 7px;
  height: 4px;
}
.toMail-scroll::-webkit-scrollbar-thumb {
  background: #c8c8c8;
  border-radius: 15px;
}
.toMail-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 15px;
}

.btn-height-28 {
  height: 2.8em !important;
  box-shadow: none !important;
}
.to-do-list-start {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 95%;
  left: -14px;
  width: 30px;
  height: 30px;
  background-color: #ffffff;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  z-index: 1;
  border-radius: 50%;
}
.to-do-list-end {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 95%;
  left: 318px;
  width: 30px;
  height: 30px;
  background-color: #ffffff;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  z-index: 1;
  border-radius: 50%;
}

.text-disable-for-assign {
  pointer-events: unset !important;
  color: #9ca3af !important;
}

.popover-btn-parent {
  justify-content: center;
  display: flex;
  flex-direction: row;
  position: sticky;
  bottom: 0;
  background: white;
  width: 100%;
}

.popover-btn-parent button {
  flex: 1;
  margin: 3px;
}

.selected-role-count {
  font-weight: bold;
  border: 1px solid #147afc;
  background: #ececec;
  padding: 4px 6px;
  border-radius: 10px;
  color: #147afc;
}

/* incorporate section start prefix start with icp */
.icp_section_border {
  border: 1px dashed #d1d5db;
  border-radius: 5px;
}

.category_padding {
  padding: 11.5px 0px 11.5px 14px !important;
}

.border_category_active {
  border: 1px solid #c4c4c4 !important;
}

.border_category_active:hover {
  border: 1px solid black !important;
}

.width_540px {
  width: 540px;
}

.grey_shade_1 {
  color: #4b5563;
}

.grey_shade_2 {
  color: #374151;
}

.grey_shade_2_background {
  background-color: #374151;
}

.grey_shade_3 {
  color: #9ca3af;
}

.grey_shade_4 {
  color: #6b7280;
}

.white_shade_1 {
  background-color: #f9fafb;
}

.white_shade_2 {
  background-color: #f3f4f6;
}

.grey_background_1 {
  background-color: #f9fafb;
}
.text-resubmit {
  color: #4f46e5;
}
.bg-resubmit {
  background: #4f46e5 !important;
}

.category_overview_grid {
  display: grid;
  grid-template-columns: 40px 2fr 1fr;
  align-items: center;
}

.form_step_parent {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 180px;
}

.form_step_child {
  border-radius: 10px;
  background: white;
  height: 100px;
  width: 300px;
  border: 1px solid #d1d5db;
}

.text-center {
  text-align: center !important;
}

.paper_box_shadow_as_figma {
  box-shadow: 0px 1px 2px 0 rgba(0, 0, 0, 0.12);
}

.drag_drop_dashed {
  border: 2px dashed #d1d5db;
  border-radius: 5px;
}

.drag_drop_dashed_left {
  border-left: 2px dashed #d1d5db;
}

.drag_drop_solid {
  border: 1px solid #d1d5db;
}

.grid_section_with_resource {
  display: grid;
  grid-template-columns: 3fr 1fr;
}

.grid_section_with_resource_item {
  grid-column-start: 1;
  grid-column-end: 4;
}

.display_capture {
  width: 380px;
  border-radius: 5px;
  box-shadow: 0 0 5px #000000;
  position: absolute;
  top: 58px;
  left: 50%;
  transform: translate(-50%, -50%);
}

.fw_500 {
  font-weight: 500;
}

.font_orange_intimation {
  color: #f59e0b;
}
.text_underline {
  text-decoration: underline;
}

.conclude_display {
  width: 220px;
  border: 1px solid #dad5db;
  border-radius: 5px;
}

.set_reference_doc_height {
  overflow-y: auto;
  max-height: 580px;
}

.width_180px {
  width: 180px !important;
}

.border_grey_solid {
  border: 1px solid #e1e1e1;
  border-radius: 5px;
}

.background_white {
  background-color: white;
}

.bottom_border_grey {
  border-bottom: 1px solid #e1e1e1;
}

.sticky_header {
  position: sticky;
  top: 0;
  z-index: 999;
  background: white;
}

.category_padding_sub {
  padding: 7px;
  padding-right: 0px;
}
.approver-user {
  width: 180px;
}
.user-name {
  width: 100px;
}
.users-gap {
  column-gap: 50px;
  row-gap: 20px;
}
.fixed-header {
  position: sticky !important;
  inset-block-start: 0 !important;
  z-index: 2 !important;
}
.bg-grey {
  background-color: #f9fafb;
}

.height_580px {
  min-height: 580px;
}

.gap-13 {
  gap: 13px;
}

.height_overflow_category {
  height: 400px;
  overflow-y: auto;
}

.pr-32 {
  padding-right: 32px;
}

.modal-9999 {
  z-index: 9999 !important;
}

/*----------------------------------approval style--------------------------------*/
.br-8 {
  border-radius: 8px !important;
}
.application-status {
  border: 1px solid #f59e0b;
  background-color: #fffbeb;
  color: '#F59E0B';
}
.approval-role-border {
  border: 1.5px solid #d1d5db;
}
.approval-role-border-right {
  border-right: 1.5px solid #d1d5db;
}
.approval-margin-left {
  margin-left: 35px;
}
.approval-guild-resorce {
  flex: calc(100% / 1);
  text-align: end;
  margin-right: 40px;
}
.approval-menuItem-selected {
  color: #9ca3af;
}
.approval-view-all {
  color: #b91c1c !important;
}

.q360_background_color {
  background-color: #f3f4f6;
}

.col-6-no-padding {
  padding-right: 0px;
  padding-left: 0px;
}
.defalut-content {
  color: red;
}
.approver-list {
  max-height: 500px;
  overflow-y: auto;
}
.approver-ckeditor-particular .ck-editor__editable {
  border: 2px solid #e5e7eb !important;
  border-bottom-left-radius: 4px !important;
  border-bottom-right-radius: 4px !important;
  box-shadow: none !important;
}
.approver-ckeditor-particular .ck-editor__editable {
  min-height: 220px;
}
.approver-ckeditor .ck-editor__editable {
  min-height: 220px;
}
.approver-ckeditor-particular .ck.ck-editor__top .ck-sticky-panel .ck-sticky-panel__content {
  border-top: 2px solid #e5e7eb !important;
  border-left: 2px solid #e5e7eb !important;
  border-right: 2px solid #e5e7eb !important;
  border-top-left-radius: 4px !important;
  border-top-right-radius: 4px !important;
}
.text-underline {
  border-bottom: 1.5px solid #1e8bff;
}
.iframe-style {
  width: 100%;
  height: 100vh;
  overflow-y: scroll;
}
/*----------------------------------approval style--------------------------------*/

.form-disable {
  background-color:#f7f5f5;
  cursor:default;
}

.scroll-overflow-roles-permission {
  height: 300px;
  overflow-y: auto;
}

.scroll-overflow-roles-permission-max {
  max-height: 300px;
  overflow-y: auto;
}
.custom-scroll-ck-editor{
  height: 70vh;
  overflow-y: auto;
}
.from-accordion-stickey{
   position: sticky;
   top: 50;
   bottom: 30 
}

.discussion-msg {
   background-color: #fafafa;
   background-clip: border-box;
   border: 1px solid rgba(0, 0, 0, 0.1);
   border-radius: 4px;
   width: fit-content;
   white-space: normal; 
   word-wrap: break-word; 
   word-break: break-word; 
   max-width: 350px; 
   padding: 4px;
}

.full-height-q360 {
  height: 200vh;
}

.sticky-header-q360 {
  position: sticky;
  top: 0;
  /* padding: 20px; */
}

.q360-min-height-100 {
  min-height: 100px !important;
}
