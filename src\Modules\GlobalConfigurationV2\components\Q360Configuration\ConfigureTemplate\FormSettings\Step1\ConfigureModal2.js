import React, { useEffect, useState } from 'react';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import { Chip, Paper, Radio, Divider, Checkbox } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

import { List, Map as IMap, fromJS } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';
import {
  getTermList,
  // getQaPcSetting
} from '_reduxapi/q360/actions';
import { selectTermList, selectQaPcSetting } from '_reduxapi/q360/selectors';
import { useSelector } from 'react-redux';
import { EnableOrDisable } from 'Modules/GlobalConfigurationV1/utils';
import {
  useCallApiHook,
  useSearchParams,
} from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';

import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
// import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CircleIcon from '@mui/icons-material/Circle';
import MButton from 'Widgets/FormElements/material/Button';

import { numbers, monthNames } from './utils';

export function ConditionalWrapper({ condition, children }) {
  return <>{condition ? children : null}</>;
}

export default function ConfigureModal2({
  modal,
  openOrCloseModal,
  checkedData,
  constructingForConfiguration,
  programName,
  programId,
  setDelete,
}) {
  const [modalCheckedData, setModalCheckedData] = useState(checkedData);
  const [searchParams] = useSearchParams();
  const academicTerms = searchParams.get('academicTerms');
  const studentGroups = searchParams.get('studentGroups');
  const attemptType = searchParams.get('attemptType');
  const everyAcademic = searchParams.get('everyAcademic');
  const occurrenceConfiguration = searchParams.get('occurrenceConfiguration');
  const level = searchParams.get('level');
  const actions = {
    studentGroups: studentGroups ? true : false,
    everyAcademic: everyAcademic ? true : false,
    occurrenceConfiguration: occurrenceConfiguration ? true : false,
    academicTerms: academicTerms ? true : false,
    attemptType: attemptType ? true : false,
  };

  const singleGroup = IMap({
    name: 'all',
    minimum: 1,
    startMonth: '',
    endMonth: '',
  });

  const initialAttemptType = fromJS({
    typeName: 'All',
    termName: 'All',
    executionsPer: true,
    academicYear: 'every',
    group: level === 'course' && actions.studentGroups ? 'all' : 'none',
    groupType: [singleGroup],
  });

  const [state, setState] = useState(
    fromJS({
      attemptTypes: fromJS([initialAttemptType]),
      academicTerm: actions.academicTerms ? 'all' : 'none',
      attemptType: actions.attemptType ? 'all' : 'none',
      selectedAcademicTerm: List(),
      selectedAttemptType: List(),
      tags: List(),
      studentGroups: List(),
    })
  );

  // const [getQaPcSettingApi] = useCallApiHook(getQaPcSetting);
  const [getTermListApi] = useCallApiHook(getTermList);

  // useEffect(() => {
  //   // if (qaPcSetting.get('tagData', List()).size) return;
  //   getQaPcSettingApi();
  // }, []);

  const qaPcSetting = useSelector(selectQaPcSetting);
  const termList = useSelector(selectTermList);

  useEffect(() => {
    if (level !== 'institution') getTermListApi(programId);
  }, [programId]);

  useEffect(() => {
    const getSelectedCourseCount = gettingSelectedCourseCount();
    if (level !== 'institution' && getSelectedCourseCount === 0) openOrCloseModal();
  }, [gettingSelectedCourseCount]);

  function gettingSelectedCourseCount() {
    let count = 0;
    modalCheckedData.map((course) => {
      count += course.size;
    });
    return count;
  }

  const handleChipDelete = (key, index) => () => {
    setDelete((prev) => {
      let prevState = prev;
      prevState = prevState.update(key, List(), (courseKeys) => {
        return courseKeys.filter((_, i) => i !== index);
      });
      return prevState.filter((value) => value.size > 0);
    });

    setModalCheckedData((prev) =>
      prev.update(key, List(), (courseKeys) => {
        return courseKeys.filter((_, i) => i !== index);
      })
    );
  };

  const updateGroupsCount = (e) => {
    const length = e.target.value;
    const result = Array.from({ length }, (_, index) => `Gr-${String(index + 1).padStart(2, '0')}`);
    setState((prevState) =>
      prevState.set('studentGroups', fromJS(result)).update('attemptTypes', List(), (data) => {
        let newData = data;
        newData.forEach((_, index) => {
          newData = newData.setIn([index, 'group'], 'all');
        });
        return newData;
      })
    );
  };
  const handleDelete = (index) => () => {
    setState((prevState) => prevState.update('studentGroups', (group) => group.delete(index)));
  };

  const handleChange = (key, value) => {
    const typeKey = key === 'academicTerm' ? 'selectedAcademicTerm' : 'selectedAttemptType';
    const typeData = key === 'academicTerm' ? List(['All']) : List(['All']);
    setState((prevState) =>
      prevState.set(key, value).set(typeKey, value === 'all' ? typeData : List())
    );
  };

  const selectedTerms = state.get('selectedAcademicTerm', List());
  const selectedTypes = state.get('selectedAttemptType', List());
  const selectedAcademicTerm = state.get('academicTerm', '');
  const selectedAttemptType = state.get('attemptType', '');

  useEffect(() => {
    const generateOutput = () => {
      let output = List();

      const checkTerm = selectedAcademicTerm === 'all' && level !== 'institution' ? 'All' : 'none';
      const checkAttemptType =
        selectedAttemptType === 'all' && level !== 'institution' ? 'All' : 'none';

      if (selectedTerms.isEmpty() && selectedTypes.isEmpty()) {
        output = output.push(
          fromJS(initialAttemptType.set('termName', checkTerm).set('typeName', checkAttemptType))
        );
      }

      if (selectedTerms.isEmpty() && !selectedTypes.isEmpty()) {
        selectedTypes.forEach((type) => {
          output = output.push(
            fromJS(initialAttemptType.set('termName', checkTerm).set('typeName', type))
          );
        });
        return output;
      }

      if (selectedTypes.isEmpty() && !selectedTerms.isEmpty()) {
        selectedTerms.forEach((term) => {
          output = output.push(
            fromJS(initialAttemptType.set('termName', term).set('typeName', checkAttemptType))
          );
        });
        return output;
      }

      selectedTerms.forEach((term) => {
        selectedTypes.forEach((type) => {
          output = output.push(
            fromJS(initialAttemptType.set('termName', term).set('typeName', type))
          );
        });
      });

      return output;
    };

    setState(state.set('attemptTypes', generateOutput()));
  }, [selectedTerms, selectedTypes, selectedAcademicTerm, selectedAttemptType]);

  const handleChange2 = (e, key, value) => {
    const isChecked = e.target.checked;

    if (isChecked) {
      setState((prevState) => prevState.update(key, List(), (ids) => ids.push(value)));
    } else {
      setState((prevState) =>
        prevState.update(key, List(), (ids) => ids.filter((id) => id !== value))
      );
    }
  };

  const handleChangeAttemptData = (data) => (e) => {
    let { key, value } = data;
    if (value === undefined) value = e.target.value;
    setState((prevState) => {
      let result = IMap();
      result = prevState.setIn(key, value);
      if (key.includes('group')) {
        key[key.length - 1] = 'groupType';
        if (value === 'all') {
          result = result.setIn(key, List([singleGroup]));
        } else {
          const studentGroups = result.get('studentGroups', List());
          result = result.setIn(
            key,
            studentGroups.map((groupName) => singleGroup.set('name', groupName))
          );
        }
      }
      return result;
    });
  };

  const getTagData = () => {
    return qaPcSetting
      .get('tagData', List())
      .filter((tag) => tag.get('isDefault', false))
      .filter((tag) => tag.get('level', false) === level);
  };

  const tagData = getTagData();

  const handleCheckTag = (tagIndex, bool, tagName, id, hasSubTags) => {
    setState((prev) => {
      let newState = prev;
      if (bool) {
        newState = newState
          .setIn(['tags', tagIndex, 'name'], tagName)
          .setIn(['tags', tagIndex, '_id'], id);

        if (hasSubTags) {
          newState = newState.setIn(
            ['tags', tagIndex, 'subTag'],
            tagData.getIn([tagIndex, 'subTag'], List())
          );
        }
      } else {
        newState = newState.setIn(['tags', tagIndex], IMap());
      }
      return newState;
    });
  };
  const handleCheckSubTag = (tagIndex, subTagIndex, bool, subTagName, tagName, id) => {
    setState((prev) => {
      let newState = prev;
      if (typeof newState.getIn(['tags', tagIndex], '') === 'undefined') {
        newState = newState.setIn(['tags', tagIndex], IMap());
      }
      newState = newState.updateIn(['tags', tagIndex, 'subTag'], List(), (data) => {
        if (bool) {
          return data.push(tagData.getIn([tagIndex, 'subTag', subTagIndex], ''));
        } else {
          const index = data.findIndex((item) => item === subTagName);
          return data.filter((_, i) => i !== index);
        }
      });
      newState = newState
        .setIn(['tags', tagIndex, 'name'], tagName)
        .setIn(['tags', tagIndex, '_id'], id);
      return newState;
    });
  };
  return (
    <DialogModal show={modal} onClose={openOrCloseModal} maxWidth={'md'} fullWidth={true}>
      <div className="px-4 py-3">
        <div className="h5 fw-400 q360-color-gray">{programName}</div>

        {level !== 'institution' && (
          <div className="f-14 text-mGrey fw-400">
            Selected Courses ({gettingSelectedCourseCount()})
          </div>
        )}
        <div className="d-flex flex-wrap align-items-center gap-10 pt-2 pb-3">
          {modalCheckedData.entrySeq().map(([key, courses]) =>
            courses.map((courseKey, index) => {
              const [courseId, courseName] = courseKey.split('/');
              return (
                <Chip
                  label={courseName}
                  key={courseId}
                  // onClick={handleClick}
                  onDelete={handleChipDelete(key, index)}
                  deleteIcon={<CloseIcon />}
                  size="small"
                  className="select-color"
                />
              );
            })
          )}
        </div>

        <Paper elevation={0} variant="outlined">
          <div className="popup-q360-overflow px-3 py-3">
            <div className="f-16 fw-500 text-dGrey mb-2">Occurrences</div>
            <ConditionalWrapper condition={actions.studentGroups}>
              <div>
                <div className="f-12 fw-400 text-dGrey my-2">Students Groups</div>
                <div className="w-20">
                  <MaterialInput
                    elementType={'materialInput'}
                    value={state.get('studentGroups', List()).size}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    // elementConfig={{ options: numbers }}
                    placeholder={'Enter Numbers'}
                    changed={updateGroupsCount}
                  />
                </div>
                <div className="d-flex flex-wrap align-items-center gap-10 mt-1 my-2">
                  {state.get('studentGroups', List()).map((group, groupIndex) => {
                    return (
                      <Chip
                        key={groupIndex}
                        label={group}
                        // onClick={handleClick}
                        onDelete={handleDelete(groupIndex)}
                        deleteIcon={<CloseIcon />}
                        size="small"
                        className="select-color"
                      />
                    );
                  })}
                </div>
              </div>
            </ConditionalWrapper>

            <ConditionalWrapper condition={actions.academicTerms}>
              <div>
                <div className="f-14 fw-400 text-dGrey my-2">Academic Term</div>
                <div className="d-flex align-items-center ml-1 mt-1">
                  <div className="d-flex align-items-center my-1">
                    <div className="mr-2">
                      <div className="d-flex align-item-center">
                        <Radio
                          checked={state.get('academicTerm', 'all') === 'all'}
                          onChange={() => handleChange('academicTerm', 'all')}
                          name="radio-buttons"
                          inputProps={{ 'aria-label': 'A' }}
                          size="small"
                          className="m-0 p-0"
                        />
                        <div className="f-14 ml-2">All Term</div>
                      </div>
                    </div>
                  </div>
                  <div className="d-flex align-items-center my-1 ml-2">
                    <div className="mr-2">
                      <div className="d-flex align-item-center">
                        <Radio
                          checked={state.get('academicTerm', 'every') === 'every'}
                          onChange={() => handleChange('academicTerm', 'every')}
                          name="radio-buttons"
                          inputProps={{ 'aria-label': 'B' }}
                          size="small"
                          className="m-0 p-0"
                        />
                        <div className="f-14 ml-2">Every Term</div>
                      </div>
                    </div>
                  </div>
                </div>
                {/* {state.get('academicTerm', 'every') === 'every' && ( */}
                <div className="d-flex align-items-center ml-1 mt-1">
                  {termList.map((term, index) => (
                    <div className="d-flex align-items-center my-1 mr-2" key={index}>
                      <div className="mr-2">
                        <div className="d-flex align-item-center">
                          <Checkbox
                            checked={
                              state.get('selectedAcademicTerm', List()).includes(term) ||
                              state.get('academicTerm', '') === 'all'
                            }
                            onChange={(e) => handleChange2(e, 'selectedAcademicTerm', term)}
                            name="radio-buttons"
                            inputProps={{ 'aria-label': 'A' }}
                            size="small"
                            className="m-0 p-0"
                            disabled={state.get('academicTerm', '') === 'all'}
                          />
                          <div className="f-14 ml-2 text-capitalize">{term}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                {/*  )} */}
              </div>
              <Divider className="my-3" />
            </ConditionalWrapper>

            <ConditionalWrapper condition={actions.attemptType}>
              <div>
                <div className="f-14 fw-400 text-dGrey my-2">Attempt Type</div>
                <div className="d-flex align-items-center ml-1 mt-1">
                  <div className="d-flex align-items-center my-1">
                    <div className="mr-2">
                      <div className="d-flex align-item-center">
                        <Radio
                          checked={state.get('attemptType', 'every') === 'all'}
                          onChange={() => handleChange('attemptType', 'all')}
                          name="radio-buttons"
                          inputProps={{ 'aria-label': 'A' }}
                          size="small"
                          className="m-0 p-0"
                        />
                        <div className="f-14 ml-2">All Type</div>
                      </div>
                    </div>
                  </div>
                  <div className="d-flex align-items-center my-1 ml-2">
                    <div className="mr-2">
                      <div className="d-flex align-item-center">
                        <Radio
                          checked={state.get('attemptType', 'every') === 'every'}
                          onChange={() => handleChange('attemptType', 'every')}
                          name="radio-buttons"
                          inputProps={{ 'aria-label': 'B' }}
                          size="small"
                          className="m-0 p-0"
                        />
                        <div className="f-14 ml-2">Every Type</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* {state.get('attemptType', 'every') === 'every' && ( */}
                <div className="d-flex align-items-center ml-1 mt-1">
                  {qaPcSetting.get('attemptData', List()).map((attemptType, index) => (
                    <div className="d-flex align-items-center my-1" key={index}>
                      <div className="mr-2">
                        <div className="d-flex align-item-center">
                          <Checkbox
                            checked={
                              state
                                .get('selectedAttemptType', List())
                                .includes(attemptType.get('name', '')) ||
                              state.get('attemptType', '') === 'all'
                            }
                            onChange={(e) =>
                              handleChange2(e, 'selectedAttemptType', attemptType.get('name', ''))
                            }
                            name="radio-buttons"
                            inputProps={{ 'aria-label': 'A' }}
                            size="small"
                            className="m-0 p-0"
                            disabled={state.get('attemptType', '') === 'all'}
                          />
                          <div className="f-14 ml-2 text-capitalize">
                            {attemptType.get('name', '')}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                {/* )} */}
              </div>
              <Divider className="my-3" />
            </ConditionalWrapper>

            {state.get('attemptTypes', List()).map((attempt, attemptKey) => {
              return (
                <div key={attemptKey}>
                  <div>
                    <div className="text-capitalize f-14">
                      {level !== 'institution' && attempt.get('termName', '') !== 'none'
                        ? `${attempt.get('termName', '')} Term`
                        : ''}
                    </div>
                    <div className="ml-3 mt-1 f-14">
                      <div className="text-capitalize">
                        {level !== 'institution' && attempt.get('typeName', '') !== 'none'
                          ? `${attempt.get('typeName', '')} Attempt Type`
                          : ''}
                      </div>
                      <section className="f-14 fw-400 text-dGrey">
                        {/* <ConditionalWrapper condition={actions.everyAcademic}>
                          <div className="d-flex align-items-center mt-2">
                            <div className="mr-2">
                              <Checkbox
                                size="small"
                                className="m-0 p-0"
                                checked={attempt.get('executionsPer', false)}
                                onChange={handleChangeAttemptData({
                                  key: ['attemptTypes', attemptKey, 'executionsPer'],
                                  value: !attempt.get('executionsPer', false),
                                })}
                              />
                            </div>
                            <div>Executions Per Academic Year </div>
                          </div>
                          <div className="d-flex align-items-center pl-4 ml-1 mt-1">
                             <div className="d-flex align-items-center my-1">
                              <div className="mr-2">
                                <Radio
                                  checked={attempt.get('academicYear', '') === 'all'}
                                  onChange={handleChangeAttemptData({
                                    key: ['attemptTypes', attemptKey, 'academicYear'],
                                    value: 'all',
                                  })}
                                  name="radio-buttons"
                                  inputProps={{ 'aria-label': 'B' }}
                                  size="small"
                                  className="m-0 p-0"
                                  disabled={true}
                                />
                              </div>
                              <div>For All Academic Year</div>
                            </div> 
                             <div className="d-flex align-items-center my-1 ml-2">
                              <div className="mr-2">
                                <Radio
                                  checked={attempt.get('academicYear', '') === 'every'}
                                  onChange={handleChangeAttemptData({
                                    key: ['attemptTypes', attemptKey, 'academicYear'],
                                    value: 'every',
                                  })}
                                  name="radio-buttons"
                                  inputProps={{ 'aria-label': 'A' }}
                                  size="small"
                                  className="m-0 p-0"
                                />
                              </div>
                              <div>For Every Academic Year</div>
                            </div>
                          </div>
                        </ConditionalWrapper> */}

                        <ConditionalWrapper condition={actions.studentGroups}>
                          <div className="d-flex align-items-center pl-4 ml-1 mt-1">
                            <div className="d-flex align-items-center my-1">
                              <div className="mr-2">
                                <Radio
                                  checked={attempt.get('group', '') === 'all'}
                                  onChange={handleChangeAttemptData({
                                    key: ['attemptTypes', attemptKey, 'group'],
                                    value: 'all',
                                  })}
                                  name="radio-buttons"
                                  inputProps={{ 'aria-label': 'A' }}
                                  size="small"
                                  className="m-0 p-0"
                                />
                              </div>
                              <div>All Group</div>
                            </div>
                            <div className="d-flex align-items-center my-1 ml-2">
                              <div className="mr-2">
                                <Radio
                                  checked={attempt.get('group', '') === 'individual'}
                                  onChange={handleChangeAttemptData({
                                    key: ['attemptTypes', attemptKey, 'group'],
                                    value: 'individual',
                                  })}
                                  value="b"
                                  name="radio-buttons"
                                  inputProps={{ 'aria-label': 'B' }}
                                  size="small"
                                  className="m-0 p-0"
                                />
                              </div>
                              <div>Individual Group</div>
                            </div>
                          </div>
                        </ConditionalWrapper>
                        {attempt.get('group', '') === 'all' ||
                        attempt.get('group', '') === 'none' ? (
                          <>
                            <div className="d-flex align-items-center pl-4 pt-1">
                              <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
                              <div>Minimum</div>
                              <div className="mx-2">
                                <MaterialInput
                                  value={attempt.getIn(['groupType', 0, 'minimum'], 0)}
                                  changed={handleChangeAttemptData({
                                    key: ['attemptTypes', attemptKey, 'groupType', 0, 'minimum'],
                                  })}
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'standard'}
                                  size={'small'}
                                  elementConfig={{ options: numbers }}
                                  disabled={!actions.occurrenceConfiguration}
                                  sx={{
                                    width: 45,
                                    paddingBottom: 0,
                                    padding: '0 5px',
                                  }}
                                />
                              </div>
                              <div>Times</div>
                            </div>
                            <div className="d-flex align-items-center pl-4 pt-2">
                              <CircleIcon sx={{ fontSize: 7 }} className="mx-1" />
                              <div>Duration</div>
                            </div>
                            <div className="d-flex align-items-center pl-4 mlt-12 gap-10 py-2 w-30">
                              <div className="flex-grow-1">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  size={'small'}
                                  placeholder="Start Month"
                                  elementConfig={{ options: monthNames }}
                                  value={attempt.getIn(['groupType', 0, 'startMonth'], '')}
                                  changed={handleChangeAttemptData({
                                    key: ['attemptTypes', attemptKey, 'groupType', 0, 'startMonth'],
                                  })}
                                />
                              </div>
                              <div className="flex-grow-1">
                                <MaterialInput
                                  elementType={'materialSelect'}
                                  type={'text'}
                                  variant={'outlined'}
                                  elementConfig={{ options: monthNames }}
                                  placeholder="End Month"
                                  value={attempt.getIn(['groupType', 0, 'endMonth'], '')}
                                  changed={handleChangeAttemptData({
                                    key: ['attemptTypes', attemptKey, 'groupType', 0, 'endMonth'],
                                  })}
                                  size="small"
                                />
                              </div>
                            </div>
                          </>
                        ) : (
                          <>
                            <div className="w-100 d-flex flex-row flex-wrap gap-10 pl-4 ml-1 mt-2">
                              {attempt.get('groupType', List()).map((groupValue, groupIndex) => {
                                return (
                                  <div
                                    className="align-self-start"
                                    style={{ width: '47%' }}
                                    key={groupIndex}
                                  >
                                    <Accordion
                                      className="bg-white"
                                      elevation={0}
                                      variant="outlined"
                                      disableGutters
                                      expanded={true}
                                    >
                                      <AccordionSummary
                                        // expandIcon={<ExpandMoreIcon />}
                                        aria-controls="panel3-content"
                                        id="panel3-header"
                                      >
                                        {groupValue.get('name', '')}
                                      </AccordionSummary>
                                      <Divider className="mx-3" />
                                      <AccordionDetails>
                                        <div>
                                          <div className="d-flex align-items-center gap-5">
                                            <div>Minimum</div>
                                            <div>
                                              <MaterialInput
                                                elementType={'materialSelect'}
                                                type={'text'}
                                                changed={handleChangeAttemptData({
                                                  key: [
                                                    'attemptTypes',
                                                    attemptKey,
                                                    'groupType',
                                                    groupIndex,
                                                    'minimum',
                                                  ],
                                                })}
                                                variant={'standard'}
                                                size={'small'}
                                                elementConfig={{ options: numbers }}
                                                disabled={!actions.occurrenceConfiguration}
                                                value={groupValue.get('minimum', 0)}
                                                sx={{
                                                  width: 45,
                                                  paddingBottom: 0,
                                                  padding: '0 5px',
                                                }}
                                              />
                                            </div>
                                            <div>Times</div>
                                          </div>
                                          <div className="d-flex align-items-center ">
                                            <div>Duration</div>
                                          </div>
                                          <div className="d-flex align-items-center gap-10 py-2">
                                            <MaterialInput
                                              elementType={'materialSelect'}
                                              type={'text'}
                                              variant={'outlined'}
                                              changed={handleChangeAttemptData({
                                                key: [
                                                  'attemptTypes',
                                                  attemptKey,
                                                  'groupType',
                                                  groupIndex,
                                                  'startMonth',
                                                ],
                                              })}
                                              size={'small'}
                                              placeholder="Start Month"
                                              elementConfig={{ options: monthNames }}
                                              value={groupValue.get('startMonth', '')}
                                            />
                                            <MaterialInput
                                              elementType={'materialSelect'}
                                              type={'text'}
                                              variant={'outlined'}
                                              size={'small'}
                                              changed={handleChangeAttemptData({
                                                key: [
                                                  'attemptTypes',
                                                  attemptKey,
                                                  'groupType',
                                                  groupIndex,
                                                  'endMonth',
                                                ],
                                              })}
                                              placeholder="End Month"
                                              elementConfig={{ options: monthNames }}
                                              value={groupValue.get('endMonth', '')}
                                            />
                                          </div>
                                        </div>
                                      </AccordionDetails>
                                    </Accordion>
                                  </div>
                                );
                              })}
                            </div>
                            {/* <div className="file-gallery-1">
                              {attempt.get('groupType', List()).map((groupValue, groupIndex) => {
                                return <div className="text-capitalize">asdasd</div>;
                              })}
                            </div> */}
                          </>
                        )}
                      </section>
                    </div>
                  </div>

                  <Divider className="mt-2" />
                </div>
              );
            })}

            {tagData.size > 0 && (
              <div className="f-12 fw-400 text-dGrey mt-3 mb-2">Q360 - Tags</div>
            )}
            {tagData.map((item, index) => {
              const checkedSubTag = state.getIn(['tags', index, 'subTag'], List()).size;
              const subTag = tagData.getIn([index, 'subTag'], List()).size;
              const hasSubTags = subTag > 0;
              const checkedTag = state
                .get('tags', List())
                .filter((data) => typeof data !== 'undefined');
              const withoutSubTagCheck = checkedTag.some(
                (data) => data.get('_id', '') === item.get('_id', '')
              );
              return (
                <div key={index}>
                  <div className="d-flex align-items-center mt-2">
                    <div className="mr-2">
                      <Checkbox
                        size="small"
                        className="m-0 p-0"
                        checked={hasSubTags ? checkedSubTag === subTag : withoutSubTagCheck}
                        indeterminate={
                          hasSubTags ? checkedSubTag !== subTag && checkedSubTag !== 0 : false
                        }
                        onClick={(event) =>
                          handleCheckTag(
                            index,
                            event.target.checked,
                            item.get('name', ''),
                            item.get('_id', ''),
                            hasSubTags
                          )
                        }
                      />
                    </div>
                    <div className="f-14 fw-400 text-dGrey text-capitalize">
                      {item.get('name', '')}{' '}
                      <EnableOrDisable valid={item.get('isDefault', false)}>
                        <span className="color-lt-gray">(Default)</span>
                      </EnableOrDisable>
                    </div>
                  </div>
                  <div className="d-flex">
                    {item.get('subTag', List()).map((subTag, subTagIndex) => (
                      <div key={subTagIndex} className="d-flex align-items-center mt-2 ml-3">
                        <Checkbox
                          size="small"
                          className="m-0 p-0"
                          checked={state.getIn(['tags', index, 'subTag'], List()).includes(subTag)}
                          onClick={(event) =>
                            handleCheckSubTag(
                              index,
                              subTagIndex,
                              event.target.checked,
                              subTag,
                              item.get('name', ''),
                              item.get('_id', '')
                            )
                          }
                        />
                        <div className="f-14 fw-400 text-dGrey ml-2 text-capitalize">{subTag}</div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </Paper>
        <div className="d-flex align-items-center mt-3 gap-20">
          <MButton
            variant="outlined"
            className="px-4 ml-auto "
            size={'small'}
            clicked={openOrCloseModal}
            color={'gray'}
          >
            Cancel
          </MButton>
          <MButton
            variant="contained"
            className="px-4 "
            color="primary"
            size={'small'}
            clicked={constructingForConfiguration(state, actions)}
          >
            Save
          </MButton>
        </div>
      </div>
    </DialogModal>
  );
}
