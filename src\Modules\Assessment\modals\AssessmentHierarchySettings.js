import React, { useState } from 'react';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MButton from 'Widgets/FormElements/material/Button';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import { ucFirst } from '../../../utils';

const AssessmentHierarchySettingsModal = (props) => {
  const {
    show,
    handleModalClose,
    assessmentTypesList,
    toggleAssessmentSubType,
    getAssessmentTypesList,
  } = props;
  const [checkedState, setCheckedState] = useState(
    fromJS(
      assessmentTypesList.get('types', List()).map((parent, index) =>
        fromJS({
          typeName: parent.get('typeName', ''),
          isActive: parent.get('isActive', ''),
          subTypes: parent.get('subTypes', List()).map((subType, index) =>
            fromJS({
              typeName: subType.get('typeName', ''),
              isActive: subType.get('isActive', ''),
            })
          ),
        })
      )
    )
  );
  const handleCheck = (event, index, sIndex) => {
    setCheckedState(
      checkedState.setIn([index, 'subTypes', sIndex, 'isActive'], event.target.checked)
    );
  };
  const toggleCallback = () => {
    getAssessmentTypesList();
    handleModalClose();
  };
  const handleSave = () => {
    const requestData = fromJS({
      _id: assessmentTypesList.get('_id', ''),
      types: checkedState.map((parent, index) =>
        fromJS({
          typeName: parent.get('typeName', ''),
          isActive: true,
          subTypes: parent.get('subTypes', List()).map((subType, index) =>
            fromJS({
              typeName: subType.get('typeName', ''),
              isActive: subType.get('isActive', ''),
            })
          ),
        })
      ),
    });
    toggleAssessmentSubType(requestData, () => toggleCallback());
  };

  return (
    <MaterialDialog show={show} onClose={handleModalClose} maxWidth={'xs'} fullWidth={true}>
      <div className="w-100 p-4">
        <p className="mb-3 pb-2 border-bottom bold f-19"> Assessment Hierarchy Settings</p>
        {assessmentTypesList.get('types', List()).map((parent, index) => (
          <div key={index} className="mt-2 mb-2">
            <p className="bold mb-1 f-15">{ucFirst(parent.get('typeName', ''))}</p>
            <div className="d-flex">
              {parent.get('subTypes', List()).map((sType, sIndex) => (
                <FormControlLabel
                  key={sIndex}
                  control={<Checkbox color="primary" defaultChecked={sType.get('isActive', '')} />}
                  label={ucFirst(sType.get('typeName', ''))}
                  labelPlacement="end"
                  onChange={(event) => handleCheck(event, index, sIndex)}
                />
              ))}
            </div>
          </div>
        ))}
        <div className="d-flex justify-content-end border-top pt-3">
          <MButton variant="outlined" color="primary" className={'mr-2'} clicked={handleModalClose}>
            Cancel
          </MButton>
          <MButton variant="contained" color="primary" clicked={handleSave}>
            Save
          </MButton>
        </div>
      </div>
    </MaterialDialog>
  );
};
AssessmentHierarchySettingsModal.propTypes = {
  show: PropTypes.bool,
  handleModalClose: PropTypes.func,
  assessmentTypesList: PropTypes.instanceOf(Map),
  getAssessmentTypesList: PropTypes.func,
  toggleAssessmentSubType: PropTypes.func,
};
export default AssessmentHierarchySettingsModal;
