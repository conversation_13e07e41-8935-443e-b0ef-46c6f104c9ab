import React from 'react';
import { Route, Switch } from 'react-router';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import SnackBars from 'Modules/Utils/Snackbars';
import './style.css';
import { t } from 'i18next';

// redux
import * as actions from '_reduxapi/Announcement/actions';
import { selectIsLoading, selectMessage } from '_reduxapi/Announcement/selector';

// utils
import Loader from 'Widgets/Loader/Loader';
import Breadcrumb from 'Widgets/Breadcrumb/Breadcrumb';
import AnnouncementSettingsIndex from './Components/AnnouncementSettingsIndex';

const AnnouncementUserPermission = ({ message, isLoading }) => {
  return (
    <div>
      {message && <SnackBars show={true} message={message} />}
      <Loader isLoading={isLoading} />
      <Breadcrumb>{t('User_Module_Permission.user_module_permission')}</Breadcrumb>
      <Switch>
        <Route path="/UserModulePermissions" exact component={AnnouncementSettingsIndex}></Route>
      </Switch>
    </div>
  );
};

AnnouncementUserPermission.propTypes = {
  message: PropTypes.string,
  isLoading: PropTypes.bool,
  messageData: PropTypes.string,
  isLoadingData: PropTypes.bool,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
  };
};

export default connect(mapStateToProps, { ...actions })(withRouter(AnnouncementUserPermission));
