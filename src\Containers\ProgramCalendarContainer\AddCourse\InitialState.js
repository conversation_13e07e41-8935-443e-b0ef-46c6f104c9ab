import { setMinutes, setHours } from 'date-fns';
export const nonRotational = {
  update_method: '',
  course_flow: 'normal',
  course_add_mode: 'manual',
  course_index: -1,
  modal: false,
  modal_mode: '',
  event_edit: false,
  event_index: -1,
  disable_level: false,
  disable_title: false,
  group_course: false,
  loading: true,
  set_course_events: false,
  title: '',
  select_dates: '',
  custom_dates: '',
  work_column: '',
  work_course: '',
  work_event: '',
  work_start_date: '',
  work_end_date: '',
  work_level_is_rotation: false,
  work_level_number: '',
  work_min_date: '',
  work_max_date: '',
  copy_events: [],
  copy_dates: [],
  deleted_events: [],
  copy_date_index: 0,
  academic_weeks: [],
  academic_week_start: 0,
  academic_week_end: 0,
  academic_week_start_start_date: '',
  academic_week_start_end_date: '',
  academic_week_end_start_date: '',
  academic_week_end_end_date: '',
  level_no: '',
  check_courses: false,
  add_courses: {
    course: null,
    elective: null,
    module: null,
  },
  batch_courses_id: [],
  course_events: [],
  modal_content: {
    description: '',
    event_type: 'exam',
    title: '',
    start_date: '',
    start_time: setHours(setMinutes(new Date(), 0), 8),
    end_date: '',
    end_time: setHours(setMinutes(new Date(), 0), 17),
  },
  edit: {
    level_one_title: '',
    level_one_start_date: '',
    level_one_end_date: '',
    level_one_columns: 1,
    level_one_courses: [],
    level_one_course_events: [],
    events: [],
    level_two_title: '',
    level_two_start_date: '',
    level_two_end_date: '',
    level_two_columns: 1,
    level_two_courses: [],
    level_two_course_events: [],
  },
  check: {
    pre_start: '',
    pre_end: '',
  },
  type: {
    title: '',
    sub_title: '',
    week_start: 0,
    week_end: 0,
    rotation_count: 0,
    start_date: '',
    end_date: '',
    model: '',
    _course_id: '',
    background_color: '#FFF4BD',
    events: [],
    _batch_course_id: [],
    course_name: '',
    // _id: "",
  },
  year2: {},
  year3: {},
  year4: {},
  year5: {},
  year6: {},
};
