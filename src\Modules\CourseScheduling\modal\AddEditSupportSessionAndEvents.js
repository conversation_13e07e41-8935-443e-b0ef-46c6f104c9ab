import React from 'react';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { List, Map, Set } from 'immutable';
import FormControl from '@mui/material/FormControl';
import Select from '@mui/material/Select';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import MenuItem from '@mui/material/MenuItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import { ThemeProvider } from '@mui/styles';
import DatePicker from 'react-datepicker';
import { Dialog, DialogActions } from '@mui/material';
import {
  startOfDay,
  format,
  isValid,
  endOfDay,
  set,
  //getDay,
  isWithinInterval,
  getHours,
  getMinutes,
  subMilliseconds,
} from 'date-fns';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

import {
  MUI_THEME,
  MUI_CHECKBOX_THEME,
  capitalize,
  getModes,
  isIndGroup,
  allowedTimeInterval,
  getManualStaffOption,
  isManualAttendanceEnabled,
  studentGroupRename,
  studentGroupViewList,
  // isModuleEnabled,
} from '../../../utils';
import {
  getFormattedGroupName,
  getHour,
  transformDateToCustomObject,
  getRotationDates,
} from '../components/utils';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import MaterialInput from 'Widgets/FormElements/material/Input';

const menuProps = {
  getContentAnchorEl: null,
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'center',
  },
  transformOrigin: {
    vertical: 'top',
    horizontal: 'center',
  },
  variant: 'menu',
};

function AddEditSupportSessionAndEvents({
  show,
  onHide,
  onSave,
  onChange,
  mode,
  data,
  course,
  excludedTimes,
  handleDeleteSchedule,
  supportSessionTypes,
  isRotation,
  deliveryTypes,
  programId,
}) {
  const subjects = getSubjects();
  const staffs = getStaffs();
  const infrastructures = getInfrastructures();
  const errors = getErrors();
  const manualStaffOptions = useSelector((state) =>
    state.courseScheduling.getIn(['staffScheduleOptionList', 0], Map())
  );

  function getErrors() {
    return data.get('errors', List());
  }
  function handleChange(name, value) {
    switch (name) {
      case 'schedule_date': {
        if (!value) value = '';
        onChange(name, value ? format(startOfDay(value), 'yyyy-MM-dd') : '');
        break;
      }
      case 'start':
      case 'end': {
        if (!value) {
          onChange(name, Map());
          return;
        }
        const time = transformDateToCustomObject(value);
        if (time.get('minute', NaN) % allowedTimeInterval() !== 0) return;
        onChange(name, time);
        break;
      }
      case 'sub_type': {
        onChange(name, value);
        break;
      }
      case 'subjects': {
        onChange('staffs', List());
        const selected = getAllSubjects().filter((subject) =>
          value.includes(subject.get('_subject_id'))
        );
        onChange(name, selected);
        break;
      }
      case 'staffs': {
        const selected = staffs
          .filter((staff) => value.includes(staff.get('_id')))
          .map((staff) => staff.delete('name').delete('value'));
        onChange(name, selected);
        break;
      }
      case 'mode': {
        onChange(name, value);
        break;
      }
      case 'manualStaffs': {
        onChange(name, value);
        break;
      }
      case 'mobileNumberOrEmail': {
        onChange(name, value);
        break;
      }
      default:
        onChange(name, value);
        break;
    }
  }

  function getScheduleDate() {
    const scheduleDate = data.getIn(['schedule', 'schedule_date'], null) || null;
    if (!scheduleDate) return null;
    return startOfDay(new Date(scheduleDate));
  }

  function getStartEndDate(type) {
    const date = data.getIn(['schedule', type], Map());
    if (date.isEmpty()) return null;
    return set(startOfDay(new Date()), { hours: getHour(date), minutes: date.get('minute') });
  }

  function getMinMaxDate() {
    let startDate = new Date(course.get('start_date'));
    let endDate = new Date(course.get('end_date'));
    if (isRotation) {
      const rotationDates = getRotationDates(course);
      const groupNo = schedule.get('student_groups', List()).getIn([0, 'group_no']);
      const rotationDate = rotationDates.find(
        (rotation) => rotation.get('rotation_count') === groupNo
      );
      if (!rotationDate) return {};
      startDate = rotationDate.get('start_date');
      endDate = rotationDate.get('end_date');
    }
    if (!isValid(startDate) || !isValid(endDate)) return {};
    endDate = endOfDay(endDate);
    return { minDate: startDate, maxDate: endDate };
  }

  function getAllSubjects() {
    return course.get('subjects', List());
  }

  function getSubjects() {
    return getAllSubjects().map((subject) =>
      Map({
        name: subject.get('subject_name'),
        value: subject.get('_subject_id'),
      })
    );
  }

  function getStaffs() {
    const selectedSubjects = data.getIn(['schedule', 'subjects'], List());
    if (selectedSubjects.isEmpty()) return List();
    return getAllSubjects()
      .reduce((acc, subject) => {
        return acc.concat(
          subject.get('staffs', List()).map((staff) =>
            staff.merge(
              Map({
                name: `${staff.getIn(['name', 'first'], '')} ${staff.getIn(
                  ['name', 'middle'],
                  ''
                )} ${staff.getIn(['name', 'last'], '')}`,
                value: staff.get('_id'),
                staff_name: staff.get('name'),
                _staff_id: staff.get('_id'),
              })
            )
          )
        );
      }, List())
      .reduce((acc, staff) => acc.set(staff.get('value'), staff), Map())
      .valueSeq()
      .toList();
  }

  function getInfrastructures() {
    const mode = data.getIn(['schedule', 'mode'], '');
    if (!mode) return List();
    const genders = Set(
      data
        .getIn(['existingSchedule', 'student_groups'], List())
        .map((studentGroup) => studentGroup.get('gender'))
        .filter((g) => g)
    ).toList();
    if (genders.isEmpty()) return List();
    const includeAll = genders.size !== 1;
    const gender = genders.get(0, '').toLowerCase();
    if (mode === 'remote') {
      return course
        .get('remote_scheduling', List())
        .filter((room) => {
          if (
            // room.get('yearName') === course.get('year_no') &&
            room.get('term', '').toLowerCase() === course.get('term', '').toLowerCase() &&
            room.get('levelName') === course.get('level_no')
          ) {
            return (
              room.get('gender', '').toLowerCase() === gender || room.get('gender', '') === 'both'
            );
          }
          return false;
        })
        .map((room) =>
          room.merge(
            Map({
              name: room.get('meetingTitle'),
              value: room.get('_id'),
              secondaryTitle1: `${capitalize(room.get('gender', ''))}${
                room.get('meetingUsername', '') !== null
                  ? `, ${room.get('meetingUsername', '')}`
                  : ''
              }`,
              secondaryTitle2: `${
                room.get('remotePlatform', '') === 'teams'
                  ? 'Microsoft Teams'
                  : `${room.get('meetingId', '')}, Zoom`
              }`,
            })
          )
        )
        .sort((r1, r2) => {
          const v1 = r1.get('meetingTitle', '');
          const v2 = r2.get('meetingTitle', '');
          return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
        });
    } else if (mode === 'onsite') {
      const subjects = data.getIn(['schedule', 'subjects'], List());
      if (subjects.isEmpty()) return List();
      let infrastructures = subjects.reduce(
        (acc, subject) => acc.concat(subject.get('subject_infra', List())),
        List()
      );
      if (!includeAll) {
        infrastructures = infrastructures.filter((infra) => {
          const gendersInTimeGroups = Set(
            infra
              .get('timing', List())
              .map((t) => {
                const g = t.getIn(['_time_id', 'gender']);
                if (!g) return '';
                return g.toLowerCase();
              })
              .filter((g) => g)
          ).toList();
          if (gendersInTimeGroups.isEmpty()) return false;
          return gendersInTimeGroups.includes('both') ? true : gendersInTimeGroups.includes(gender);
        });
      }
      const startDate = getStartEndDate('start');
      let endDate = getStartEndDate('end');
      return infrastructures
        .filter((item) => {
          const selectedSubType = data.getIn(['schedule', 'sub_type'], '');
          if (
            !['Counselling', 'Academic Advisor', 'Feedback', 'Training'].includes(selectedSubType)
          ) {
            const selectedDelivery = deliveryTypes.find(
              (del) => del.get('delivery_type') === selectedSubType
            );
            if (selectedDelivery !== undefined && selectedDelivery.get('_delivery_id', '') !== '') {
              const groupDeliveryId = item
                .get('delivery_type', List())
                .map((dt) => dt.get('_delivery_type_id', ''))
                .toJS();
              return groupDeliveryId.includes(selectedDelivery.get('_delivery_id', ''));
            } else {
              return item;
            }
          } else {
            return item;
          }
        })
        .filter((infra) => {
          let incInfra = false;
          infra.get('timing', List()).forEach((t) => {
            if (incInfra) return;
            let infraStartDate = new Date(t.getIn(['_time_id', 'start_time']));
            let infraEndDate = new Date(t.getIn(['_time_id', 'end_time']));
            if (!isValid(startDate) || !isValid(endDate)) return;
            if (!isValid(infraStartDate) || !isValid(infraEndDate)) return;
            endDate = subMilliseconds(endDate, 1);
            infraStartDate = set(startDate, {
              hours: getHours(infraStartDate),
              minutes: getMinutes(infraStartDate),
              seconds: 0,
              milliseconds: 0,
            });
            infraEndDate = subMilliseconds(
              set(endDate, {
                hours: getHours(infraEndDate),
                minutes: getMinutes(infraEndDate),
                seconds: 0,
                milliseconds: 0,
              }),
              1
            );
            incInfra =
              infraStartDate > infraEndDate
                ? true
                : isWithinInterval(startDate, { start: infraStartDate, end: infraEndDate }) &&
                  isWithinInterval(endDate, { start: infraStartDate, end: infraEndDate });
          });
          return incInfra;
        })
        .map((infra) =>
          infra.merge(
            Map({
              value: infra.get('_id'),
              secondaryTitle1: `${infra.get('floor_no', '')}, ${
                infra.get('zone', List()).join(', ') || 'NA'
              }, ${infra.get('room_no', '')}`,
              secondaryTitle2: `${infra.get('building_name', '')}`,
              outsideCampus: infra.get('outsideCampus', false),
            })
          )
        )
        .reduce((acc, infra) => acc.set(infra.get('_id'), infra), Map())
        .valueSeq()
        .toList()
        .sort((i1, i2) => {
          const v1 = i1.get('name', '');
          const v2 = i2.get('name', '');
          return new Intl.Collator('en', { numeric: true, sensitivity: 'accent' }).compare(v1, v2);
        });
    }
    return List();
  }

  function getSelectedSubjects() {
    return data
      .getIn(['schedule', 'subjects'], List())
      .map((subject) => subject.get('_subject_id'))
      .toJS();
  }

  function getSelectedStaffs() {
    return data
      .getIn(['schedule', 'staffs'], List())
      .map((subject) => subject.get('_id'))
      .toJS();
  }

  function getSelectedInfrastructure() {
    return data.getIn(['schedule', '_infra_id'], '');
  }

  const selectedSubjects = getSelectedSubjects();
  const selectedStaffs = getSelectedStaffs();
  const transformedErrors = errors.reduce(
    (acc, err) => acc.set(err.get('field', ''), err.get('message', '')),
    Map()
  );
  const isSupportSession = data.getIn(['schedule', 'type']) === 'support_session';
  const schedule = data.get('existingSchedule', Map());
  // const programType = [
  //   {
  //     name: 'Mobile Number *',
  //     value: 'Mobile Number',
  //   },
  //   {
  //     name: 'Email *',
  //     value: 'Email',
  //   },
  // ];
  // const withoutOutlineSelect = {
  //   '& .MuiOutlinedInput-input': {
  //     padding: '0px',
  //   },
  //   '&.MuiOutlinedInput-root': {
  //     width: '10em',
  //     '& fieldset': {
  //       border: 'none',
  //       color: 'red',
  //     },
  //     '&:hover fieldset': {
  //       border: 'none',
  //     },
  //     '&.Mui-focused fieldset': {
  //       border: 'none',
  //     },
  //   },
  // };
  return (
    <Dialog fullWidth={true} maxWidth={'md'} open={show} onClose={onHide}>
      <div className="p-3">
        <div className="border-bottom mb-3">
          <p className="mb-2">
            {' '}
            {`${isSupportSession ? t('session_title') : t('event_title_type')} - `}
            <b>{schedule.get('title', 'N/A')}</b>
            <b>{!isSupportSession ? ' / ' + capitalize(schedule.get('sub_type', 'N/A')) : ''}</b>
          </p>
          <p className="mb-2">
            {t('student_grouping.course_groups')} -{' '}
            <b>
              {schedule.get('type', '') !== 'regular'
                ? studentGroupViewList(schedule.get('student_groups', List()), programId)
                    .entrySeq()
                    .map(([groupName, sGroup], index, array) => {
                      return (
                        <span key={groupName} className="mr-2 mb-1">
                          <span>
                            {getFormattedGroupName(studentGroupRename(groupName, programId), 2)}
                            {sGroup.get('delivery_symbol', '') !== '' &&
                              `-${sGroup.get('session_group')}`}
                          </span>
                          {index !== array.size - 1 && ' • '}
                        </span>
                      );
                    })
                : schedule
                    .get('student_groups', List())
                    .reduce(
                      (sgAcc, sg) =>
                        sgAcc.push(
                          getFormattedGroupName(
                            studentGroupRename(sg.get('group_name', ''), programId),
                            isIndGroup(sg.get('group_name', '')) ? 1 : 2
                          )
                        ),
                      List()
                    )
                    .join(' • ')}
            </b>
          </p>
        </div>

        <div className="row">
          <div className="col-md-6">
            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'Date'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <div className="schedule-date-picker-container">
                  <DatePicker
                    onChange={(date) => handleChange('schedule_date', date)}
                    selected={getScheduleDate()}
                    className="schedule-date-picker-input"
                    dateFormat="dd/MM/yyyy"
                    // filterDate={(date) => {
                    //   const day = getDay(date);
                    //   return ![5, 6].includes(day);
                    // }}
                    {...getMinMaxDate()}
                  />
                </div>
              </div>
            </div>

            {data.getIn(['schedule', 'type']) === 'support_session' && (
              <div className="row align-items-center pt-2 pb-2">
                <div className="col-md-5">
                  <div className="f-16">
                    <Trans i18nKey={'type'}></Trans> *
                  </div>
                </div>
                <div className="col-md-7">
                  <FormControl fullWidth variant="outlined" size="small">
                    <Select
                      native
                      value={data.getIn(['schedule', 'sub_type'], '')}
                      onChange={(e) => handleChange('sub_type', e.target.value)}
                    >
                      <option value=""></option>
                      {supportSessionTypes.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.name}
                        </option>
                      ))}
                    </Select>
                  </FormControl>
                </div>
              </div>
            )}

            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'configuration.subject'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <FormControl fullWidth variant="outlined" size="small">
                  <Select
                    multiple
                    value={selectedSubjects}
                    onChange={(e) => handleChange('subjects', e.target.value)}
                    MenuProps={{
                      ...menuProps,
                      PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                    }}
                    renderValue={(selected) =>
                      subjects
                        .reduce((acc, group) => {
                          if (!selected.includes(group.get('value'))) return acc;
                          return acc.push(group.get('name'));
                        }, List())
                        .join(', ')
                    }
                  >
                    {subjects.map((option) => (
                      <MenuItem
                        className="white-space-normal"
                        key={option.get('value')}
                        value={option.get('value')}
                      >
                        <ListItemIcon>
                          <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                            <Checkbox
                              color="primary"
                              checked={selectedSubjects.indexOf(option.get('value')) > -1}
                              size="small"
                            />
                          </ThemeProvider>
                        </ListItemIcon>
                        <ListItemText primary={option.get('name')} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </div>
            </div>

            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'user_management.staff'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <FormControl
                  fullWidth
                  variant="outlined"
                  size="small"
                  error={transformedErrors.get('staff', '').length !== 0}
                >
                  <Select
                    multiple
                    value={selectedStaffs}
                    onChange={(e) => handleChange('staffs', e.target.value)}
                    MenuProps={{
                      ...menuProps,
                      PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                    }}
                    renderValue={(selected) =>
                      staffs
                        .reduce((acc, group) => {
                          if (!selected.includes(group.get('value'))) return acc;
                          return acc.push(group.get('name'));
                        }, List())
                        .join(', ')
                    }
                  >
                    {staffs
                      .sort((a, b) => (a.get('name', '') > b.get('name', '') ? 1 : -1))
                      .map((option) => (
                        <MenuItem
                          className="white-space-normal"
                          key={option.get('value')}
                          value={option.get('value')}
                        >
                          <ListItemIcon>
                            <ThemeProvider theme={MUI_CHECKBOX_THEME}>
                              <Checkbox
                                color="primary"
                                checked={selectedStaffs.indexOf(option.get('value')) > -1}
                                size="small"
                              />
                            </ThemeProvider>
                          </ListItemIcon>
                          <ListItemText primary={option.get('name')} />
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
              </div>
            </div>
            {isManualAttendanceEnabled() &&
              data.getIn(['schedule', 'mode'], '') !== 'remote' &&
              manualStaffOptions.get('manualAttendance', false) &&
              manualStaffOptions.get('assignedStaffIds', List()).size > 0 && (
                <div className="row align-items-center pt-2 pb-2">
                  <div className="col-md-5">
                    <div className="f-16">Attendance taking Staff</div>
                  </div>
                  <div className="col-md-7">
                    <MaterialInput
                      elementType={'materialSelectMultiple'}
                      disabled={mode === 'view'}
                      type={'text'}
                      variant={'outlined'}
                      size={'small'}
                      elementConfig={{
                        options: getManualStaffOption(
                          manualStaffOptions.get('assignedStaffIds', List())
                        ).toJS(),
                      }}
                      label={''}
                      labelclass={'mb-0 f-14'}
                      changed={(e) => handleChange('manualStaffs', e.target.value)}
                      isAllSelected={false}
                      value={data.getIn(['schedule', 'manualStaffs'], List()).toJS()}
                      multiple={true}
                    />
                  </div>
                </div>
              )}
            {/* {isModuleEnabled('OUTSIDE_CAMPUS') && (
              <Fragment>
                {data.getIn(['schedule', 'outsideCampus'], false) ? (
                  <div className="row align-items-center pt-2 pb-2">
                    <div className="col-md-5">
                      <div className="f-16">
                        <MaterialInput
                          elementType={'materialSelect'}
                          type={'text'}
                          variant={'outlined'}
                          size={'small'}
                          sx={withoutOutlineSelect}
                          elementConfig={{ options: programType }}
                          changed={(e) => handleChange('selectNumOrEmail', e.target.value)}
                          value={data.getIn(['schedule', 'selectNumOrEmail'], '')}
                        />
                      </div>
                    </div>
                    <div className="col-md-7">
                      <MaterialInput
                        elementType={'materialInput'}
                        type={'text'}
                        variant={'outlined'}
                        size={'small'}
                        changed={(e) => handleChange('mobileNumberOrEmail', e.target.value)}
                        value={data.getIn(['schedule', 'mobileNumberOrEmail'], '')}
                      />
                    </div>
                  </div>
                ) : (
                  ''
                )}
              </Fragment>
            )} */}
          </div>

          <div className="col-md-6">
            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'dashboard_view.time'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <div className="row">
                  <div className="col-md-6 pr-1">
                    <div className="schedule-date-picker-container">
                      <DatePicker
                        onChange={(date) => handleChange('start', date)}
                        selected={getStartEndDate('start')}
                        className="schedule-date-picker-input"
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={allowedTimeInterval()}
                        timeCaption="Start time"
                        dateFormat="h:mm aa"
                        excludeTimes={excludedTimes}
                      />
                    </div>
                  </div>

                  <div className="col-md-6 pl-1">
                    <div className="schedule-date-picker-container">
                      <DatePicker
                        onChange={(date) => handleChange('end', date)}
                        selected={getStartEndDate('end')}
                        className="schedule-date-picker-input"
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={allowedTimeInterval()}
                        timeCaption="End time"
                        dateFormat="h:mm aa"
                        excludeTimes={excludedTimes}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'configure_levels.mode'}></Trans> *
                </div>
              </div>
              <div className="col-md-7">
                <FormControl fullWidth variant="outlined" size="small">
                  <Select
                    native
                    value={data.getIn(['schedule', 'mode'], '')}
                    onChange={(e) => handleChange('mode', e.target.value)}
                  >
                    <option value=""></option>
                    {getModes(programId).map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.name}
                      </option>
                    ))}
                  </Select>
                </FormControl>
              </div>
            </div>

            <div className="row align-items-center pt-2 pb-2">
              <div className="col-md-5">
                <div className="f-16">
                  <Trans i18nKey={'infrastructure'}></Trans>{' '}
                  {data.getIn(['schedule', 'mode'], '') === 'remote' && '*'}
                </div>
              </div>
              <div className="col-md-7">
                <FormControl
                  fullWidth
                  variant="outlined"
                  size="small"
                  error={transformedErrors.get('infra', '').length !== 0}
                >
                  <Select
                    value={infrastructures.isEmpty() ? '' : getSelectedInfrastructure()}
                    onChange={(e) => {}}
                    MenuProps={{
                      ...menuProps,
                      PaperProps: { style: { maxWidth: 350, maxHeight: 200 } },
                    }}
                    displayEmpty
                    renderValue={(value) => {
                      const infra =
                        infrastructures.find((infra) => infra.get('value') === value) || Map();
                      return infra.get('name', '');
                    }}
                  >
                    {!infrastructures.isEmpty() && (
                      <MenuItem value="" onClick={() => handleChange('infra', Map())}>
                        <ListItemText primary="Unselect" />
                      </MenuItem>
                    )}
                    {infrastructures.map((option) => (
                      <MenuItem
                        key={option.get('value')}
                        value={option.get('value')}
                        className="white-space-normal"
                        onClick={() => handleChange('infra', option)}
                      >
                        <ListItemText
                          primary={option.get('name')}
                          secondary={
                            <span className="d-block">
                              <span className="d-block">{option.get('secondaryTitle1', '')}</span>
                              <span className="d-block">{option.get('secondaryTitle2', '')}</span>
                            </span>
                          }
                        />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </div>
            </div>
          </div>
        </div>

        {!errors.isEmpty() && (
          <div className="color-red f-14 mt-2 mb-2">
            <div>
              <Trans i18nKey={'note'}></Trans>:-
            </div>
            {errors.map((err, i) => (
              <div key={`${err.get('field')}-${i}`}>{`- ${studentGroupRename(
                err.get('message', ''),
                programId
              )}`}</div>
            ))}
          </div>
        )}

        <DialogActions className={'mt-3'}>
          <ThemeProvider theme={MUI_THEME}>
            <div className="d-flex justify-content-end">
              {mode === 'update' &&
                CheckPermission(
                  'subTabs',
                  'Schedule Management',
                  'Course Scheduling',
                  '',
                  'Schedule',
                  '',
                  'Course Schedule',
                  'Delete'
                ) && (
                  <div className="mr-2" style={{ position: 'absolute', left: '15px' }}>
                    <Button
                      variant="outlined"
                      color="primary"
                      className="text-uppercase"
                      onClick={handleDeleteSchedule}
                    >
                      <Trans i18nKey={'delete'}></Trans>
                    </Button>
                  </div>
                )}

              <div className="mr-2">
                <Button
                  variant="outlined"
                  color="primary"
                  className="text-uppercase"
                  onClick={onHide}
                >
                  <Trans i18nKey={'cancel'}></Trans>
                </Button>
              </div>
              <div>
                <Button
                  variant="contained"
                  color="primary"
                  className="text-uppercase"
                  onClick={() => onSave(mode)}
                >
                  <Trans i18nKey={'schedule'}></Trans>
                </Button>
              </div>
            </div>
          </ThemeProvider>
        </DialogActions>
      </div>
    </Dialog>
  );
}

AddEditSupportSessionAndEvents.propTypes = {
  show: PropTypes.bool,
  onHide: PropTypes.func,
  onSave: PropTypes.func,
  onChange: PropTypes.func,
  mode: PropTypes.string,
  data: PropTypes.instanceOf(Map),
  course: PropTypes.instanceOf(Map),
  activeSessionFlow: PropTypes.instanceOf(Map),
  excludedTimes: PropTypes.array,
  sessionFlowList: PropTypes.instanceOf(List),
  scheduleList: PropTypes.instanceOf(List),
  handleDeleteSchedule: PropTypes.func,
  supportSessionTypes: PropTypes.array,
  isRotation: PropTypes.bool,
  deliveryTypes: PropTypes.instanceOf(List),
  programId: PropTypes.string,
};

export default AddEditSupportSessionAndEvents;
