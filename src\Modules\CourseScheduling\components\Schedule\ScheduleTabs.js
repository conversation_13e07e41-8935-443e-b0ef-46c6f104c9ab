import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import { Badge, Chip, Paper } from '@mui/material';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';

import CloseIcon from '@mui/icons-material/Close';
import { List, Map } from 'immutable';
// import { isModuleEnabled } from '../../../../utils';
import {
  getExternalStaff,
  postClassLeader,
  mergeScheduledClassLeader,
} from '_reduxapi/course_scheduling/action';
import StaffAndClassLeader from './StaffAndClassLeader';
import { formattedFullName } from 'Modules/ReportsAndAnalytics/utils';
import ExternalStaff from './ExternalStaff';
import { scheduledExternalStaff } from '_reduxapi/course_scheduling/action';
function CustomTabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <div>{children}</div>}
    </div>
  );
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function ScheduleTabs({
  staffs,
  data,
  infrastructures,
  handleChanges,
  setModalData,
  courseData,
  programId,
  institutionCalendarId,
  courseId,
  classLeaderData,
  GetPostClassLeader,
  scheduleId,
  checkUpdateExternal,
  externalstaff,
  type,
  mode,
  setData,
}) {
  const [value, setValue] = useState(0);
  const dispatch = useDispatch();
  const scheduleData = data.get('schedule', Map());
  const requestBodyClassLeader = {
    _student_group_id: data.get('studentGroupId'),
    level_no: courseData.get('level_no'),
    term: courseData.get('term'),
    _course_id: courseId,
    student_groups: scheduleData
      .get('student_groups', List())
      .map((studentGroup) => {
        return studentGroup
          .set('group_id', studentGroup.get('_id'))
          .delete('_id')
          .delete('schedule_status')
          .delete('students')
          .set(
            'session_group',
            studentGroup
              .get('session_group', List())
              .map((sessionGroup) =>
                sessionGroup.set('session_group_id', sessionGroup.get('_id')).delete('_id')
              )
          );
      })
      .toJS(),
  };
  useEffect(() => {
    if (mode === 'update') {
      dispatch(scheduledExternalStaff(scheduleId));
    }
    if (type === 'RegularType') {
      dispatch(postClassLeader(requestBodyClassLeader));
    }
    if (type === 'MergeType') {
      dispatch(
        mergeScheduledClassLeader({
          scheduleIdList: data
            .get('scheduleList', List())
            .map((s) => s.get('_id'))
            .toJS(),
        })
      );
    }
    if (['create', 'update'].includes(mode)) {
      dispatch(getExternalStaff());
    }
  }, []); //eslint-disable-line

  // const getscheduledExternalStaff = useSelector(selectscheduledExternalStaff);
  // const checkDataUpdateExternal = getscheduledExternalStaff.get('externalStaffs', List());

  const handleChange10 = (_, newValue) => {
    setValue(newValue);
  };

  const handleDeleteChip = (index, type) => {
    setModalData((prevSelectedStaff) => {
      return prevSelectedStaff.deleteIn(
        ['schedule', type === 'AcademicStaff' ? 'checkStaff' : 'checkclassLeaderData', index],
        ''
      );
    });
  };

  let updatedCheckUpdateExternal = checkUpdateExternal;

  const handleExternalDeleteChip = (checkUpdateDataId) => {
    updatedCheckUpdateExternal = checkUpdateExternal.filter(
      (item) => item.get('_id') !== checkUpdateDataId
    );
    setModalData((prev) =>
      prev.updateIn(['schedule', 'checkExternalList'], (list) =>
        list.map((item) =>
          item.get('_id') === checkUpdateDataId ? item.set('isChecked', false) : item
        )
      )
    );
  };

  const tabColor = {
    '&.Mui-selected': { color: '#F9FAFB !important', backgroundColor: '#147AFC' },
  };
  const classNameTabs = {
    borderRadius: '4px',
    justifyContent: 'space-between',
    '&.MuiTabs-root': { borderBottom: '0px !important' },
    '.MuiTabs-indicator': {
      display: 'none',
    },
  };
  const badgeClass = {
    '& .MuiBadge-badge': {
      backgroundColor: '#EFF9FB',
      color: '#4B5563',
      padding: '6px 4px 6px 4px',
      height: '28px',
      borderRadius: '15px',
      width: '29px',
    },
  };

  const checkStaff = data.getIn(['schedule', 'checkStaff'], List());
  const ClassLeader = data.getIn(['schedule', 'checkclassLeaderData'], List());
  const checkExternalDatas = data.getIn(['schedule', 'checkExternalList'], List());
  const filteredClassLeaderList = GetPostClassLeader.filter((item) => {
    const classLeaderIds = ClassLeader.map((leaderItem) => leaderItem.get('_staff_id', ''));
    return classLeaderIds.includes(item.get('_id', ''));
  });

  return (
    <div>
      {infrastructures.size !== 0 ? (
        <>
          {data.getIn(['schedule', 'outsideCampus'], false) ? (
            <div className="px-3">
              <div className="w-100">
                <div className="top-sticky-drawer pb-2">
                  <div className="f-16 mt-3 mb-2">Attendance Taking User</div>
                  <Paper elevation={3}>
                    <Tabs value={value} onChange={handleChange10} sx={classNameTabs}>
                      <Tab
                        sx={tabColor}
                        label={
                          <div className="d-flex align-items-center">
                            <div className="text-capitalize">Academic Staff</div>
                            {checkStaff.size !== 0 ? (
                              <Badge
                                badgeContent={
                                  checkStaff.size >= 10 ? checkStaff.size : `0${checkStaff.size}`
                                }
                                className="ml-3 pl-1"
                                color="primary"
                                sx={badgeClass}
                              />
                            ) : (
                              ''
                            )}
                          </div>
                        }
                        className="flex-grow-1 border-right"
                      />
                      <Tab
                        sx={tabColor}
                        label={
                          <div className="d-flex align-items-center">
                            <div className="text-capitalize">Class Leader</div>
                            {filteredClassLeaderList.size !== 0 ? (
                              <Badge
                                badgeContent={
                                  filteredClassLeaderList.size >= 10
                                    ? filteredClassLeaderList.size
                                    : `0${filteredClassLeaderList.size}`
                                }
                                className="ml-3 pl-1"
                                color="primary"
                                sx={badgeClass}
                              />
                            ) : (
                              ''
                            )}
                          </div>
                        }
                        className="flex-grow-1 border-right"
                      />
                      <Tab
                        sx={tabColor}
                        label={
                          <div className="d-flex align-items-center">
                            <div className="text-capitalize">External Staff </div>
                            {checkUpdateExternal.size !== 0 ? (
                              <Badge
                                badgeContent={
                                  checkUpdateExternal.size >= 10
                                    ? checkUpdateExternal.size
                                    : `0${checkUpdateExternal.size}`
                                }
                                className="ml-3 pl-1"
                                color="primary"
                                sx={badgeClass}
                              />
                            ) : (
                              ''
                            )}
                          </div>
                        }
                        className="flex-grow-1 border-right"
                      />
                    </Tabs>
                  </Paper>
                </div>
                <CustomTabPanel value={value} index={0}>
                  <div>
                    <StaffAndClassLeader
                      staffs={staffs}
                      handleChanges={handleChanges}
                      checkStaff={checkStaff}
                      type={'staffs'}
                    />
                  </div>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={1}>
                  <div>
                    <StaffAndClassLeader
                      staffs={classLeaderData}
                      handleChanges={handleChanges}
                      ClassLeader={ClassLeader}
                      type={'ClassLeader'}
                    />
                  </div>
                </CustomTabPanel>
                <CustomTabPanel value={value} index={2}>
                  <>
                    <ExternalStaff
                      programId={programId}
                      courseId={courseId}
                      institutionCalendarId={institutionCalendarId}
                      checkExternalDatas={checkExternalDatas}
                      setModalData={setModalData}
                      scheduleId={scheduleId}
                      checkUpdateExternal={checkUpdateExternal}
                      data={data}
                      setDataMsg={setData}
                    />
                  </>
                </CustomTabPanel>
                <div className="mt-2 mx-3">
                  {checkStaff.size !== 0 ? (
                    <>
                      <div className="colorLiteGreen">Academic Staff:</div>
                      <div className="row  align-items-center mt-0 ht-4 mx-2">
                        {checkStaff.map((staff, index) => (
                          <Chip
                            key={index}
                            sx={{ backgroundColor: '#EFF9FB' }}
                            className="mr-2 col-3 mb-2"
                            onDelete={() => handleDeleteChip(index, 'AcademicStaff')}
                            deleteIcon={<CloseIcon sx={{ color: '#4d4242 !important' }} />}
                            label={<div className="pr-1">{staff.get('name', '')}</div>}
                          />
                        ))}
                      </div>
                    </>
                  ) : (
                    ''
                  )}
                </div>
                <div className="mt-2 mx-3">
                  {filteredClassLeaderList.size !== 0 ? (
                    <>
                      <div className="colorLiteGreen">Class Leader:</div>
                      <div className="row  align-items-center mt-1 ht-4 mx-2">
                        {filteredClassLeaderList.map((classLeaderItem, index) => (
                          <Chip
                            key={index}
                            sx={{ backgroundColor: '#EFF9FB' }}
                            className="mr-2 col-3 mb-2"
                            onDelete={() => handleDeleteChip(index, 'ClassLeader')}
                            deleteIcon={<CloseIcon sx={{ color: '#4d4242 !important' }} />}
                            label={
                              <div className="pr-1">
                                {formattedFullName(classLeaderItem.get('name', Map()).toJS())
                                  .replace(/\s+/g, ' ')
                                  .trim()}
                              </div>
                            }
                          />
                        ))}
                      </div>
                    </>
                  ) : (
                    ''
                  )}
                </div>
                <div className="mt-2 mx-3">
                  {updatedCheckUpdateExternal.size !== 0 ? (
                    <>
                      <div className="colorLiteGreen">External Staff:</div>
                      <div className="row  align-items-center mt-1 ht-4 mx-2">
                        {updatedCheckUpdateExternal.map((checkUpdateData, index) => {
                          const checkUpdateDataId = checkUpdateData.get('_id', '');
                          return (
                            <Chip
                              key={index}
                              sx={{ backgroundColor: '#EFF9FB' }}
                              className="mr-2 col-3 mb-2"
                              onDelete={() => handleExternalDeleteChip(checkUpdateDataId)}
                              deleteIcon={<CloseIcon sx={{ color: '#4d4242 !important' }} />}
                              label={<div className="pr-1">{checkUpdateData.get('name', '')}</div>}
                            />
                          );
                        })}
                      </div>
                    </>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            </div>
          ) : (
            ''
          )}
        </>
      ) : (
        ''
      )}
    </div>
  );
}

ScheduleTabs.propTypes = {
  staffs: PropTypes.func,
  data: PropTypes.instanceOf(Map),
  infrastructures: PropTypes.func,
  handleChanges: PropTypes.func,
  setModalData: PropTypes.func,
  courseData: PropTypes.instanceOf(Map),
  programId: PropTypes.string,
  institutionCalendarId: PropTypes.string,
  courseId: PropTypes.string,
  classLeaderData: PropTypes.instanceOf(List),
  GetPostClassLeader: PropTypes.instanceOf(List),
  scheduleId: PropTypes.string,
  checkUpdateExternal: PropTypes.instanceOf(List),
  externalstaff: PropTypes.instanceOf(List),
  type: PropTypes.string,
  mode: PropTypes.string,
  setData: PropTypes.func,
};

export default ScheduleTabs;
