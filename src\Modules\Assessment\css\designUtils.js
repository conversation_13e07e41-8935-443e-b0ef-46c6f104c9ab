import { makeStyles } from '@mui/styles';

export const useStylesFunction = makeStyles(() => ({
  accordionBorderNone: {
    boxShadow: 'none',
    borderRadius: 'unset !important',
  },
  accordionBackgroundGray: {
    boxShadow: 'none',
    borderRadius: 'unset !important',
    background: '#F9FAFB !important',
  },
  accordionArrowPosition: {
    '&.MuiButtonBase-root': {
      padding: '0px',
      flexDirection: 'row-reverse',
      marginLeft: '-16px',
    },
    '&.MuiAccordionSummary-content.Mui-expanded': {
      margin: '10px 0',
    },
  },
  indicator: {
    backgroundColor: '#147AFC',
  },
  tabTitle: {
    fontSize: '16px',
    textTransform: 'capitalize',
  },
  accordionBorderUnset: {
    boxShadow: 'none',
    borderBottom: '1px solid #E5E7EB',
    borderRadius: 'unset !important',
    background: 'none',
  },
  accordionBorderInnerUnset: {
    boxShadow: 'none',
    borderRadius: 'unset !important',
    background: 'none',
    '&:before': { height: '0px !important' },
  },
  accordionBackgroundNone: {
    boxShadow: 'none',
    borderBottom: 'none',
    borderRadius: 'unset !important',
    background: 'none',
  },
  green: {
    color: '#4ADE80 !important',
    '&$checked': {
      color: '#4ADE80 !important',
    },
  },
  popupIndicator: {
    color: 'blue',
  },
  popOverScrollBar: {
    '& .MuiPaper-root': {
      '&::-webkit-scrollbar': {
        width: '0.5em',
      },
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: '#e2dbde',
        borderRadius: '4px',
      },
    },
  },
  newTextFile: {
    padding: '0px',
    width: 'auto',
    boxShadow: 'none',
    border: '1px solid #c4c4c4',
    borderRadius: '6px',
    maxHeight: '40px',
  },
}));
