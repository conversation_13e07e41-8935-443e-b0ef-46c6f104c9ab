import { connect } from 'react-redux';
import { compose } from 'redux';
import { Switch } from 'react-router';
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { withRouter, Link, Route } from 'react-router-dom';
import Breadcrumb from 'Widgets/Breadcrumb/v2/Breadcrumb';
import Loader from 'Widgets/Loader/Loader';
import SnackBars from '../../Modules/Utils/Snackbars';
import GlobalConfig from './components/GlobalConfiguration';
import Program from './components/Program';
import College from './components/College';
import GlobalConfigSidebar from './components/GlobalConfigSidebar';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import { selectPgmInput } from '_reduxapi/program_input/v2/selectors';
import { getPgmInput } from '_reduxapi/program_input/v2/actions';

import { selectIsLoading as selectCollegesLoading } from '_reduxapi/institution/selectors';
import {
  selectIsLoading,
  selectMessage,
  selectBreadCrumb,
} from '_reduxapi/global_configuration/selectors';
import { selectIsLoading as selectIsProgramLoading } from '_reduxapi/program_input/v2/selectors';
import * as actions from '_reduxapi/global_configuration/actions';
import { getLang } from '../../utils';
import { getInstitutionHeader } from 'v2/utils';
import parentContext from './programTable/Context/Context';
import GlobalTableIndex from './programTable/globalTableIndex';
import i18n from '../../i18n';
class GlobalConfiguration extends Component {
  constructor(props) {
    super(props);
    const {
      match: {
        params: { type, id, name },
      },
    } = props;
    this.state = {
      type,
      id,
      name,
    };
  }
  history = this.props;
  params = { pageNo: 1, limit: 10, tab: '' };
  institutionHeader = getInstitutionHeader(this.history);
  //institutionId = dString('NjI5NjA2NTAwZTMyMzkyNWMzZTc5Y2Ri');
  institutionId = this.institutionHeader?._institution_id;
  componentDidMount() {
    this.props.setBreadCrumb([{ to: '#', label: i18n.t('side_nav.menus.global_configuration') }]);
    getPgmInput({
      institutionId: this.institutionId,
      params: this.params,
      header: this.institutionHeader,
    });
  }

  render() {
    const {
      message,
      breadcrumbs,
      loggedInUserData,
      programInputList,
      isLoading,
      isProgramLoading,
      isCollegesLoading,
    } = this.props;
    const items = [];

    if (breadcrumbs && breadcrumbs.size > 0) {
      breadcrumbs.map((bread) => {
        items.push({ to: bread.get('to'), label: bread.get('label') });
        return bread;
      });
    }
    return (
      <parentContext.PIL_Context.Provider value={{ programInputList }}>
        {message !== '' && <SnackBars show={true} message={message} />}
        <Loader isLoading={isLoading || isProgramLoading || isCollegesLoading} />
        <Breadcrumb>
          {items.map(({ to, label }) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
              {label}
            </Link>
          ))}
        </Breadcrumb>
        <div className="pb-5 bg-white">
          <div className="container-fluid">
            <div className="row">
              {/* <div className="col-md-2 p-0 si"> */}

              <div
                className={`col-md-3 col-xl-2 col-lg-2 col-sm-3 col-3 p-0 pi_sidebar ${
                  getLang() === 'ar' ? 'si-ar' : 'rightBorder'
                }`}
              >
                <GlobalConfigSidebar {...this.state} loggedInUserData={loggedInUserData} />
              </div>
              <div className="col-md-9 col-xl-10 col-lg-10 col-sm-9 col-9">
                <Switch>
                  <Route
                    path={`/:type/:id/:name/global-configuration/program`}
                    exact
                    render={() => {
                      return <GlobalTableIndex history={this.history} />;
                    }}
                  />
                  <Route
                    path={`/:type/:id/:name/global-configuration/u-sity`}
                    exact
                    render={() => {
                      return <GlobalConfig {...this.state} />;
                    }}
                  />
                  <Route
                    exact
                    path={`/:type/:id/:name/global-configuration/program/:programID/view`}
                    render={() => {
                      return <Program {...this.state} />;
                    }}
                  ></Route>
                  <Route
                    exact
                    path={`/:type/:id/:name/global-configuration/college`}
                    render={() => {
                      return <College {...this.state} loggedInUserData={loggedInUserData} />;
                    }}
                  ></Route>
                </Switch>
              </div>
            </div>
          </div>
        </div>
      </parentContext.PIL_Context.Provider>
    );
  }
}

GlobalConfiguration.propTypes = {
  isLoading: PropTypes.bool,
  isCollegesLoading: PropTypes.bool,
  message: PropTypes.string,
  history: PropTypes.object,
  setBreadCrumb: PropTypes.func,
  breadcrumbs: PropTypes.object,
  match: PropTypes.object,
  loggedInUserData: PropTypes.object,
  programInputList: PropTypes.object,
  isProgramLoading: PropTypes.bool,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    isCollegesLoading: selectCollegesLoading(state),
    isProgramLoading: selectIsProgramLoading(state),
    message: selectMessage(state),
    breadcrumbs: selectBreadCrumb(state),
    loggedInUserData: selectUserInfo(state),
    programInputList: selectPgmInput(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(GlobalConfiguration);
