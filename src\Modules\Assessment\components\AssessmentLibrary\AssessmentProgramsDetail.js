import React, { useEffect } from 'react';
import { Route, Switch } from 'react-router';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { withRouter, useHistory } from 'react-router-dom';

import { Header } from './Header';
import Sidebar from './Sidebar';
import AssessmentDashboard from './AssessmentDashboard';
import AssessmentPlanningDashboard from './AssessmentPlanning';
import AssessmentProgramLevel from './AssessmentProgramLevel';
import AssessmentCourseList from './AllCourses';
import AssessmentMarks from './AssessmentMarks';

import * as actions from '../../../../_reduxapi/assessment/action';
import {
  selectAssessmentDashboard,
  selectAssessmentTerm,
} from '../../../../_reduxapi/assessment/selector';
import { selectActiveInstitutionCalendar } from '../../../../_reduxapi/Common/Selectors';
import { getURLParams } from '../../../../utils';

function AssessmentProgramDetails(props) {
  const {
    activeInstitutionCalendar,
    getAssessmentPlanningDashboard,
    programId,
    programName,
    assessmentDashboardDetails,
    assessmentTerm,
    getAssessmentPlanningByTerm,
  } = props;
  const history = useHistory();
  const institutionCalendar = activeInstitutionCalendar.get('_id', '');

  useEffect(() => {
    if (programId !== '' && institutionCalendar !== '') {
      const term = getURLParams('term', true);
      getAssessmentPlanningDashboard(programId, institutionCalendar, term);
    }
  }, [getAssessmentPlanningDashboard, programId, institutionCalendar]);

  const isConfigurePages = history?.location?.pathname.includes(
    'assessment-management/assessment_details/configure'
  );
  function getAssessmentTerm(term) {
    getAssessmentPlanningByTerm(programId, institutionCalendar, term);
  }
  return (
    <div className="main pb-5 bg-mainBackground">
      {!isConfigurePages && (
        <Header
          history={history}
          programName={programName + ' Program'}
          isConfigurePages={isConfigurePages}
          type={'pgm'}
        />
      )}
      <div className={`${isConfigurePages ? 'pl-0 pr-0' : ''}`}>
        <div className="d-flex">
          {!isConfigurePages && (
            <Sidebar
              history={history}
              assessmentDashboardDetails={assessmentDashboardDetails}
              callTermFunction={getAssessmentTerm}
            />
          )}
          <div className="w-100">
            <Switch>
              <Route
                exact
                path="/assessment-management/assessment_details"
                render={() => {
                  return <AssessmentDashboard assessmentTerm={assessmentTerm} />;
                }}
              />
              <Route
                path="/assessment-management/assessment_details/planning"
                render={() => {
                  return (
                    <AssessmentPlanningDashboard
                      programId={programId}
                      activeInstitutionCalendar={activeInstitutionCalendar}
                      showMarks={false}
                      history={history}
                    />
                  );
                }}
              />
              <Route
                exact
                path="/assessment-management/assessment_details/configure"
                render={() => {
                  return (
                    <AssessmentProgramLevel
                      programId={programId}
                      activeInstitutionCalendar={activeInstitutionCalendar}
                    />
                  );
                }}
              />
              <Route
                exact
                path="/assessment-management/assessment_details/configure/courses"
                render={() => {
                  return (
                    <AssessmentCourseList
                      programId={programId}
                      activeInstitutionCalendar={activeInstitutionCalendar}
                    />
                  );
                }}
              />
              <Route
                exact
                path="/assessment-management/assessment_details/configure/marks"
                render={() => {
                  return (
                    <AssessmentMarks
                      programId={programId}
                      activeInstitutionCalendar={activeInstitutionCalendar}
                      programName={programName + ' Program'}
                    />
                  );
                }}
              />
            </Switch>
          </div>
        </div>
      </div>
    </div>
  );
}
const mapStateToProps = function (state) {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    assessmentDashboardDetails: selectAssessmentDashboard(state),
    assessmentTerm: selectAssessmentTerm(state),
  };
};
AssessmentProgramDetails.propTypes = {
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  getAssessmentPlanningDashboard: PropTypes.func,
  history: PropTypes.object,
  programId: PropTypes.string,
  assessmentDashboardDetails: PropTypes.instanceOf(List),
  programName: PropTypes.string,
  assessmentTerm: PropTypes.instanceOf(Map),
  getAssessmentPlanningByTerm: PropTypes.func,
};
export default compose(withRouter, connect(mapStateToProps, actions))(AssessmentProgramDetails);
