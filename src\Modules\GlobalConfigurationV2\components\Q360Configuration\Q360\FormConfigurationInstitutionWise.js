import MaterialInput from 'Widgets/FormElements/material/Input';
import Button from 'Widgets/FormElements/material/Button';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import InfoIcon from '@mui/icons-material/Info';
import { jsUcfirstAll } from 'utils';
import {
  describeSx,
  MenuWithOpenAndClose,
  typeOfForms,
  useDispatchAndSelectorFunctionsQlc,
} from './utils';
import { Map as IMap, List } from 'immutable';
import { useDispatch, useSelector } from 'react-redux';
import {
  createDuplicateForm,
  getCategoryForm,
  getInstitutionsList,
  updateDuplicateForm,
} from '_reduxapi/q360/actions';
import { selectInstitutionList } from '_reduxapi/q360/selectors';
import {
  Checkbox,
  Chip,
  FormControl,
  RadioGroup,
  Radio,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import { DebouncedSearch } from 'Widgets/FormElements/material/DebouncedSearch';
import PropTypes from 'prop-types';

export default function FormConfigurationInstitutionWise({ handleClose, params, existingData }) {
  const [search, setSearch] = useState('');
  const [formData, setFormData] = useState(
    IMap({
      formName: existingData.get('formName', ''),
      describe: existingData.get('describe', ''),
      formType: existingData.get('formType', 'complete'),
      incorporateMandatory: existingData.get('incorporateMandatory', false),
    })
  );
  const initialState = useMemo(function () {
    let institutionMap = IMap();
    for (const eachInstitution of existingData.get('selectedInstitution', List())) {
      institutionMap = institutionMap.set(
        eachInstitution.get('assignedInstitutionId', ''),
        eachInstitution
      );
    }
    return institutionMap;
  }, []);
  const [pgmDetailsState, setPgmDetailsState] = useState(initialState);
  const dispatch = useDispatch();

  const handleInput = (key) => (e) => {
    setFormData((prev) => prev.set(key, e.target.value));
  };

  const institutionListRedux = useSelector(selectInstitutionList);
  useEffect(() => {
    if (institutionListRedux.size) return;
    dispatch(getInstitutionsList());
  }, []);

  function handleAllInstitution(e) {
    const checked = e.target.checked;
    if (checked) {
      return setPgmDetailsState(institutionListRedux);
    }
    setPgmDetailsState(IMap());
  }
  const searchCallBack = (query) => {
    setSearch(query);
  };
  const handleIndividualInstitution = (institution) => (e) => {
    const checked = e.target.checked;
    setPgmDetailsState((prev) => {
      if (checked) {
        return prev.set(institution.get('assignedInstitutionId', ''), institution);
      }
      return prev.delete(institution.get('assignedInstitutionId', ''), institution);
    });
  };

  const onDelete = (assignedInstitutionId) => () => {
    setPgmDetailsState((prev) => prev.delete(assignedInstitutionId));
  };
  const {
    setMessage,
    configureTemplate,
    currentCategoryIndex,
  } = useDispatchAndSelectorFunctionsQlc();
  const isIncorporateMandatory = configureTemplate.getIn(
    [currentCategoryIndex, 'actions', 'incorporateMandatory'],
    false
  );
  const categoryFormType = configureTemplate.getIn([currentCategoryIndex, 'categoryFormType'], '');
  const clearAll = () => {
    setPgmDetailsState(initialState);
  };
  const handleValidate = () => {
    if (!formData.get('formName', '').trim()) {
      setMessage('Enter the Form Name');
      return true;
    }
    if (pgmDetailsState.isEmpty()) {
      setMessage('Select the Institution');
      return true;
    }
    return false;
  };

  function handleCreateCallback() {
    dispatch(getCategoryForm(params));
    handleClose();
  }
  const handleCreate = () => {
    if (handleValidate()) return;
    const selectedData = pgmDetailsState.valueSeq().map((institution) =>
      IMap({
        assignedInstitutionId: institution.get('assignedInstitutionId', ''),
        institutionName: institution.get('institutionName', ''),
      })
    );
    const categoryId = params.split('=').pop();
    const payload = {
      [existingData.has('formId') ? 'categoryFormId' : 'categoryId']: existingData.get(
        'formId',
        configureTemplate.getIn([currentCategoryIndex, 'assignedInstitutionId'])
      ),
      ...(existingData.get('formName', '') !== formData.get('formName', '') && {
        formName: formData.get('formName', ''),
      }),
      describe: formData.get('describe', ''),
      formType: formData.get('formType', ''),
      categoryFormType: categoryFormType,
      incorporateMandatory: formData.get('incorporateMandatory', false),
      selectedInstitution: selectedData,
      categoryId,
    };
    if (existingData.has('formId')) {
      return dispatch(updateDuplicateForm(payload, handleCreateCallback));
    }
    payload['categorySettings'] = {
      level: configureTemplate.getIn([currentCategoryIndex, 'level'], ''),
    };
    payload['selectCourses'] = selectedData;
    const actions = {};
    actions['studentGroups'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'studentGroups'],
      false
    );
    actions['everyAcademic'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'everyAcademic'],
      false
    );
    actions['occurrenceConfiguration'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'occurrenceConfiguration'],
      false
    );
    actions['attemptType'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'attemptType'],
      false
    );
    actions['academicTerms'] = configureTemplate.getIn(
      [currentCategoryIndex, 'actions', 'academicTerms'],
      false
    );
    payload['actions'] = actions;
    dispatch(createDuplicateForm(payload, params, 'create', handleCreateCallback));
  };
  const handleCheckboxInput = (key) => (e) => {
    setFormData((prev) => prev.set(key, e.target.checked));
  };
  const filteredInstitutionListRedux = institutionListRedux.filter(
    (institution) =>
      search === '' ||
      (search !== '' &&
        (institution.get('institutionName', '').toLowerCase().includes(search) ||
          institution.get('code', '').toLowerCase().includes(search)))
  );

  const parentDialogRef = useRef(null);
  return (
    <Dialog
      open={true}
      ref={parentDialogRef}
      onClose={handleClose}
      maxWidth={'sm'}
      fullWidth={true}
      // className={`${open ? 'invisible' : 'visible'}`}
    >
      <DialogTitle id="scroll-dialog-title">
        <div className="form_heading_creation">{existingData.get('dialogTitle', '')}</div>
      </DialogTitle>
      <DialogContent ref={parentDialogRef}>
        <div className="">
          <div>
            <MaterialInput
              changed={handleInput('formName')}
              elementType={'materialInput'}
              type={'text'}
              value={formData.get('formName', '')}
              variant={'outlined'}
              label={<div className="f-12 fw-400 text-mGrey">Form Name</div>}
              placeholder={'Enter Name'}
              sx={{
                '& .MuiInputBase-root': {
                  color: '#000000 !important',
                },
                '& .MuiInputBase-input': {
                  border: '1px solid #D1D5DB !important',
                  borderRadius: '4px',
                  padding: '8px 7px',
                },
              }}
            />
          </div>
          <div>
            <MaterialInput
              changed={handleInput('describe')}
              value={formData.get('describe', '')}
              elementType={'materialTextArea'}
              type={'text'}
              variant={'outlined'}
              label={<div className="f-12 fw-400 text-mGrey pt-2">Describe (Optional)</div>}
              placeholder={'Describe here'}
              maxRows={'4'}
              minRows={'4'}
              bgWhite={true}
              sx={describeSx}
            />
          </div>
          {isIncorporateMandatory && (
            <div>
              <div className="f-12 fw-400 text-mGrey pt-1">Incorporate Type</div>
              <div className="d-flex align-items-center gap-10 mt-1">
                <Checkbox
                  size="small"
                  checked={formData.get('incorporateMandatory', false)}
                  onChange={handleCheckboxInput('incorporateMandatory')}
                  sx={{
                    padding: '5px 0px',
                    '& .MuiSvgIcon-root': { width: 17, height: 17 },
                    color: '#000000',
                  }}
                />
                <div className="f-14">Incorporate is Mandatory</div>
                <InfoIcon className="f-16" sx={{ color: '#6B7280' }} />
              </div>
            </div>
          )}
          {categoryFormType === 'form' && (
            <>
              <div className="f-12 fw-400 text-mGrey mt-2">Form Type</div>{' '}
              <FormControl>
                <RadioGroup value={formData.get('formType', '')} onChange={handleInput('formType')}>
                  <div className="d-flex align-items-center gap-10 mt-2 ">
                    {typeOfForms.map((element, index) => (
                      <div className="d-flex align-items-center gap-10" key={index}>
                        <FormControlLabel
                          key={index}
                          className="m-0 d-flex gap-2"
                          value={element.value}
                          control={
                            <Radio
                              size="small"
                              sx={{
                                padding: '0px',
                                '& .MuiSvgIcon-root': {
                                  width: '17px',
                                  height: '17px',
                                },
                              }}
                            />
                          }
                          label={<div className="f-14 ml-2">{element.label}</div>}
                        />
                        <InfoIcon className="mx-2 f-16" sx={{ color: '#6B7280' }} />
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </FormControl>
            </>
          )}
        </div>

        <div className="mt-1">
          <div className="f-12 fw-400 text-dGrey mb-2 pt-2">Assign Institutions</div>
          <div className=" rounded pl-3 q360-select-pd " style={{ border: '1px solid #D1D5DB' }}>
            <MenuWithOpenAndClose
              children1={(handleClick) => (
                <div className="d-flex align-items-center cursor-pointer" onClick={handleClick}>
                  <div style={{ color: '#D1D5DB' }}>Select Institution</div>
                  <div className="ml-auto cursor">
                    <ArrowDropDownIcon className="pt-1" />
                  </div>
                </div>
              )}
            >
              {(handleClose) => (
                <>
                  <DialogTitle sx={{ padding: '16px 16px 0px 16px' }}>
                    <DebouncedSearch
                      placeholder={'Search a Name, ID...'}
                      doSomething={searchCallBack}
                    />
                  </DialogTitle>
                  <DialogContent
                    sx={{
                      padding: '0px 8px',
                      height: parentDialogRef?.current?.offsetHeight,
                    }}
                  >
                    {!(filteredInstitutionListRedux.size > 0) && (
                      <div
                        className="f-12 py-2 d-flex align-items-center justify-content-center"
                        style={{ height: '50vh' }}
                      >
                        No Data Found...
                      </div>
                    )}

                    {filteredInstitutionListRedux.size > 0 && (
                      <>
                        <div className="d-flex align-items-center px-2">
                          <div className="f-12 fw-400 text-lGrey ml-2">Select Institution</div>
                          <div className="ml-auto">
                            <div className="d-flex align-items-center">
                              <Checkbox
                                size="small"
                                checked={institutionListRedux.size === pgmDetailsState.size}
                                onChange={handleAllInstitution}
                              />
                              <div className="f-12 fw-400 text-dGrey">All Institution</div>
                            </div>
                          </div>
                        </div>
                        {filteredInstitutionListRedux
                          .entrySeq()
                          .map(([assignedInstitutionId, institution]) => {
                            // if (
                            //   search !== '' &&
                            //   (!institution.get('institutionName', '').toLowerCase().includes(search) ||
                            //     !institution.get('code', '').toLowerCase().includes(search))
                            // )
                            //   return null;
                            return (
                              <div
                                className="d-flex align-items-center "
                                key={assignedInstitutionId}
                              >
                                <div>
                                  <Checkbox
                                    onChange={handleIndividualInstitution(institution)}
                                    size="small"
                                    checked={pgmDetailsState.has(assignedInstitutionId)}
                                  />
                                </div>

                                <div className="f-14">
                                  {jsUcfirstAll(institution.get('institutionName', ''))}
                                </div>
                              </div>
                            );
                          })}
                      </>
                    )}
                  </DialogContent>
                  <DialogActions sx={{ padding: '8px 16px' }}>
                    <Button
                      clicked={() => {
                        clearAll();
                        handleClose();
                      }}
                      variant="outlined"
                      className="ml-auto px-4"
                      size={'small'}
                      color={'gray'}
                    >
                      Cancel
                    </Button>
                    <Button
                      clicked={() => {
                        handleClose();
                      }}
                      disabled={!pgmDetailsState.size}
                      variant="contained"
                      className="ml-3 px-4"
                      color="primary"
                      size={'small'}
                    >
                      Save
                    </Button>
                  </DialogActions>
                </>
              )}
            </MenuWithOpenAndClose>
          </div>
          <div className="d-flex align-items-center text-overflow-wrap">
            {pgmDetailsState.entrySeq().map(([assignedInstitutionId, institution]) => {
              return (
                <Chip
                  key={assignedInstitutionId}
                  label={
                    <div className="f-12 fw-400 text-dGrey text-capitalize">
                      {institution.get('institutionName', '')}
                    </div>
                  }
                  onDelete={onDelete(assignedInstitutionId)}
                  // deleteIcon={<CloseIcon className="f-18" />}
                  sx={{
                    '&.MuiButtonBase-root.MuiChip-root': {
                      borderRadius: '6px',
                      color: '#000000',
                      height: '45px',
                    },
                  }}
                  className="select-color mt-2 mr-3"
                />
              );
            })}
          </div>
        </div>
      </DialogContent>
      <DialogActions
        sx={{
          padding: '16px 24px',
        }}
      >
        <Button
          clicked={handleClose}
          variant="outlined"
          className="px-4 ml-auto"
          size={'small'}
          color={'gray'}
        >
          Cancel
        </Button>
        <Button
          clicked={handleCreate}
          variant="contained"
          className="ml-3 px-4"
          color="primary"
          size={'small'}
        >
          {existingData.has('formId') ? 'Save' : 'Create'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

FormConfigurationInstitutionWise.propTypes = {
  existingData: PropTypes.instanceOf(IMap),
  handleClose: PropTypes.func,
  params: PropTypes.string,
  formType: PropTypes.string,
};
