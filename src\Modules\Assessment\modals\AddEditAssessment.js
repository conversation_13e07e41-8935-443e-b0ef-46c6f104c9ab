import React, { useEffect, useState } from 'react';
import {
  ListItem,
  List as Lists,
  ListItemText,
  Collapse,
  ListItemIcon,
  Checkbox,
  Divider,
  Typography,
} from '@mui/material';
import { Menu } from '@mui/material';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MButton from 'Widgets/FormElements/material/Button';
import MaterialInput from 'Widgets/FormElements/material/Input';
import PropTypes from 'prop-types';
import * as actions from '../../../_reduxapi/assessment/action';
import { connect } from 'react-redux';
import {
  selectAssessmentCourses,
  selectAssessmentPlanning,
} from '../../../_reduxapi/assessment/selector';
import { selectActiveInstitutionCalendar } from '../../../_reduxapi/Common/Selectors';
import { Map, List, fromJS } from 'immutable';
import { assessmentValidation } from '../utils';

const AddEditAssessmentModal = (props) => {
  const {
    show,
    cancel,
    updateAssessment,
    mode,
    getAssessmentCourses,
    getAssessmentPlan,
    programId,
    calendarId,
    planId,
    assessmentCourses,
    setData,
    ids,
    programCourseLevel,
  } = props;
  const term = programCourseLevel.get('term', '');
  const type = programCourseLevel.get('type', '');
  const level = programCourseLevel.get('level', '');
  const courseId = programCourseLevel.get('courseId', '');

  const [anchorEl, setAnchorEl] = useState(null);
  const [name, setName] = useState(ids.getIn(['plan', 'name'], ''));
  const [occurring, setOccurring] = useState(ids.getIn(['plan', 'occurring'], 'program'));
  const [courses, setCourses] = useState(ids.getIn(['plan', 'courses'], List()));
  const [marks, setMarks] = useState(ids.getIn(['plan', 'totalMark'], ''));
  const [open1, setOpen] = useState(true);
  const [edited, setEdited] = useState(false);
  const [search, setSearch] = useState('');
  const stage = ids.get('showMarks', false);
  const open = Boolean(anchorEl);
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const ITEM_HEIGHT = 48;

  const programType = [
    {
      name: 'Program Level',
      value: 'program',
    },
    {
      name: 'Course Level',
      value: 'course',
    },
  ];

  useEffect(() => {
    !stage &&
      occurring === 'course' &&
      !assessmentCourses.size &&
      getAssessmentCourses(programId, calendarId, term);
  }, [occurring]); // eslint-disable-line
  const handleClicks = (e) => {
    e.stopPropagation();
    setOpen(!open1);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const callback = () => {
    getAssessmentPlan(programId, calendarId, { term, type, level, courseId }, stage);
    cancel();
  };
  const checkOnlyNumber = (item) => {
    return /^\d+$/.test(item);
  };

  const handleChange = (e, type) => {
    setEdited(true);
    type === 'name' && setName(e.target.value);
    type === 'occurring' && setOccurring(e.target.value);
    type === 'marks' &&
      (e.target.value !== ''
        ? checkOnlyNumber(e.target.value)
          ? setMarks(e.target.value)
          : setData(
              Map({
                message: `Please enter numbers only`,
              })
            )
        : setMarks(e.target.value));
  };
  const getCreditHours = (array) => {
    let sum = 0;
    array.forEach((creditHour) => {
      const crhr = creditHour.get('credit_hours', 0);
      sum += crhr;
    });
    return sum;
  };
  const handleCheck = (e, year, level, option, selected) => {
    setEdited(true);

    if (!selected) {
      setCourses(
        courses.push(
          fromJS({
            year,
            level,
            courseId: option.get('_course_id', ''),
          })
        )
      );
    } else {
      setCourses(
        courses.filter((item) => {
          return item.get('courseId', '') !== option.get('_course_id', '');
        })
      );
    }
  };
  const handleLevelCheck = (e, year, level, selected) => {
    setEdited(true);
    let tempArray = courses;
    let tempCourseIds = level
      .get('courses', List())
      .map((item, index) => item.get('_course_id', ''));
    if (!selected) {
      level.get('courses', List()).map((course) => {
        const courseObj = courses.find(
          (c) => c.get('courseId', '') === course.get('_course_id', '')
        );
        if (!courseObj) {
          tempArray = tempArray.push(
            fromJS({ year, level: level.get('level', ''), courseId: course.get('_course_id', '') })
          );
        }
        return '';
      });
    } else {
      tempArray = courses.filter((item) => !tempCourseIds.includes(item.get('courseId', '')));
    }
    setCourses(tempArray);
  };
  const findFunc = (id) => {
    const checkCondition = courses.find((item) => item.get('courseId', '') === id);
    return checkCondition;
  };
  const levelCheckFunc = (data, value = true) => {
    const stateArray = courses.map((item) => item.get('courseId', ''));
    const dataArray = data.map((item) => item.get('_course_id', ''));
    if (dataArray.size === 0 && stateArray.size === 0) {
      return false;
    } else {
      const returnElement = dataArray.every((element) => {
        if (stateArray.includes(element)) {
          return true;
        }
        return false;
      });
      return returnElement;
    }
  };
  const getLevelSize = (checkLevel) => {
    let levelCourses = courses.filter((course) => course.get('level', '') === checkLevel);
    return levelCourses.size;
  };
  const handleSearch = (e) => {
    setSearch(e.target.value);
  };
  const filteredCourses = () => {
    if (search === '') {
      return assessmentCourses;
    } else {
      const filteredArr = assessmentCourses.map((term) =>
        term.set(
          'year',
          term.get('year', List()).map((year) =>
            year.set(
              'levels',
              year.get('levels', List()).map((level) =>
                level.set(
                  'courses',
                  level
                    .get('courses', List())
                    .filter(
                      (course) =>
                        course
                          .get('courses_name', '')
                          .toLowerCase()
                          .includes(search.toLowerCase()) ||
                        course
                          .get('courses_number', '')
                          .toLowerCase()
                          .includes(search.toLowerCase())
                    )
                )
              )
            )
          )
        )
      );

      return filteredArr;
    }
  };
  const handleSubmit = () => {
    let namesArray = List();
    namesArray = ids
      .getIn(['assessmentType', 'planning'], List())
      .map((plan, planIndex) => plan.get('name', '').toLowerCase());
    const requestBody = {
      _id: planId,
      typeId: ids.get('typeId', ''),
      subTypeId: ids.get('subTypeId', ''),
      assessmentTypeId: ids.get('assessmentTypeId', ''),
      ...(mode === 'update' && { assessmentId: ids.get('assessmentId', '') }),
      assessmentName: name.trim(),
      ...(!stage && { occurring }),
      totalMark: marks,
      ...(!stage && occurring === 'course' && { courses }),
    };
    const newNamesArray = namesArray.filter(
      (item) => item !== ids.getIn(['plan', 'name'], '').toLowerCase()
    );
    assessmentValidation(
      name.trim(),
      ids.getIn(['plan', 'name'], '') !== '' ? newNamesArray : namesArray,
      setData,
      'Assessment'
    ) && updateAssessment({ requestBody, mode, callback, stage });
  };
  return (
    <MaterialDialog show={show} onClose={cancel} maxWidth={'xs'} fullWidth={true}>
      <div className="w-100 p-4">
        <p className="mb-3 pb-2 border-bottom bold f-19">
          {' '}
          {mode === 'create'
            ? ` Add Assessment For ${ids.getIn(['assessmentType', 'name'], '')}`
            : 'Edit Assessment'}
        </p>
        <div className="mt-2 mb-2 ">
          <MaterialInput
            elementType={'materialInput'}
            type={'text'}
            variant={'outlined'}
            size={'small'}
            placeholder={'Assessment Name'}
            label={'Assessment Name'}
            value={name}
            changed={(e) => handleChange(e, 'name')}
            maxLength={100}
          />
        </div>

        {!stage && (
          <div className="mt-2 mb-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: programType }}
              label={'Assessment Occuring'}
              value={occurring}
              changed={(e) => handleChange(e, 'occurring')}
            />
          </div>
        )}

        {!stage && occurring === 'course' && (
          <div className="mt-2 mb-2">
            <label className="form-label ">Select Courses</label>
            <Typography
              component="div"
              className="muiOutline remove_hover"
              aria-label="more"
              aria-controls="long-menu"
              aria-haspopup="true"
              onClick={handleClick}
            >
              <div className="d-flex justify-content-between pt-2 pb-2">
                <p className="mb-0 pl-3">{`${courses.size} Courses`}</p>
                <ArrowDropDownIcon className="text-gray" />
              </div>
            </Typography>
            <Menu
              id="long-menu"
              anchorEl={anchorEl}
              keepMounted
              open={open}
              onClose={handleClose}
              PaperProps={{
                style: {
                  maxHeight: ITEM_HEIGHT * 10.5,
                  width: '42.2ch',
                },
              }}
            >
              <div className="w-100 p-3">
                <MaterialInput
                  elementType={'materialSearch'}
                  type={'text'}
                  size={'small'}
                  placeholder={'Search'}
                  value={search}
                  changed={(e) => handleSearch(e)}
                />
              </div>
              {filteredCourses().map((term, tIndex) =>
                term.get('year', List()).map((year, yIndex) => (
                  <div key={yIndex}>
                    <ListItem button>
                      <ListItemText primary={year.get('year', '')} className="text-darkgray" />
                    </ListItem>
                    <Divider />
                    {year.get('levels', List()).map((level, lIndex) => (
                      <div key={lIndex}>
                        <Lists component="div" className="align-items-center" disablePadding>
                          <ListItem
                            button
                            selected={levelCheckFunc(level.get('courses', List()))}
                            onClick={(e) =>
                              handleLevelCheck(
                                e,
                                year.get('year', ''),
                                level,
                                levelCheckFunc(level.get('courses', List()))
                              )
                            }
                          >
                            <ListItemIcon className="minWidth-32">
                              <Checkbox
                                edge="start"
                                checked={levelCheckFunc(level.get('courses', List()))}
                                tabIndex={-1}
                                disableRipple
                                color="primary"
                              />
                            </ListItemIcon>
                            <ListItemText primary={level.get('level', '')} />
                            <ListItemText
                              className="text-right"
                              primary={`${getLevelSize(level.get('level', ''))} Course Selected`}
                            />
                            <div onClick={(e) => handleClicks(e)}>
                              {open1 ? <ExpandLess /> : <ExpandMore />}
                            </div>
                          </ListItem>
                        </Lists>
                        <Divider />

                        {level.get('courses', List()).map((course, cIndex) => (
                          <div key={cIndex}>
                            <Collapse in={open1} timeout="auto" unmountOnExit>
                              <ListItem
                                button
                                selected={Boolean(findFunc(course.get('_course_id', '')))}
                                value={course.get('_course_id', '')}
                                onClick={(e) =>
                                  handleCheck(
                                    e,
                                    year.get('year', ''),
                                    level.get('level', ''),
                                    course,
                                    Boolean(findFunc(course.get('_course_id', '')))
                                  )
                                }
                              >
                                <ListItemIcon className="minWidth-32">
                                  <Checkbox
                                    edge="start"
                                    checked={Boolean(findFunc(course.get('_course_id', '')))}
                                    tabIndex={-1}
                                    disableRipple
                                    color="primary"
                                  />
                                </ListItemIcon>
                                <ListItemText
                                  primary={`${course.get('courses_number', '')} - ${course.get(
                                    'courses_name',
                                    ''
                                  )} - ${getCreditHours(course.get('credit_hours', List()))} Cr. H`}
                                />
                              </ListItem>
                              <Divider />
                            </Collapse>
                          </div>
                        ))}
                      </div>
                    ))}{' '}
                  </div>
                ))
              )}
            </Menu>
          </div>
        )}
        <div className="mt-2 mb-2 pb-2">
          <MaterialInput
            elementType={'materialInput'}
            type={'text'}
            variant={'outlined'}
            size={'small'}
            placeholder={'Total Marks'}
            label={'Set Total Marks'}
            value={marks}
            maxLength={3}
            changed={(e) => handleChange(e, 'marks')}
          />
        </div>

        <div className="d-flex justify-content-end border-top pt-3">
          <MButton variant="outlined" color="primary" className={'mr-2'} clicked={cancel}>
            Cancel
          </MButton>
          <MButton
            variant="contained"
            color="primary"
            clicked={handleSubmit}
            disabled={!edited || !name || !marks || (occurring === 'course' ? !courses.size : '')}
          >
            Save
          </MButton>
        </div>
      </div>
    </MaterialDialog>
  );
};
AddEditAssessmentModal.propTypes = {
  show: PropTypes.bool,
  cancel: PropTypes.func,
  updateAssessment: PropTypes.func,
  mode: PropTypes.string,
  getAssessmentCourses: PropTypes.func,
  programId: PropTypes.string,
  calendarId: PropTypes.string,
  ids: PropTypes.instanceOf(Map),
  planId: PropTypes.string,
  getAssessmentPlan: PropTypes.func,
  assessmentCourses: PropTypes.instanceOf(List),
  assessmentPlanning: PropTypes.instanceOf(Map),
  setData: PropTypes.func,
  programCourseLevel: PropTypes.instanceOf(Map),
};
const mapStateToProps = function (state) {
  return {
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    assessmentCourses: selectAssessmentCourses(state),
    assessmentPlanning: selectAssessmentPlanning(state),
  };
};
export default connect(mapStateToProps, actions)(AddEditAssessmentModal);
