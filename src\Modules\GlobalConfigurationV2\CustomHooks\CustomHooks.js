import { Map } from 'immutable';
import { getConfigureTemplate } from '_reduxapi/q360/actions';
import { selectedConfigureTemplate } from '_reduxapi/q360/selectors';
import { useState, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { Map as IMap, fromJS } from 'immutable';
import { useHistory } from 'react-router-dom';
/*
@params  - param1 : boolean
@useAges - this hook working toggle boolean value to use boolean component 
@like    - Dialog,Modal etc...
*/
export const useBooleanHook = (defaultValue = false) => {
  const [open, setOpen] = useState(defaultValue);
  const handleDialog = () => setOpen(!open);
  return [open, handleDialog];
};
/*
@params  - param1 : string
@useAges - this hook working toggle boolean value to use boolean component 
@like    - TextFelid etc...
*/
export const useInputHook = (defaultValue = '', isLowerCase = false) => {
  const [input, setInput] = useState(defaultValue);
  const handleChangeState = (event) =>
    setInput(isLowerCase ? event.target.value.toLowerCase() : event.target.value);
  const setInitialValue = (value = '') => setInput(value);
  return [input, handleChangeState, setInitialValue];
};
/*
@params  - param1 : Object || immutable Map
@useAges - this hook change nested key value
*/
export const useNestedHook = (defaultValue = Map()) => {
  const [input, setInput] = useState(defaultValue);
  const handleChangeNested = (key, value) => setInput((previous) => previous.set(key, value));
  const handleChangeDeepNested = (key, value) => setInput((previous) => previous.setIn(key, value));
  const setDefault = () => setInput(defaultValue);
  return [input, handleChangeNested, handleChangeDeepNested, setDefault];
};
/*
@params  - param1 : function , param2 : object
@useAges - update api data 
*/
export const useCallApiHook = (action) => {
  const dispatch = useDispatch();
  const dispatching = (...params) => {
    dispatch(action(...params));
  };
  return [dispatching];
};
/*
@params  - param1 : function , param2 : string , param3 : number , param4 : object
@useAges - use for search data once action completed delay to call function
*/
export const useDebounce = (action, searchInput, condition, ...params) => {
  const [updateApi] = useCallApiHook(action);
  useEffect(() => {
    const cb = setTimeout(() => {
      if (condition && searchInput) updateApi(...params);
    }, 500);
    return () => clearTimeout(cb);
  }, [searchInput]);
};
/*
@params  - param1 : function , param2 : object , param2 : boolean
@useAges - useIng for autoSave purpose wil component unMount time 
*/
export const useAutoSave = (action, ref, callBack = () => {}) => {
  const [updateApi] = useCallApiHook(action);
  useEffect(() => {
    return () => {
      const payload = ref.current.data;
      const isEdited = ref.current.boolean.isEdited;
      if (isEdited) {
        updateApi(payload);
        callBack && callBack();
      }
    };
  }, []);
};

export const useAutoSaveWithCheckChanges = (data, comparisonData, apiCall) => {
  const dataRef = useRef(data);
  const comparisonDataRef = useRef(comparisonData);

  useEffect(() => {
    dataRef.current = data;
    comparisonDataRef.current = comparisonData;
  }, [data, comparisonData]);

  useEffect(() => {
    return () => {
      if (
        JSON.stringify(fromJS(dataRef.current).toJS()) !==
        JSON.stringify(fromJS(comparisonDataRef.current).toJS())
      ) {
        apiCall();
      }
    };
  }, []);
};
/*
@useAges - get url query params value 
*/
export const useSearchParams = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  return [searchParams];
};
/*
@params  - param1 : useSelector function , param2 : action function
@useAges - call GetApi check is data Empty
*/
export const useIsReduxEmpty = (selected, action, ...args) => {
  const selector = useSelector(selected);
  const [getApi] = useCallApiHook(action);
  useEffect(() => {
    if (selector.size === 0) getApi(...args);
  }, []);
  return [selector];
};

/*
@params  - param1 : react state
@useAges - the compound scroll into view on screen
 */
export const useScrollIntoView = (state) => {
  const [store, setStore] = useState(Map());
  const ref = useRef(null);
  useEffect(() => {
    if (store?.size < state.size) ref.current.scrollIntoView({ behavior: 'smooth' });
    setStore(state);
  }, [state]);
  return [ref];
};

export const useSetInitialState = (value, setFunction, dependency, stateInital) => {
  useEffect(() => {
    if (value && !stateInital) setFunction();
  }, [dependency]);
};

export const useRouteing = () => {
  const history = useHistory();
  return [history];
};

export const useZoomPage = (defaultValue = 100) => {
  const [zoom, setZoom] = useState(defaultValue);
  const [intervalId, setIntervalId] = useState(null);
  const handleZoomPage = (value) =>
    setZoom((count) => {
      const maxZoom = 250;
      const minZoom = 75;
      if (value === '+') {
        if (maxZoom > count) count = count + 10;
      } else {
        if (minZoom < count) count = count - 10;
      }
      return count;
    });
  const handleMouseDown = (value) => {
    const id = setInterval(() => {
      handleZoomPage(value);
    }, 50);
    setIntervalId(id);
  };
  const handleMouseUp = () => {
    if (intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
    }
  };
  return [zoom, handleMouseDown, handleMouseUp];
};

export const useConfigureTemplate = () => {
  const configureTemplate = useSelector(selectedConfigureTemplate);
  const [searchParams] = useSearchParams();
  const currentCategoryIndex = Number(searchParams.get('currentCategoryIndex') ?? 0);
  const dispatch = useDispatch();
  useEffect(() => {
    if (configureTemplate.size) return;
    dispatch(getConfigureTemplate());
  }, []);
  const currentCategory = configureTemplate.get(currentCategoryIndex, IMap());
  return [currentCategory];
};

export const useDisableElement = (isDisable, elementIds) => {
  useEffect(() => {
    if (isDisable) elementIds.forEach((Id) => (document.getElementById(Id).disabled = true));
  }, []);
};
