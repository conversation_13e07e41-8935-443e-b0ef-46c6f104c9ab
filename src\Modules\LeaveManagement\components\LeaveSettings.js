import React, { Component } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { Badge } from 'react-bootstrap';
import PropTypes from 'prop-types';
import { t } from 'i18next';

import Permission from './Permission';
import LeaveClassification from './LeaveClassification';
import OnDutyClassification from './OnDutyClassification';
import WarningSettings from './AbsenceCalculation';

import ReportAbsence from './ReportAbsence';
import * as actions from '../../../_reduxapi/leave_management/actions';
import ContactMail from '../modal/HrContatctModal';
import LeaveApproval from '../../LeaveManagement/components/LeaveApproval';
import { selectLeaveCategories } from '../../../_reduxapi/leave_management/selectors';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { activeLMSVersion } from 'utils';

class LeaveSettings extends Component {
  constructor() {
    super();
    this.state = {
      staffView: false,
      show: false,
      studentView: false,
      name: '',
      staffname: '',
      studentname: '',
    };
  }
  componentDidMount() {
    let query = new URLSearchParams(this.props.location.search);
    const { history } = this.props;
    let params = query.get('name');
    if (params !== 'staff') {
      this.setState({
        studentView: true,
        studentname: 'student',
        staffView: false,
      });
    } else {
      this.setState({
        studentView: false,
        staffname: 'staff',
        staffView: true,
      });
    }
    this.props.setBreadCrumbName(
      `${t('role_management.modules_list.Academic_Accountability_Management')} > ${t(
        'infra_management.bread_crumb.Settings'
      )}`
    );
    if (
      !CheckPermission('tabs', 'Leave Management', 'Leave Settings', '', 'Staff', '') &&
      CheckPermission('tabs', 'Leave Management', 'Leave Settings', '', 'Student LMS', 'View') &&
      activeLMSVersion().toLowerCase() === 'v2'
    ) {
      history.push('/lms');
    } else if (
      !CheckPermission('tabs', 'Leave Management', 'Leave Settings', '', 'Staff', '') &&
      CheckPermission('tabs', 'Leave Management', 'Leave Settings', '', 'Student', '') &&
      activeLMSVersion().toLowerCase() === 'v1'
    ) {
      history.push('/leave-management/settings?name=student');
    }
  }

  staffTab = () => {
    this.setState({
      studentView: false,
      staffname: 'staff',
      staffView: true,
    });
  };

  studentTab = () => {
    this.setState({
      staffView: true,
      studentView: true,
      studentname: 'student',
    });
  };

  onModalClose = () => {
    this.setState({ show: !this.state.show });
  };

  onModalSave = (categoryName) => {
    const requestBody = {
      hr_contact: categoryName,
    };
    this.props.HrContact(requestBody);
    this.setState({ show: !this.state.show });
  };
  render() {
    const { staffname, studentname, staffView, studentView } = this.state;
    let query = new URLSearchParams(this.props.location.search);
    let params = query.get('name');
    const activeLMSVer = activeLMSVersion();
    return (
      <React.Fragment>
        <div className="customize_tab">
          <ul id="menu">
            {CheckPermission('tabs', 'Leave Management', 'Leave Settings', '', 'Staff', '') && (
              <Link
                to="/leave-management/settings?name=staff"
                onClick={this.staffTab}
                className={`tabaligment ${
                  staffname.includes(params) && staffView ? 'tabactive' : ''
                }`}
              >
                {t('events.staff')}
              </Link>
            )}
            <>
              {activeLMSVer.toLowerCase() === 'v2' ? (
                <>
                  {CheckPermission(
                    'tabs',
                    'Leave Management',
                    'Leave Settings',
                    '',
                    'Student LMS',
                    'View'
                  ) && (
                    <Link to="/lms" className={`tabaligment`}>
                      Student
                    </Link>
                  )}
                </>
              ) : (
                <>
                  {CheckPermission(
                    'tabs',
                    'Leave Management',
                    'Leave Settings',
                    '',
                    'Student',
                    ''
                  ) && (
                    <Link
                      to="/leave-management/settings?name=student"
                      onClick={this.studentTab}
                      className={`tabaligment ${
                        studentname.includes(params) && studentView ? 'tabactive' : ''
                      }`}
                    >
                      {t('events.student')}
                    </Link>
                  )}
                </>
              )}
            </>
          </ul>
        </div>

        {staffname.includes(params) && staffView && (
          <React.Fragment>
            <div className="container-fluid ">
              <div className="main">
                <div className="p-2">
                  {CheckPermission(
                    'subTabs',
                    'Leave Management',
                    'Leave Settings',
                    '',
                    'Staff',
                    '',
                    'HR Contact',
                    'View'
                  ) && (
                    <div className="d-flex justify-content-end mb-3">
                      <span className="">
                        <Badge variant="light badge_bg_white" size="sm" onClick={this.onModalClose}>
                          {t('role_management.sub_tabs.HR Contact')}
                        </Badge>
                      </span>
                    </div>
                  )}
                  <div className="row pb-3">
                    <div className="col-md-12">
                      {this.state.show && (
                        <ContactMail
                          show={this.state.show}
                          onClose={this.onModalClose}
                          onSave={this.onModalSave}
                        />
                      )}

                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        'Staff',
                        '',
                        'Permission',
                        'View'
                      ) && <Permission type="staff" />}
                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        'Staff',
                        '',
                        'Leave Classification',
                        'View'
                      ) && <LeaveClassification type="staff" />}
                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        'Staff',
                        '',
                        'On Duty Classification',
                        'View'
                      ) && <OnDutyClassification type="staff" />}
                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        'Staff',
                        '',
                        'Leave Approval Process',
                        'View'
                      ) && <LeaveApproval type="staff" />}
                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        'Staff',
                        '',
                        'Report Absence',
                        'View'
                      ) && <ReportAbsence type="staff" />}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </React.Fragment>
        )}
        {studentname.includes(params) && studentView && (
          <React.Fragment>
            <div className="container-fluid ">
              <div className="main">
                <div className="p-2">
                  <div className="row pb-3">
                    <div className="col-md-12">
                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        'Student',
                        '',
                        'Permission',
                        'View'
                      ) && <Permission type="student" />}

                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        'Student',
                        '',
                        'Leave Classification',
                        'View'
                      ) && <LeaveClassification type="student" />}

                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        'Student',
                        '',
                        'On Duty Classification',
                        'View'
                      ) && <OnDutyClassification type="student" />}

                      {CheckPermission(
                        'subTabs',
                        'Leave Management',
                        'Leave Settings',
                        '',
                        'Student',
                        '',
                        'Warning and Absence Calculation',
                        'View'
                      ) && <WarningSettings />}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </React.Fragment>
        )}
      </React.Fragment>
    );
  }
}

const mapStateToProps = function (state) {
  return {
    leaveCategories: selectLeaveCategories(state),
  };
};
LeaveSettings.propTypes = {
  location: PropTypes.object,
  setBreadCrumbName: PropTypes.func,
  HrContact: PropTypes.func,
  history: PropTypes.object,
};

export default compose(withRouter, connect(mapStateToProps, actions))(LeaveSettings);
