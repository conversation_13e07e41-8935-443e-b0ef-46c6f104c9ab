const courseHandoutState = (state) => state.courseHandout;

const selectLoading = (state) => courseHandoutState(state).get('isLoading');
const selectMessage = (state) => courseHandoutState(state).get('message');
const selectUserAndCourse = (state) => courseHandoutState(state).get('userAndCourse');
const selectCourseReport = (state) => courseHandoutState(state).get('courseReport');

export { selectLoading, selectMessage, selectUserAndCourse, selectCourseReport };
