import React, { Component } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import Loader from '../../Widgets/Loader/Loader';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Accordion, Card } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
import PropTypes from 'prop-types';

import axios from '../../axios';
import './review.css';
import { connect } from 'react-redux';
import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import moment from 'moment';
import { formatFullName, getEnvCollegeName, isMobileVerifyMandatory } from '../../utils';
import { selectActiveInstitutionCalendar, selectUserId } from '../../_reduxapi/Common/Selectors';
import { Trans } from 'react-i18next';
import { Map } from 'immutable';

const agree = [
  ['Yes', 'yes'],
  ['No', 'no'],
];

const messageItems = [
  { name: 'Email', value: false },
  { name: 'DigiScheduler', value: false },
  { name: 'DigiClass', value: false },
];
class DeanResponse extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      publishShow: false,
      selectAgree: agree[0][1][2],
      feed: '',
      reviewlist: [],
      publishKey: '',
      PROGRAM_CALENDAR_ID: '',
      notifyVia: messageItems,
      deanApprove: false,
      calendarName: null,
      calendarId: null,
      activeProgramId: null,
      activeProgramName: null,
    };
  }

  componentDidMount() {
    const notifyVia = isMobileVerifyMandatory()
      ? [{ name: 'SMS', value: false }, ...messageItems]
      : [...messageItems];
    const resetValues = notifyVia.map((item) => {
      return { ...item, value: false };
    });
    this.setState({ notifyVia: resetValues });
    this.interval = setInterval(() => {
      const { activeInstitutionCalendar } = this.props;
      if (!activeInstitutionCalendar.isEmpty()) {
        this.fetchApi();
        clearInterval(this.interval);
      }
    }, 200);
  }

  componentDidUpdate(prevProps) {
    if (prevProps.activeInstitutionCalendar !== this.props.activeInstitutionCalendar) {
      this.fetchApi();
    }
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  loadIframe = (data) => {
    const { calendarName, activeProgramId } = this.state;
    if (activeProgramId !== null && activeProgramId === data.programId) {
      const basePath = process.env.REACT_APP_BASE_PATH || '';
      return (
        <div>
          <Accordion defaultActiveKey="" className="pt-4">
            <Card>
              <Accordion.Toggle as={Card.Header} eventKey="0">
                <Trans i18nKey={'side_nav.menus.program_calendar'}></Trans>
              </Accordion.Toggle>
              <Accordion.Collapse eventKey="0">
                <Card.Body className="bg-white">
                  <div className="row">
                    {calendarName !== null ? (
                      <iframe
                        title="reviewer1"
                        src={`${basePath}/pc?programId=${data.programId}&programName=${
                          data.programName
                        }&iframeShow=true&iframeProgramId=${data.programId}&iframeName=${''}`}
                        height="1200"
                        width="100%"
                      />
                    ) : (
                      ''
                    )}
                  </div>
                </Card.Body>
              </Accordion.Collapse>
            </Card>
          </Accordion>
        </div>
      );
    }
    return <></>;
  };

  async getLatestCalendar(id, programName) {
    const { activeInstitutionCalendar } = this.props;
    this.setState({
      calendarName: activeInstitutionCalendar.get('calendar_name', ''),
      calendarId: activeInstitutionCalendar.get('_id', ''),
      activeProgramId: id,
      activeProgramName: programName,
    });
    // axios
    //   .get(`program_calendar/dashboard/${id}`)
    //   .then((res) => {
    //     let institutionCalendar = res.data.data.institution_calendar;
    //     console.log('institutionCalendar', institutionCalendar);
    //     if (institutionCalendar && institutionCalendar.length > 0) {
    //       this.setState({
    //         calendarName: activeInstitutionCalendar.get('calendar_name',''),
    //         calendarId: activeInstitutionCalendar.get('_id',''),
    //         activeProgramId: id,
    //         activeProgramName: programName,
    //       });
    //     }
    //     this.setState({ isLoading: false });
    //   })
    //   .catch((ex) => {});
  }

  async fetchApi() {
    this.setState({
      isLoading: true,
    });
    const { userId, activeInstitutionCalendar } = this.props;
    if (userId !== null) {
      await axios
        .get(
          `program_calendar_review/list_approver_reviewer_review/dean/${userId}?institutionCalendarId=${activeInstitutionCalendar.get(
            '_id',
            ''
          )}`
        )
        .then((res) => {
          const program =
            res.data.data.programs !== undefined &&
            res.data.data.programs.map((data) => {
              return {
                programId: data.program_id,
                programName: data.program_name,
              };
            });
          this.getLatestCalendar(program[0].programId, program[0].programName);
          const reviewList = res.data.data.review.map((data) => {
            return {
              id: data._id,
              programId: data.program_id,
              dean: data.dean !== undefined ? data.dean : [],
              reply: [
                { name: 'Approve', value: 'approve' },
                { name: 'Disapprove', value: 'disapprove' },
              ],
              replyRadio: 0,
              deanReply: '',
            };
          });
          let reviewListByProgram = program.map((item, i) =>
            Object.assign({}, item, reviewList[i])
          );
          this.setState({
            reviewlist: reviewListByProgram,
            isLoading: false,
          });
        })
        .catch((ex) => {
          //console.log('repose error', ex);
          this.setState({
            isLoading: false,
            reviewlist: [],
          });
        });
    }
  }

  disableCompleteCheck = () => {
    const { notifyVia } = this.state;
    const array = notifyVia.filter((item) => item.value === true).length === 0;
    return array;
  };

  handleChange = (e, index) => {
    const reviewlist = [...this.state.reviewlist];
    reviewlist[index].deanReply = e.target.value;
    this.setState({
      reviewlist,
    });
  };

  onChangeReply = (option, replyindex, index) => {
    const reviewlist = this.state.reviewlist;
    reviewlist[index].replyRadio = replyindex;
    this.setState({
      reviewlist,
    });
  };

  deanSubmit = (e, index) => {
    const reviewlist = this.state.reviewlist[index];
    const data = {
      _calendar_id: reviewlist.id,
      reviewer_type: 'dean',
      _reviewer_id: reviewlist.dean._dean_id,
      review: reviewlist.replyRadio === 0 ? true : false,
      review_comment: reviewlist.deanReply,
    };

    axios
      .post(`program_calendar_review/add_reviewer_review`, data)
      .then((res) => {
        NotificationManager.success(`Dean comment sent successfully`);
        this.setState(
          {
            //publishShow: true,
            isLoading: false,
          },
          () => this.fetchApi()
        );
      })
      .catch((ex) => {});
  };

  publishShow = (name, data) => {
    this.setState({
      publishKey: name,
      PROGRAM_CALENDAR_ID: data.id,
      publishShow: !this.state.publishShow,
    });
  };

  publishSubmit = () => {
    let publishnotify = [];
    this.state.notifyVia
      .filter((item) => item.value === true)
      .map((item) => {
        publishnotify.push(item.name.toLowerCase());
        return item;
      });
    const data = {
      _calendar_id: this.state.PROGRAM_CALENDAR_ID,
      to: this.state.publishKey === 'publish' ? 'publish' : 'creator',
      message: `<p>Dear User,<br><br>
      The following Program Calendar has successfully been published.
      <br><br>
      ${this.state.activeProgramName} | Academic year ${this.state.calendarName}
      <br><br>
      Best Regards<br>
      ${getEnvCollegeName()}</p>`,
      notify_via: publishnotify,
    };
    axios
      .post(`program_calendar_review/send_notification`, data)
      .then((res) => {
        NotificationManager.success(`Program Calendar Published Successfully`);

        this.setState(
          {
            isLoading: false,
            publishShow: false,
          },
          () => this.fetchApi()
        );
      })
      .catch((ex) => {});
  };

  handleAllCheckBox = (event, replyindex) => {
    const notifyVia = this.state.notifyVia;
    notifyVia[replyindex].value = event.target.checked;
    this.setState({ notifyVia: notifyVia });
  };

  handleSelect = (data) => {
    const { reviewlist } = this.state;
    setTimeout(() => {
      this.setState({
        deanApprove: false,
        activeProgramId: reviewlist[data].programId,
        activeProgramName: reviewlist[data].programName,
      });
    }, 200);
  };

  render() {
    const items = [{ to: '#', label: 'Program Calendar > Review' }];
    return (
      <React.Fragment>
        <Breadcrumb>
          {items &&
            items.map(({ to, label }, index) => (
              <Link
                className="breadcrumb-icon"
                style={{ color: '#fff', marginTop: '-68px' }}
                key={index}
                to={to}
              >
                {label}
              </Link>
            ))}
        </Breadcrumb>

        {this.state.reviewlist.length === 0 ? (
          <div className="pt-3">
            <div className="col-md-12">
              <div className="notpublished-screen">
                <div className="notpublished">
                  <h2> STILL NOT BEEN REVIEWED</h2>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <>
            <Tabs activeKey={this.state.key} onSelect={this.handleSelect} id="tab-example">
              {this.state.reviewlist.length >= 0 &&
                this.state.reviewlist.map((data, index) => {
                  return (
                    <Tab key={index} eventKey={index} title={data.programName}>
                      <React.Fragment>
                        <div className="main pt-3 pb-5 bg-white">
                          <Loader isLoading={this.state.isLoading} />

                          <div className="container">
                            <div className="row mt-1 pb-1">
                              <div className="col-md-6 ">
                                <div className="float-left">
                                  <span className="pl-2 f-18 font-weight-bold">
                                    {' '}
                                    Review Courses &amp; Events
                                  </span>
                                  <h3 className=" pl-2  f-14 pt-2">
                                    Academic year {this.state.calendarName} | {data.programName}{' '}
                                    Program Calendar
                                  </h3>
                                </div>
                              </div>
                              <div className="col-md-6">
                                <p className="mt-3 float-right">
                                  <Button
                                    onClick={() => this.publishShow('publish', data)}
                                    variant={
                                      data.dean.review === false && !this.state.deanApprove
                                        ? 'light'
                                        : 'primary'
                                    }
                                    disabled={!data.dean.review && !this.state.deanApprove}
                                  >
                                    Publish
                                  </Button>
                                </p>
                              </div>
                            </div>

                            <hr />

                            <div className="row mt-1 pb-3">
                              <div className="col-md-12 pt-2 pb-2">
                                <p> Your Review</p>

                                {data.dean.status.charAt(0).toUpperCase() +
                                  data.dean.status.slice(1) ===
                                  'Pending' && (
                                  <div className="reply_comment">
                                    <div className="row">
                                      <div className="col-md-6">
                                        <p>{formatFullName(data.dean.name)} </p>
                                      </div>

                                      <div className="col-md-6"></div>
                                      <div className="col-md-6 float-right">
                                        <p className="text-right"></p>
                                      </div>

                                      <div className="col-md-12">
                                        <p className="mb-1"> Reply</p>

                                        {data.reply.map((option, replyIndex) => (
                                          <div key={replyIndex}>
                                            <input
                                              type="radio"
                                              className={'form-control form-radio1'}
                                              value={option.name}
                                              checked={data.replyRadio == replyIndex ? true : false} // eslint-disable-line
                                              onChange={(e) =>
                                                this.onChangeReply(option, replyIndex, index)
                                              }
                                            />
                                            <span className="radio-label1">{option.name}</span>
                                          </div>
                                        ))}

                                        <div className="pt-3">
                                          <textarea
                                            value={data.deanReply}
                                            onChange={(e) => this.handleChange(e, index)}
                                            maxLength="150"
                                            className={'form-control'}
                                            placeholder={'Add a comment'}
                                          />
                                        </div>
                                      </div>

                                      <div className="col-md-12 pt-3 ">
                                        <div className="float-right">
                                          <Button
                                            variant="primary"
                                            onClick={(e) => this.deanSubmit(e, index)}
                                          >
                                            Send{' '}
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                )}
                                {(data.dean.status === 'Done' ||
                                  data.dean.status === 're-pending') && (
                                  <div className="pb-3">
                                    <div className="reviewer-status">
                                      <div className="row">
                                        <div className="col-md-8">
                                          <p className=" mb-0">
                                            Dr. {formatFullName(data.dean.name)}{' '}
                                            <small>
                                              -{' '}
                                              {moment(data.dean.timestamp).format(
                                                'MMMM Do YYYY, h:mm:ss a'
                                              )}{' '}
                                            </small>
                                          </p>
                                        </div>

                                        <div className="col-md-4 ">
                                          <div className="float-right">
                                            {data.dean.review === false && (
                                              <Button
                                                variant="outline-danger border_cuve"
                                                size="sm"
                                              >
                                                Disapprove
                                              </Button>
                                            )}
                                            {data.dean.review === true && (
                                              <Button
                                                variant="outline-success border_cuve"
                                                size="sm"
                                              >
                                                Approve
                                              </Button>
                                            )}
                                          </div>
                                        </div>
                                        {data.dean.comment !== undefined && (
                                          <div className="col-md-10">
                                            <small>{data.dean.comment}</small>
                                          </div>
                                        )}
                                        {data.dean.review === false &&
                                          data.dean.status === 're-pending' && (
                                            <>
                                              <div className="col-md-10">
                                                Reply
                                                <div>
                                                  <input
                                                    type="radio"
                                                    className={'form-control form-radio1'}
                                                    onChange={() => {
                                                      this.setState({
                                                        deanApprove: true,
                                                      });
                                                    }}
                                                    checked={this.state.deanApprove}
                                                  />
                                                  <span className="radio-label1">Approve</span>
                                                </div>
                                              </div>
                                              <div className="col-md-12 pt-3 ">
                                                {/* <div className="float-right">
                                                <Button
                                                  variant="primary"
                                                  disabled={
                                                    !this.state.deanApprove
                                                  }
                                                  onClick={(e) =>
                                                    this.deanSubmit(e, index)
                                                  }
                                                >
                                                  Send{" "}
                                                </Button>
                                              </div> */}
                                              </div>
                                            </>
                                          )}
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                            {this.loadIframe(data)}
                          </div>
                        </div>
                      </React.Fragment>
                    </Tab>
                  );
                })}
            </Tabs>
            <Modal
              show={this.state.publishShow}
              centered
              onHide={this.completeReview}
              dialogClassName="modal-30w"
              aria-labelledby="example-custom-modal-styling-title"
            >
              <Modal.Body>
                <div className="row justify-content-center ">
                  <div className="col-md-12">
                    <p className="f-16">Publish Program Calendar</p>
                    <small>
                      Are you sure you want to publish the program calendar to the staff of
                      respective program
                    </small>

                    <p className="pt-4">Notify via</p>
                    {this.state.notifyVia.map((data, notifyIndex) => (
                      <p className="pr-4 float-left" key={notifyIndex}>
                        <input
                          type="checkbox"
                          className="calendarFormRadio"
                          onClick={(e) => this.handleAllCheckBox(e, notifyIndex)}
                          value="checkedall"
                        />{' '}
                        {data.name}
                      </p>
                    ))}

                    <div>
                      {' '}
                      <p className="f-16 text-red">{this.state.completeError}</p>{' '}
                    </div>
                  </div>
                </div>
              </Modal.Body>

              <Modal.Footer>
                <button
                  className="remove_hover btn btn-outline-primary"
                  onClick={() => this.setState({ publishShow: false })}
                >
                  CANCEL
                </button>
                <button
                  className="remove_hover btn btn-primary"
                  disabled={this.disableCompleteCheck()}
                  onClick={this.publishSubmit}
                >
                  PUBLISH
                </button>
              </Modal.Footer>
            </Modal>
          </>
        )}
      </React.Fragment>
    );
  }
}

DeanResponse.propTypes = {
  userId: PropTypes.string,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
};

const mapStateToProps = (state) => {
  return {
    isAuthenticated: state.auth.token !== null,
    token: state.auth.token,
    userId: selectUserId(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
  };
};

export default connect(mapStateToProps)(withRouter(DeanResponse));
