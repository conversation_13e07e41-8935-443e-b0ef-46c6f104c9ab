import React from 'react';
import PropTypes from 'prop-types';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import { courseOrComprehensive } from '_reduxapi/leave_management/actions';
import { setUpdateData } from '_reduxapi/actions/auth';
import { Map, fromJS } from 'immutable';
import { useDispatch, useSelector } from 'react-redux';
import { selectLmsSettings } from '_reduxapi/leave_management/selectors';
import * as actions from '_reduxapi/leave_management/actions';
import { connect } from 'react-redux';
import MButton from 'Widgets/FormElements/material/Button';
function ComprehensiveConfirmPopup(props) {
  const { confirmPopupOpen, setConfirmPopupOpen, lmsSettings, getLmsSettings } = props;

  const dispatch = useDispatch();

  const loggedInUserData = useSelector(({ auth }) => {
    const userData = fromJS(auth).get('loggedInUserData', Map());
    return userData;
  });
  const callBack = (warningModeValue) => {
    getLmsSettings('leave');
    const updatedUserData = loggedInUserData.set('warningMode', warningModeValue);
    dispatch(
      setUpdateData({
        loggedInUserData: updatedUserData.toJS(),
      })
    );
  };
  const courseOrComprehensiveMode = () => {
    setConfirmPopupOpen(confirmPopupOpen.get('ChangeWorkingSystem', ''));
    const requestData = {
      warningModeValue: confirmPopupOpen.get('ChangeWorkingSystem', ''),
      settingId: lmsSettings.get('_id', ''),
    };
    dispatch(courseOrComprehensive(requestData, callBack));
    setConfirmPopupOpen(confirmPopupOpen.set('ConfirmPopupOpen', false));
  };
  return (
    <div>
      <MaterialDialog
        show={confirmPopupOpen.get('ConfirmPopupOpen', false)}
        onClose={() => setConfirmPopupOpen(confirmPopupOpen.set('ConfirmPopupOpen', false))}
        maxWidth={'xs'}
        fullWidth={true}
      >
        <div className="p-4">
          <div className="text-center h3">Are you Sure?</div>
          <div className="f-14 text-center">
            {`Are you Sure you want to Switch ${confirmPopupOpen.get(
              'ChangeWorkingSystem',
              ''
            )} Warning System`}
          </div>
        </div>
        <div className="d-flex align-items-center justify-content-end mb-2 mr-2">
          <MButton
            variant="outlined"
            size={'small'}
            clicked={() => setConfirmPopupOpen(confirmPopupOpen.set('ConfirmPopupOpen', false))}
          >
            Cancel
          </MButton>
          <MButton
            className="ml-3"
            variant="contained"
            size={'small'}
            clicked={() => courseOrComprehensiveMode()}
          >
            Yes
          </MButton>
        </div>
      </MaterialDialog>
    </div>
  );
}

const mapStateToProps = function (state) {
  return {
    lmsSettings: selectLmsSettings(state),
  };
};

export default connect(mapStateToProps, actions)(ComprehensiveConfirmPopup);

ComprehensiveConfirmPopup.propTypes = {
  selectedWorkingSystem: PropTypes.instanceOf(Map),
  setSelectedWorkingSystem: PropTypes.func,
  lmsSettings: PropTypes.instanceOf(Map),
  getLmsSettings: PropTypes.func,
};
