import React from 'react';
import PropTypes from 'prop-types';
import {
  getURLParams,
  addPDFPageNo,
  jsUcfirst,
  formatDays,
  formatTwoString,
  getEnvCollegeName,
} from '../../../../utils';
import jsPDF from 'jspdf';
import { List } from 'immutable';
import { t } from 'i18next';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';

export default function BreadcrumbHeader(props) {
  const redirectUrl = () => props.history.push(props.callBack);
  const programName = getURLParams('programName', true);
  return (
    <div className="bg-white pt-3">
      <div className="container-fluid">
        <div className="row ">
          <div className="col-md-4">
            <p className="font-weight-bold mb-0 f-17">
              {' '}
              <i
                className="fa fa-arrow-left pr-3 remove_hover"
                aria-hidden="true"
                onClick={redirectUrl}
              ></i>
              {programName} Program{' '}
            </p>
          </div>
          <div className="col-md-8">
            {(CheckPermission(
              'tabs',
              'Schedule Management',
              'Extra Curricular and Break',
              '',
              'Extra Curricular',
              'Export'
            ) ||
              CheckPermission(
                'tabs',
                'Schedule Management',
                'Extra Curricular and Break',
                '',
                'Break',
                'Export'
              )) && (
              <div className="float-right pb-3 remove_hover" onClick={() => printPDF(props)}>
                <i className="fa fa-download pr-1" aria-hidden="true"></i>
                <b className="mb-0 f-17 text-uppercase"> {t('export')} </b>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

BreadcrumbHeader.propTypes = {
  history: PropTypes.object,
  callBack: PropTypes.func,
};

function printPDF(props) {
  const { activeInstitutionCalendar, extraCurricularAndBreakTiming } = props;
  const programName = getURLParams('programName', true);
  var doc = new jsPDF();

  let data = List();
  if (extraCurricularAndBreakTiming && extraCurricularAndBreakTiming.size > 0) {
    data = extraCurricularAndBreakTiming
      .filter((item) => item.get('isActive') === true)
      // eslint-disable-next-line array-callback-return
      .filter((item) => {
        if (
          CheckPermission(
            'tabs',
            'Schedule Management',
            'Extra Curricular and Break',
            '',
            'Extra Curricular',
            'View'
          ) &&
          CheckPermission(
            'tabs',
            'Schedule Management',
            'Extra Curricular and Break',
            '',
            'Break',
            'Edit'
          )
        ) {
          return item;
        } else if (
          CheckPermission(
            'tabs',
            'Schedule Management',
            'Extra Curricular and Break',
            '',
            'Break',
            'View'
          )
        ) {
          return item.get('type') === 'break';
        } else if (
          CheckPermission(
            'tabs',
            'Schedule Management',
            'Extra Curricular and Break',
            '',
            'Extra Curricular',
            'View'
          )
        ) {
          return item.get('type') === 'extra_curricular';
        }
      })
      .sort((a, b) => Date.parse(a.title) - Date.parse(b.title))
      .map((list, index) => {
        const type = list.get('type', '') === 'extra_curricular' ? 'Extra Curricular' : 'Break';
        const startTime =
          formatTwoString(list.getIn(['startTime', 'hour'], '')) +
          ':' +
          formatTwoString(list.getIn(['startTime', 'minute'], '')) +
          ' ' +
          list.getIn(['startTime', 'format'], '');
        const endTime =
          formatTwoString(list.getIn(['endTime', 'hour'], '')) +
          ':' +
          formatTwoString(list.getIn(['endTime', 'minute'], '')) +
          ' ' +
          list.getIn(['endTime', 'format'], '');

        return [
          index + 1,
          list.get('title', ''),
          type,
          jsUcfirst(list.get('gender', 'male')),
          formatDays(list.get('days', List())),
          startTime,
          endTime,
        ];
      });
  }
  const yearTitle = 'Academic year ' + activeInstitutionCalendar.get('calendar_name');
  doc.setFont('helvetica', 'normal');
  doc.setFontSize(12);
  doc.text(yearTitle, 14, 20);
  doc.text(getEnvCollegeName().toLowerCase(), 200, 20, null, null, 'right');
  doc.setFont('helvetica', 'normal');
  doc.text(programName, 200, 30, null, null, 'right');
  doc.line(14, 40, 200, 40);
  let lastPos = 40;
  jsPDF.autoTableSetDefaults({
    headStyles: { fillColor: '#e0e0e0', textColor: 0 },
    bodyStyles: { fillColor: '#ffffff', textColor: 0 },
  });
  doc.autoTable({
    startY: lastPos + 15,
    head: [['S.NO', 'TITLE', 'TYPE', 'GENDER', 'DAY', 'START TIME', 'END TIME']],
    body: data.toJS(),
    tableLineColor: [189, 195, 199],
    theme: 'grid',
    columnStyles: {
      5: { cellWidth: 25 },
      6: { cellWidth: 20 },
    },
  });
  addPDFPageNo(doc);
  doc.save(`${programName}-${yearTitle}-extra-curricular-and-break.pdf`);
}
