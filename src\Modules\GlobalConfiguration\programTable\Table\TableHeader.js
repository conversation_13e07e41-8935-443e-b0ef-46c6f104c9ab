import React from 'react';
import { Trans } from 'react-i18next';
import { getLabelName } from 'Modules/Shared/v2/Configurations';

function TableHeader() {
  return (
    <thead>
      <tr>
        <th>
          <div className="d-flex">
            {' '}
            <Trans i18nKey={'code'}></Trans>
          </div>
        </th>

        <th>
          <div className="d-flex">
            {' '}
            {getLabelName({ label: 'program', length: 10 })} <Trans i18nKey={'name'}></Trans>
          </div>
        </th>

        <th>
          <div className="d-flex">
            {' '}
            {getLabelName({ label: 'program', length: 10 })} <Trans i18nKey={'type'}></Trans>
          </div>
        </th>
        <th>
          <div className="d-flex">
            {' '}
            <Trans i18nKey={'degree_name'}></Trans>
          </div>
        </th>
        <th>
          <div className="d-flex">
            {' '}
            <Trans i18nKey={''}></Trans>
          </div>
        </th>
      </tr>
    </thead>
  );
}

export default TableHeader;
