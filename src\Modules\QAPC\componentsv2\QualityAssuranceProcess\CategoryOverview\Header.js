import { ArrowBack } from '@mui/icons-material';
import { Box, Divider, Paper } from '@mui/material';
import ThumbUpAltIcon from '@mui/icons-material/ThumbUpAlt';
import React, { forwardRef, useEffect, useState } from 'react';
import camera_with_outline from 'Assets/q360_dashboard/category_overview/camera_with_outline.svg';
import document_with_outline from 'Assets/q360_dashboard/category_overview/document_with_outline.svg';
import document_with_focus from 'Assets/q360_dashboard/category_overview/document_with_focus.svg';
import sent_icon from 'Assets/q360_dashboard/category_overview/sent_icon.svg';
import notepad from 'Assets/q360_dashboard/category_overview/notepad.svg';
import Buttons from 'Widgets/FormElements/material/Button';
import { useDispatch, useSelector } from 'react-redux';
import {
  getFormAddendum,
  getIncorporateFromWithData,
  getSingleFormList,
  setData,
  updateFormConfig,
} from '_reduxapi/q360/actions';
import {
  selectFormAddendum,
  selectIncorporateFromWithData,
  selectSingleFormList,
} from '_reduxapi/q360/selectors';
import { List, Map } from 'immutable';
import DisplayCapture from '../DisplayCapture/DisplayCapture';
import LocalStorageService from 'LocalStorageService';
import { useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import { useSearchParams } from 'Modules/GlobalConfigurationV2/CustomHooks/CustomHooks';
import { combiningProgramAndInstitution } from '../../Utils';
import IncorporateFromViewModal from './IncorporateFromViewModal';

const Header = forwardRef(
  (
    {
      showReferenceDoc,
      handleUserSelect,
      onDivClick,
      formInitiatorId,
      isTemplateBased,
      setSelectParticularArea,
      handleDocumentClick,
      setShowDisplayCapture,
      showDisplayCapture,
      show_reference_document,
      edit,
      documents,
      pdfDocuments,
    },
    { fetchChildComponentData, fetchTagsRef, fetchIncorporateFromRef }
  ) => {
    const dispatch = useDispatch();
    const singleFormList = useSelector(selectSingleFormList);
    const categoryFormId = singleFormList.getIn(['categoryFormId', '_id'], '');
    const formAddendum = useSelector(selectFormAddendum);

    const academicYearNames = singleFormList
      .get('formCalenderIds', List())
      .map((calender) => {
        if (!calender.get('isDeleted', false))
          return calender.getIn(['institutionCalenderId', 'calendar_name'], '');
        return '';
      })
      .join(', ');
    const history = useHistory();
    const [searchParams] = useSearchParams();
    const show_display_creation = searchParams.get('display_creation') === 'true';
    const incorporateFromWithData = useSelector(selectIncorporateFromWithData);
    const status = searchParams.get('status');
    const calendar = searchParams.get('calendar');

    const handleNextStep = () => {
      const incorporateMandatory = searchParams.get('incorporateMandatory')?.trim() === 'true';
      const referenceDocument = searchParams.get('reference_document')?.trim() === 'true';
      const incorporationSettings = searchParams.get('incorporation_settings')?.trim() === 'true';
      const displayCreation = searchParams.get('display_creation')?.trim() === 'true';
      const isSubmitted = searchParams.get('submissionStatus')?.trim() === 'submitted';
      const routeToConcludePage = () => {
        history.push(
          `/qapc/QualityAssurance/concluding_phase?incorporateMandatory=${incorporateMandatory}&formId=${formInitiatorId}&reference_document=${referenceDocument}&incorporation_settings=${incorporationSettings}&display_creation=${displayCreation}&edit=${edit}&status=${status}&calendar=${calendar}`
        );
      };
      const fetchIncorporateFrom = fetchIncorporateFromRef.current;
      let incorporateWithLike = fetchIncorporateFrom;
      const incorporateFrom = incorporateFromWithData.get('incorporateFrom', List());
      if (incorporateMandatory) {
        const checkEverySectionAreLiked = incorporateFrom.every(
          (section) =>
            section.get('isLike', false) ||
            incorporateWithLike.get(section.get('incorporateId', ''))
        );
        const sectionHasFalseValue = incorporateWithLike.some((value) => value === false);
        if (!checkEverySectionAreLiked || sectionHasFalseValue) {
          return dispatch(setData(Map({ message: 'You should like every section' })));
        }
      }
      const mergedFormId = JSON.parse(LocalStorageService.getCustomToken('mergedFormId')).map(
        (item) => item['formInitiatorId']
      );

      const pdfAttachment = List([pdfDocuments.delete('signedUrl').delete('documentType')]);
      let mergedDocs = documents
        .set('displayCapture', formAddendum.get('displayCapture', List()))
        .set('categoryFormId', categoryFormId)
        .set('formInitiatorIds', mergedFormId.concat([formInitiatorId]))
        .set('formEvidenceAttachment', documents.get('formEvidenceAttachment', List()))
        .set('pdfAttachment', pdfAttachment);

      if (isTemplateBased && pdfAttachment.size === 0) {
        const data = fetchChildComponentData.current?.getFormTemplateData();
        mergedDocs = mergedDocs.set('formTemplate', data || Map());
      }
      if (fetchTagsRef.current) {
        mergedDocs = mergedDocs.set('tags', fetchTagsRef.current);
      }
      if (formAddendum.has('_id')) {
        mergedDocs = mergedDocs.set('formGuideResourcesId', formAddendum.get('_id'));
      }
      // if (incorporateWithLike && incorporateWithLike.size) {
      incorporateWithLike = incorporateFrom
        .map((section) => ({
          incorporateId: section.get('incorporateId'),
          isLike: incorporateWithLike.has(section.get('incorporateId'))
            ? incorporateWithLike.get(section.get('incorporateId'), false)
            : section.get('isLike', false),
        }))
        .toArray();
      mergedDocs = mergedDocs.set('incorporateFrom', incorporateWithLike);
      // }

      const callBack = () => {
        dispatch(getFormAddendum({ formInitiatorId }));
        fetchIncorporateActionData(singleFormList);
        routeToConcludePage();
      };
      if (isSubmitted) {
        routeToConcludePage();
      } else {
        dispatch(updateFormConfig(mergedDocs.toJS(), callBack));
      }
    };
    const goBack = () => history.goBack();

    useEffect(() => {
      dispatch(
        getSingleFormList({ formInitiatorId: formInitiatorId, cb: fetchIncorporateActionData })
      );
    }, []);

    function fetchIncorporateActionData(singleFormList = Map()) {
      dispatch(
        getIncorporateFromWithData({
          url: `/formInitiator/getIncorporate?categoryFormCourseId=${singleFormList.get(
            'categoryFormCourseId',
            ''
          )}&formInitiatedId=${formInitiatorId}&categoryFormGroupId=${singleFormList.getIn(
            ['categoryFormGroupId', '_id'],
            ''
          )}`,
        })
      );
    }
    const groupNameFiltered = singleFormList
      .get('selectedGroupName', List())
      .filter((group) => group !== 'none');
    const term = singleFormList.getIn(['categoryFormGroupId', 'term'], '');
    const attemptType = singleFormList.getIn(['categoryFormGroupId', 'attemptTypeName'], '');

    return (
      <Paper
        className="category_overview_grid p-2 py-3"
        sx={{ boxShadow: '0px 1px 2px 0 rgba(0, 0, 0, 0.12) !important' }}
      >
        <div className="text-align-center">
          <ArrowBack className="ml-1 cursor-pointer" onClick={goBack} />
        </div>
        <div className="ml-2">
          <div className="grey_shade_2 f-24"> {singleFormList.get('formName', '')}</div>
          <div className="f-14 grey_shade_1">
            {singleFormList.get('level', '') === 'course'
              ? singleFormList.get('courseName', '')
              : singleFormList.get('level', '') === 'program'
              ? singleFormList.get('programName', '')
              : singleFormList.get('institutionName', '')}{' '}
            &#x2022; Academic Year {academicYearNames}
            {['course', 'program'].includes(singleFormList.get('level', '')) && (
              <span>
                {' '}
                {term !== 'none' && <span>&#x2022; {term} Term </span>}
                {attemptType !== 'none' && <span>&#x2022; {attemptType} Attempt Type </span>}
                {groupNameFiltered.size > 0 && (
                  <span> &#x2022; {singleFormList.get('selectedGroupName', List()).join(',')}</span>
                )}
              </span>
            )}
          </div>
        </div>
        <div className="ml-auto d-flex">
          {show_display_creation && (
            <img
              src={camera_with_outline}
              className="mr-3 remove_hover"
              alt="cam"
              onClick={edit ? () => setShowDisplayCapture(true) : () => {}}
            />
          )}
          {show_reference_document && (
            <img
              src={showReferenceDoc ? document_with_focus : document_with_outline}
              className="mr-3 remove_hover"
              alt="doc"
              onClick={handleDocumentClick}
            />
          )}
          <Buttons color={'blue'} className="mr-3" clicked={handleNextStep}>
            <div className="px-3">Next Step</div>
          </Buttons>
        </div>
        {showDisplayCapture && (
          <DisplayCapture
            onDivClick={onDivClick}
            optionOpen={showDisplayCapture}
            setOptionOpen={setShowDisplayCapture}
            handleUserSelect={handleUserSelect}
            setSelectParticularArea={setSelectParticularArea}
          />
        )}
      </Paper>
    );
  }
);

Header.Step = () => {
  return (
    <div className="form_step_parent">
      <div className="form_step_child p-3 d-flex align-items-center">
        <div>
          <img src={notepad} alt="notepad" />
        </div>
        <div>
          <p className="text-primary ml-3 font-weight-500 f-12">Step 1/2</p>
          <p className="ml-3 mt-1 font-weight-500  f-14">Form</p>
        </div>
        <Divider orientation="vertical" flexItem variant="middle" className="my-2 mx-5" />
        <div>
          <img src={sent_icon} alt="sent_icon" />
        </div>
      </div>
    </div>
  );
};

export const GuideResource = forwardRef(({ edit }, ref) => {
  const incorporateFromWith = useSelector(selectIncorporateFromWithData);
  const incorporateFrom = incorporateFromWith.get('incorporateFrom', List());
  const [showModal, setShowModal] = useState(-1);

  const [likeActions, setLikeActions] = useState(Map());
  const handleClick = (incorporateId) => (e) => {
    e && e.stopPropagation();
    setLikeActions((prev) => prev.update(incorporateId, (boolean) => !boolean));
  };
  ref.current = likeActions;

  useEffect(() => {
    let likedIds = Map();
    for (const form of incorporateFrom) {
      likedIds = likedIds.set(form.get('incorporateId'), form.get('isLike', false));
    }
    setLikeActions(likedIds);
  }, [incorporateFrom]);

  if (!incorporateFrom.size) {
    return <section id="incorporate_category_overview" />;
  }
  return (
    <section id="incorporate_category_overview">
      <p className="f-14 grey_shade_4 ">Incorporate From*</p>
      <div className="d-flex flex-wrap">
        {incorporateFrom.map((form, formIndex) => {
          const isLiked = likeActions.has(form.get('incorporateId'))
            ? likeActions.get(form.get('incorporateId', ''), false)
            : form.get('isLike', false);
          return (
            <Box
              key={form.get('incorporateId')}
              className="d-flex  align-items-center px-3 py-2 mr-3 cursor-pointer"
              sx={{
                border: `1px solid #16A34A`,
                backgroundColor: '#DCFCE7',
                borderRadius: '4px',
                width: '240px',
                marginBottom: '10px',
              }}
              onClick={(e) => {
                setShowModal(formIndex);
                handleClick(form.get('incorporateId', ''))(e);
              }}
            >
              <div className="d-flex flex-column">
                <div className="f-14">{form.get('formName', '')}</div>
                <div className="f-10">
                  {combiningProgramAndInstitution(form) + ' - ' + form.get('calendar_name', '')}
                </div>
                {/* <div className="f-10">Sections From: {form.get('sectionFrom', '')}</div>
                <div className="f-10">
                  Sections To:{' '}
                  {form
                    .get('sectionTo', List())
                    .map((section) => section.get('sectionName', ''))
                    .join(', ')}
                </div> */}
              </div>
              <div className="ml-2 mt-2">
                <ThumbUpAltIcon
                  onClick={edit ? handleClick(form.get('incorporateId', '')) : () => {}}
                  className="f-16"
                  sx={{ color: isLiked ? 'blue' : 'grey' }}
                />
              </div>
            </Box>
          );
        })}
      </div>
      {showModal !== -1 && (
        <IncorporateFromViewModal
          activeIndex={showModal}
          incorporateFrom={incorporateFrom}
          likeActions={likeActions}
          handleClose={(index = -1, form = Map()) => {
            if (index !== -1) {
              setTimeout(() => {
                handleClick(form.get('incorporateId', ''))();
              }, 1000);
            }
            setShowModal(index);
          }}
        />
      )}
    </section>
  );
});

export default Header;
