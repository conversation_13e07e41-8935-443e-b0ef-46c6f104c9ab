import React, { forwardRef, useEffect, useState } from 'react';
import Button from '@mui/material/Button';
import { PropTypes } from 'prop-types';
import CreateTags from '../../Models/CreateTags';
import { List } from 'immutable';
import { FormControlLabel, Switch } from '@mui/material';
import { Delete, Edit } from '@mui/icons-material';
import { makeStyles } from '@mui/styles';

export const switchSX = {
  '& .MuiSwitch-thumb': { background: '#147afc', width: '15px', height: '15px' },
  '& .MuiSwitch-track': {
    height: '69%',
    width: '95%',
    background: '#D1D5DB !important',
  },
};
export const useStyles = makeStyles((theme) => ({
  label: {
    fontSize: '14px', // Adjust the font size as needed
    color: '#4B5563',
    fontWeight: 400,
    // You can also add other styles here, such as fontFamily, fontWeight, etc.
  },
}));
const Tag = forwardRef(({ buttonName, tags }, ref) => {
  const [open, setOpen] = useState(false);
  const [tagData, setTagData] = useState(List());
  const classes = useStyles();

  ref.current.set('tags', tagData);
  const [editIndex, setEditIndex] = useState(-1);
  useEffect(() => {
    setTagData(tags);
  }, [tags]);

  return (
    <>
      <Button
        fullWidth
        className="text-capitalize btn-border-dotted text-primary responsiveFontSizeSmall border border-dotted"
        onClick={() => setOpen(true)}
      >
        + {buttonName}
      </Button>
      {tagData.map((tag, index) => {
        if (!tag.get('isActive', true)) {
          return null;
        }
        return (
          <section className="d-flex align-items-center " key={index}>
            <div className="flex-grow-1 p-3">
              <p className="m-0 pb-1">
                <span className="f-15 fw-500">{tag.get('name', '')}</span>
                <span className="f-14 fw-400 text-lGrey ml-2">
                  {tag.get('isDefault', false) ? '(Default)' : ''}
                </span>
              </p>
              <p className="m-0 f-14 fw-400 text-lGrey">Level:{tag.get('level', '')}</p>
            </div>
            <FormControlLabel
              checked={tag.get('isDefault', false)}
              className="m-0"
              control={<Switch defaultChecked sx={switchSX} />}
              classes={{ label: classes.label }}
              label={tag.get('isDefault', false) ? 'ON' : 'OFF'}
            />
            <Edit
              className="ml-3 cursor-pointer"
              onClick={() => setEditIndex(index)}
              fontSize="small"
            />
            <Delete
              className="text-red ml-3 cursor-pointer"
              fontSize="small"
              onClick={() => setTagData((prev) => prev.setIn([index, 'isActive'], false))}
            />
          </section>
        );
      })}
      {open && (
        <CreateTags open={true} handleClose={() => setOpen(false)} setTagData={setTagData} />
      )}
      {editIndex !== -1 && (
        <CreateTags
          open={true}
          editIndex={editIndex}
          existData={tagData.get(editIndex)}
          handleClose={() => {
            setEditIndex(-1);
          }}
          setTagData={setTagData}
        />
      )}
    </>
  );
});
Tag.propTypes = {
  buttonName: PropTypes.string,
  tags: PropTypes.instanceOf(List),
};
export default Tag;
