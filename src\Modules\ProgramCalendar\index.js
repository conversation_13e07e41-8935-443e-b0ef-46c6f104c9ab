import React, { useEffect, createContext } from 'react';
import { Route, Switch } from 'react-router';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { List, Map } from 'immutable';
import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import {
  selectIsLoading,
  selectMessage,
  selectBreadCrumb,
  selectProgramCalendarDashboard,
  selectProgramCalendarLanding,
} from '../../_reduxapi/program_calendar/selectors';
import Loader from '../../Widgets/Loader/Loader';
import SnackBars from '../../Modules/Utils/Snackbars';
import * as actions from '../../_reduxapi/program_calendar/action';

import HomeComponent from './components/HomeComponent';
import CalendarSettings from './components/CalendarSettings';
import Courses from './components/Courses';

import { selectActiveInstitutionCalendar, selectUserInfo } from '_reduxapi/Common/Selectors';
import { CheckPermission, CheckProgramDepartment } from 'Modules/Shared/Permissions';
import { removeURLParams } from 'utils';
import LocalStorageService from 'LocalStorageService';

export const PCContext = createContext({});

function ProgramInput({
  message,
  breadcrumbs,
  isLoading,
  setBreadCrumbName,
  getProgramCalendarDashboard,
  getProgramCalendarLanding,
  activeInstitutionCalendar,
  programCalendarDashboard,
  programCalendarLanding,
  loggedInUserData,
  //getICEventList
  history,
}) {
  let search = window.location.search;
  let params = new URLSearchParams(search);
  let programName = params.get('programName');
  let programId = params.get('programId');
  let year = params.get('year');
  let term = params.get('term');
  const calendarId = activeInstitutionCalendar.get('_id', '');
  const pathName = history?.location?.pathname;
  useEffect(() => {
    if (pathName.includes('/pc/courses')) {
      setBreadCrumbName(`${programName} Program Calendar`); // > Add Course
    } else {
      setBreadCrumbName(`${programName} Program Calendar`);
    }
  }, [setBreadCrumbName, programName, pathName]);

  // const fetchICList = useCallback(() => {
  //   getICEventList(calendarId);
  // }, [calendarId, getICEventList]);

  // useEffect(() => {
  //   if (calendarId !== '') {
  //     fetchICList();
  //   }
  // }, [calendarId, fetchICList]);

  useEffect(() => {
    if (programId !== null) {
      getProgramCalendarDashboard(programId);
    }
  }, [programId, getProgramCalendarDashboard]);

  useEffect(() => {
    if (programId !== null && calendarId !== '') {
      getProgramCalendarLanding(programId, calendarId);
    }
  }, [programId, calendarId]); // eslint-disable-line

  const items = [
    {
      to: '#',
      label: breadcrumbs !== '' ? breadcrumbs : 'Program Calendar',
    },
  ];
  const currentProgramCalendarId = programCalendarLanding.getIn([0, '_id'], '');
  const publishedStatus = programCalendarLanding.getIn([0, 'status'], '');
  const isActive =
    publishedStatus === 'published' ||
    CheckPermission('pages', 'Program Calendar', 'Dashboard', 'Calendar Settings');

  const yearArray = programCalendarDashboard.get('year_level', List());
  const terms = programCalendarDashboard.get('term', List());
  useEffect(() => {
    const savedPgId = LocalStorageService.getCustomToken('sp-id-new');
    if (yearArray.size > 0 && terms.size > 0) {
      LocalStorageService.setCustomToken('sp-id-new', programId);
      if (savedPgId === null || savedPgId !== programId) {
        if (savedPgId !== programId) {
          const location = history.location;
          const year = yearArray.getIn([0, 'year'], 'year1');
          const term = terms.getIn([0, 'term_name'], 'regular');
          const changedPath = location.pathname;
          const pathSearch =
            removeURLParams(location, ['year', 'term']) + `&year=${year}&term=${term}`;
          history.push(changedPath + pathSearch);
        }
      }
    }
    return () => {
      localStorage.removeItem('sp-id-new');
    };
  }, [programId, yearArray, terms]); //eslint-disable-line

  const indexData = {
    programCalendarDashboard,
    programCalendarLanding,
    isLoading,
    activeInstitutionCalendar,
    programId,
    programName,
    activeYear: year,
    userId: loggedInUserData.get('_id'),
    currentProgramCalendarId,
    calendarId,
    currentPGAccess: CheckProgramDepartment(programId, true),
    iframeShow: params.get('iframeShow') === 'true' ? true : false,
    activeTerm: term,
    hasAdminAccess: isActive,
  };
  //console.log('indexData123', indexData);
  return (
    <PCContext.Provider value={indexData}>
      {message !== '' && <SnackBars show={true} message={message} />}
      <Loader isLoading={isLoading} />
      <Breadcrumb>
        {items.map(({ to, label }) => (
          <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
            {label}
          </Link>
        ))}
      </Breadcrumb>
      <Switch>
        <Route exact path="/pc" render={(props) => <HomeComponent {...props} />}></Route>
        {indexData.hasAdminAccess && (
          <>
            <Route
              path="/pc/calendar-settings"
              render={(props) => <CalendarSettings {...props} />}
            ></Route>
            <Route path="/pc/courses" render={(props) => <Courses {...props} />}></Route>
          </>
        )}
      </Switch>
    </PCContext.Provider>
  );
}

ProgramInput.propTypes = {
  isLoading: PropTypes.bool,
  message: PropTypes.string,
  setBreadCrumbName: PropTypes.func,
  breadcrumbs: PropTypes.string,
  getProgramCalendarDashboard: PropTypes.func,
  getProgramCalendarLanding: PropTypes.func,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  programCalendarDashboard: PropTypes.instanceOf(Map),
  programCalendarLanding: PropTypes.instanceOf(List),
  loggedInUserData: PropTypes.instanceOf(Map),
  history: PropTypes.object,
  //getICEventList: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
    breadcrumbs: selectBreadCrumb(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    programCalendarDashboard: selectProgramCalendarDashboard(state),
    programCalendarLanding: selectProgramCalendarLanding(state),
    loggedInUserData: selectUserInfo(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(ProgramInput);
