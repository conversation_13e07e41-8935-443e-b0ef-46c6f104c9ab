import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import Switch from '@mui/material/Switch';
import { FormControlLabel, FormGroup } from '@mui/material';

import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MButton from 'Widgets/FormElements/material/Button';
import { getStaffOptionList } from '_reduxapi/course_scheduling/action';
import StaffSelectInput from '../Shared/StaffSelectInput';
import { formattedFullName } from 'Modules/ReportsAndAnalytics/utils';

export default function ManualAttendanceModal({
  open,
  handleClose,
  manualStaff,
  setManualStaff,
  manualStaffSaveSetting,
  setManualModalOpen,
}) {
  const dispatch = useDispatch();
  const staffOptions = useSelector((state) =>
    state.courseScheduling.get('staffOptionList', List())
  );
  const [modalState, setModalState] = useState(Map());

  useEffect(() => {
    setModalState(manualStaff);
  }, [manualStaff]);
  const [staffOptionList, setStaffOptionList] = useState(List());

  useEffect(() => {
    dispatch(getStaffOptionList());
  }, []); //eslint-disable-line

  useEffect(() => {
    const staffOptionListAr = staffOptions.map((item) => {
      return Map({
        name: formattedFullName(item.get('user_name', Map()).toJS()),
        value: item.get('_user_id'),
        _id: item.get('_id'),
      });
    });
    setStaffOptionList(staffOptionListAr);
  }, [staffOptions]);

  const handleSubmit = () => {
    let constructStaff = List();
    if (modalState.get('status', true)) {
      const scheduleData = modalState.get('selectedStaff', List());
      const manualStaffArray = scheduleData.map((staff) =>
        staffOptions.find((staffObj) => staffObj.get('_user_id', '') === staff)
      );

      constructStaff = manualStaffArray.map((item) => {
        return {
          staffName: item.get('user_name', Map()),
          staffId: item.get('_user_id', ''),
        };
      });
    }
    const payLoad = {
      manualAttendance: modalState.get('manualAttendance', true),
      isStaffAssigned: modalState.get('status', true),
      assignedStaffIds: constructStaff.toJS(),
    };

    const callBack = () => {
      setManualModalOpen(false);
    };

    manualStaffSaveSetting(payLoad, callBack);
  };

  const toCheckRemovedStaff = () => {
    //Removed Staffs are undefined
    const arrayWithRemovedStaff = modalState.get('selectedStaff', List()).map((value, index) => {
      const selectedStaffName = staffOptionList?.find((staff) => {
        return staff.get('value', '') === value;
      });
      return selectedStaffName;
    });
    const isRemovedStaffExists = arrayWithRemovedStaff.some((item) => item === undefined);

    return isRemovedStaffExists
      ? modalState.get('status', false)
      : modalState.get('selectedStaff', List()).size === 0 && modalState.get('status', false);
  };

  return (
    <MaterialDialog show={open} onClose={handleClose} maxWidth={'sm'} fullWidth={true}>
      <div className="w-100 p-4">
        <p className="mb-3 pb-2 border-bottom bold f-19">Secondary Attendance</p>
        <div className="pb-3">
          <div className="border border-radious-8 p-2">
            <div className="d-flex justify-content-between">
              <div className="mt-2">
                <span className="f-14 text-gray">
                  The staff has been assigned for taking secondary attendance
                </span>
              </div>
              <FormGroup>
                <FormControlLabel
                  control={
                    <Switch
                      checked={modalState.get('status', false)}
                      onClick={(e) => setModalState(modalState.set('status', e.target.checked))}
                    />
                  }
                  label={modalState.get('status', false) ? 'Yes' : 'No'}
                />
              </FormGroup>
            </div>

            {modalState.get('status', false) && (
              <StaffSelectInput
                modalState={modalState}
                setModalState={setModalState}
                staffOptionList={staffOptionList}
              />
            )}
          </div>
        </div>
        <div className="d-flex justify-content-end border-top pt-3">
          <MButton
            variant="outlined"
            color="primary"
            className={'mr-2'}
            clicked={() => {
              handleClose('onClose');
              setModalState(manualStaff);
            }}
          >
            Cancel
          </MButton>
          <MButton
            disabled={toCheckRemovedStaff()}
            variant="contained"
            clicked={() => handleSubmit()}
            color="primary"
          >
            Submit
          </MButton>
        </div>
      </div>
    </MaterialDialog>
  );
}

ManualAttendanceModal.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  manualStaffSaveSetting: PropTypes.func,
  setManualModalOpen: PropTypes.func,
  manualStaff: PropTypes.instanceOf(Map),
  setManualStaff: PropTypes.func,
  courseId: PropTypes.string,
  programId: PropTypes.string,
};
