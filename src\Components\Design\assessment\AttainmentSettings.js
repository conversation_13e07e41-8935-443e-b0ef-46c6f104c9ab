import React from 'react';
import MButton from 'Widgets/FormElements/material/Button';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MaterialInput from 'Widgets/FormElements/material/Input';
import AttainmentSettings from '../../../Assets/attainment_settings.svg';

function attainmentSettings(props) {
  const programType = [
    {
      name: 'CO, PO',
      value: 'CO, PO',
    },
    {
      name: 'CO, PO',
      value: 'CO, PO',
    },
  ];

  const regulationName = [
    {
      name: 'Regulation Name 1',
      value: 'Regulation Name 1',
    },
    {
      name: 'Regulation Name 2',
      value: 'Regulation Name 2',
    },
  ];

  const regulationYear = [
    {
      name: '2022',
      value: '2022',
    },
    {
      name: '2023',
      value: '2023',
    },
  ];

  const curricullumVersion = [
    {
      name: 'Version 1.0',
      value: 'Version 1.0',
    },
    {
      name: 'Version 2.0',
      value: 'Version 2.0',
    },
  ];

  const nodeType = [
    {
      name: 'Direct',
      value: 'Direct',
    },
    {
      name: 'Direct',
      value: 'Direct',
    },
  ];

  const rootNode = [
    {
      name: '--',
      value: '--',
    },
    {
      name: '--',
      value: '--',
    },
  ];

  return (
    <div className="container">
      <div className="pt-5 col-md-12 text-center">
        <div className="course_master">
          <img src={AttainmentSettings} alt="attainment settings" />
          <div>
            <h3 className="font-weight-normal pt-3">Attainment Settings</h3>
            <p className="bold">Plan your attainment calculation with the tree structure</p>
          </div>
          <div className="col-md-12">
            <MButton variant="contained" color="primary">
              Create Attainment Tree
            </MButton>
          </div>
        </div>
      </div>
      <MaterialDialog maxWidth={'xs'} fullWidth={true}>
        <div className="w-100 p-4">
          <p className="mb-3 pb-2 border-bottom bold f-19"> Create Attainment Tree</p>

          <div className="mt-2 mb-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: regulationYear }}
              label={'Regulation Year'}
            />
          </div>

          <div className="mt-2 mb-2 ">
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              label={'Regulation Name'}
              placeholder={'Regulation Name 1'}
            />
          </div>

          <div className="mt-2 mb-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: curricullumVersion }}
              label={'Curricullum'}
            />
          </div>

          <div className="mt-2 mb-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: programType }}
              label={'Select Outcome'}
            />
          </div>

          <div className="d-flex justify-content-end border-top pt-3">
            <MButton variant="outlined" color="primary" className={'mr-2'}>
              Cancel
            </MButton>
            <MButton variant="contained" color="primary">
              Save
            </MButton>
          </div>
        </div>
      </MaterialDialog>

      <MaterialDialog maxWidth={'xs'} fullWidth={true}>
        <div className="w-100 p-4">
          <p className="mb-3 pb-2 border-bottom bold f-19">Add Node</p>

          <div className="mt-2 mb-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: nodeType }}
              label={'NODE TYPE'}
            />
          </div>

          <div className="mt-2 mb-2 ">
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              label={'NODE NAME'}
              placeholder={'IT 1'}
            />
          </div>

          <div className="mt-2 mb-2 ">
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              label={'NODE WEIGHTAGE'}
              placeholder={'80%'}
            />
          </div>

          <div className="d-flex justify-content-end border-top pt-3">
            <MButton variant="outlined" color="primary" className={'mr-2'}>
              Cancel
            </MButton>
            <MButton variant="contained" color="primary">
              Save
            </MButton>
          </div>
        </div>
      </MaterialDialog>

      <MaterialDialog maxWidth={'xs'} fullWidth={true}>
        <div className="w-100 p-4">
          <p className="mb-3 pb-2 border-bottom bold f-19">
            {' '}
            Create Attainment Tree For Regulation - 2022.V2
          </p>

          <div className="mt-2 mb-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: regulationYear }}
              label={'Regulation Year'}
            />
          </div>

          <div className="mt-2 mb-2 ">
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              label={'Regulation Name'}
              placeholder={'Regulation Name 1'}
            />
          </div>

          <div className="mt-2 mb-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: curricullumVersion }}
              label={'Curricullum'}
            />
          </div>

          <div className="mt-2 mb-2">
            <MaterialInput
              elementType={'materialSelect'}
              type={'text'}
              variant={'outlined'}
              size={'small'}
              elementConfig={{ options: rootNode }}
              label={'Select Root Node'}
            />
          </div>

          <div className="d-flex justify-content-end border-top pt-3">
            <MButton variant="outlined" color="primary" className={'mr-2'}>
              Cancel
            </MButton>
            <MButton variant="contained" color="primary">
              Save
            </MButton>
          </div>
        </div>
      </MaterialDialog>
    </div>
  );
}

export default attainmentSettings;
