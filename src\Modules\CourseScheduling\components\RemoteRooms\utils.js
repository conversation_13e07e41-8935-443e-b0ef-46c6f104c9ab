import { t } from 'i18next';
import { Map } from 'immutable';
import { isValidEmail, isValidUrl } from '../../../../utils';

const roomFieldNames = {
  meetingTitle: 'meetingTitle',
  gender: 'gender',
  remotePlatform: 'remotePlatform',
  meetingUrl: 'meetingUrl',
  meetingId: 'meetingId',
  meetingUsername: 'meetingUsername',
  passCode: 'passCode',
  apiKey: 'apiKey',
  apiSecretKey: 'apiSecretKey',
};
// const roomFields = [
//   'meetingTitle',
//   'gender',
//   'remotePlatform',
//   'meetingUrl',
//   'meetingId',
//   'meetingUsername',
//   'passCode',
//   'associatedEmail',
//   'password',
//   'apiKey',
//   'apiSecretKey',
// ];

export function getTrimmedValues(room) {
  let updatedRoom = Map();
  const roomFields = ['meetingTitle', 'gender', 'remotePlatform'];
  const isZoom = room.get('remotePlatform') === 'zoom';
  if (isZoom) {
    roomFields.push(
      'meetingUrl',
      'meetingId',
      'meetingUsername',
      'passCode',
      'associatedEmail',
      'password',
      'apiKey',
      'apiSecretKey'
    );
  }
  roomFields.forEach((key) => {
    updatedRoom = updatedRoom.set(key, room.get(key, '').trim());
  });
  return updatedRoom;
}

export function validateIfRoomUpdated(original, updated) {
  let isUpdated = true;
  const roomFields = ['meetingTitle', 'gender', 'remotePlatform'];
  const isZoom = original.get('remotePlatform') === 'zoom';
  if (isZoom) {
    roomFields.push(
      'meetingUrl',
      'meetingId',
      'meetingUsername',
      'passCode',
      'associatedEmail',
      'password',
      'apiKey',
      'apiSecretKey'
    );
  }
  roomFields.forEach((key) => {
    if (!isUpdated) return;
    isUpdated = original.get(key) === updated.get(key);
  });
  return isUpdated;
}

function checkMeetingTitleExists(room, allRooms) {
  return Boolean(allRooms.find((r) => r.get('meetingTitle') === room.get('meetingTitle')));
}

export function validateRoom(room, allRooms) {
  if (checkMeetingTitleExists(room, allRooms))
    return t('infra_management.remote.error_msgs.meeting_title_already_exists');
  const errorFields = [];
  const errorArray = ['meetingTitle', 'gender', 'remotePlatform'];
  const isZoom = room.get('remotePlatform') === 'zoom';
  if (isZoom) {
    errorArray.push(
      'meetingUrl',
      'meetingId',
      'meetingUsername',
      'passCode',
      'apiKey',
      'apiSecretKey'
    );
  }
  errorArray.forEach((key) => {
    if (!room.get(key, '')) {
      errorFields.push(roomFieldNames[key]);
    }
  });
  if (errorFields.length) {
    return (
      t(`infra_management.remote.remote_rooms_list_table_headers.${errorFields[0]}`) +
      ' ' +
      t('infra_management.remote.is_required')
    );
  }

  const meetingUrl = room.get('meetingUrl', '');
  if (!isValidUrl(meetingUrl) && isZoom) {
    return t('infra_management.remote.error_msgs.invalid_meeting_url');
  }

  const associatedEmail = room.get('associatedEmail', '');
  if (associatedEmail && !isValidEmail(associatedEmail) && isZoom) {
    return t('infra_management.remote.error_msgs.invalid_associated_email');
  }

  return '';
}
