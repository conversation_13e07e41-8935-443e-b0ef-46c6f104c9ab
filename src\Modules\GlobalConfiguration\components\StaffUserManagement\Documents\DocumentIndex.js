import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Checkbox,
  FormControl,
  FormControlLabel,
  IconButton,
  Menu,
  MenuItem,
  Radio,
  RadioGroup,
} from '@mui/material';
import { Trans } from 'react-i18next';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import MSwitch from 'Widgets/FormElements/material/Switch';
import EditIcon from 'Assets/edit_mode.svg';
import DeleteIcon from 'Assets/delete_icon_dark.svg';
import MButton from 'Widgets/FormElements/material/Button';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { AccordionProgramInput } from '../../ReusableComponent';
import * as actions from '_reduxapi/global_configuration/actions';
import { t } from 'i18next';
import { connect } from 'react-redux';
import Delete from 'Containers/Modal/Delete';
import { selectDocumentConfigurationDetails } from '_reduxapi/global_configuration/selectors';
import { fromJS, List, Map } from 'immutable';
import { checkBoxStyles, fileFormats } from 'Modules/GlobalConfiguration/utils';
import CategoryModal from 'Modules/GlobalConfiguration/modal/AddEditCategoryModal';
import DocumentModal from 'Modules/GlobalConfiguration/modal/AddEditDocumentModal';
import { checkOnlyNumber } from 'v2/utils';
import MailConfig from './MailConfig';

const DocumentIndex = (props) => {
  const {
    setAccordionHeader,
    accordionOpen,
    setAccordionOpen,
    count,
    setData,
    settingId,
    getDocumentConfig,
    documents,
    updateDocumentFormatCheck,
    postDocumentCategory,
    updateDocumentCategory,
    postDocument,
    updateDocument,
    postMultipleDocumentCheck,
    updateDocumentRemainder,
    type,
  } = props;
  const isIndependent = documents.get('documentSize', '') === 'independent';
  const [categoryOpen, setCategoryOpen] = useState({});
  const [deleteCategoryModal, setDeleteCategoryModal] = useState(false);
  const [deleteDocumentModal, setDeleteDocumentModal] = useState(false);
  const [itemState, setItemState] = useState(Map());
  const [addCategoryModalOpen, setAddCategoryOpenModal] = useState(false);
  const [categoryIdState, setCategoryIdState] = useState('');
  const [addEditDocumentModal, setAddEditDocumentModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(List());

  const classes = checkBoxStyles();

  const today = new Date();
  today.setHours(0, 0);

  const MenuOptions = ({ data = Map() }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const menuOpen = Boolean(anchorEl);
    const handleMenuClose = () => setAnchorEl(null);

    const handleMenuAction = (type, e) => {
      handleMenuClose();
      handleCategoryModalOpen(data, type);
    };
    const handleClickMenuAction = (e) => {
      e.stopPropagation();
      setAnchorEl(e.currentTarget);
    };
    return (
      <>
        <IconButton
          aria-label="more"
          aria-controls="long-menu"
          aria-haspopup="true"
          onClick={(e) => handleClickMenuAction(e)}
        >
          <MoreVertIcon />
        </IconButton>
        <Menu
          id="long-menu"
          anchorEl={anchorEl}
          keepMounted
          open={menuOpen}
          onClose={handleMenuClose}
          PaperProps={{
            style: {
              maxHeight: 48 * 4.5,
              width: '20ch',
            },
          }}
        >
          <MenuItem onClick={(e) => handleMenuAction('edit', e)}>
            <Trans i18nKey={'edit'} />
          </MenuItem>
          <MenuItem onClick={(e) => handleMenuAction('delete', e)}>
            <Trans i18nKey={'delete'} />
          </MenuItem>
        </Menu>
      </>
    );
  };
  MenuOptions.propTypes = {
    data: PropTypes.object,
  };
  const handleCallBack = () => {
    settingId && getDocumentConfig(settingId, type);
  };
  const handleFormatChecked = (e, dataType) => {
    const documentFormat = documents.get('documentFormat', List());
    const documentSize = documents.get('documentSize', 1);
    const maximumSize = documents.get('documentMaximumSize', 6);

    if (dataType === 'format') {
      if (e.target.checked) {
        let updatedNewArr = documentFormat.push(e.target.value);
        updateDocumentFormatCheck(
          {
            settingId,
            documentFormat: updatedNewArr,
            documentSize,
            maximumSize,
          },
          () => {},
          type
        );
      } else {
        let updatedNewArr = documentFormat.filter((item) => item !== e.target.value);
        updateDocumentFormatCheck(
          {
            settingId,
            documentFormat: updatedNewArr,
            documentSize,
            maximumSize,
          },
          () => {},
          type
        );
      }
    }
    if (dataType === 'size') {
      updateDocumentFormatCheck(
        {
          settingId,
          documentFormat,
          documentSize: e.target.value,
          maximumSize,
        },
        () => {},
        type
      );
    }
    if (dataType === 'maxSize') {
      if (!checkOnlyNumber(e.target.value) && e.target.value !== '') {
        return false;
      } else {
        updateDocumentFormatCheck(
          {
            settingId,
            documentFormat,
            documentSize,
            maximumSize: e.target.value,
          },
          () => {},
          type
        );
      }
    }
  };
  const DocumentFormat = () => {
    return (
      <>
        <div className="bold digi-pt-8 col-12 col-sm-12 col-md-12 col-xl-4 col-lg-5">
          Document Format
        </div>

        <div className="col-sm-12 col-12 col-md-12 col-xl-7 col-lg-7 remove_hover bold mb-0">
          {fileFormats.map((fileFormat, index) => (
            <FormControlLabel
              key={index}
              value={fileFormat}
              control={
                <Checkbox
                  color="primary"
                  classes={{ root: classes.root }}
                  checked={documents.get('documentFormat', List()).includes(fileFormat)}
                  onChange={(e) => handleFormatChecked(e, 'format')}
                />
              }
              label={fileFormat.toUpperCase()}
              labelPlacement="end"
              className="mb-0"
            />
          ))}
        </div>
      </>
    );
  };
  const DocumentSize = () => {
    return (
      <>
        <div className="bold digi-pt-8 col-12 col-sm-12 col-md-12 col-xl-4 col-lg-5">
          <Trans i18nKey={'global_configuration.Document_Size'} />
        </div>

        <div className="col-sm-12 col-12 col-md-12 col-xl-7 col-lg-7 remove_hover bold mb-0">
          <FormControl component="fieldset">
            <RadioGroup
              row
              aria-label="position"
              name="position"
              value={documents.get('documentSize', '')}
              onChange={(e) => handleFormatChecked(e, 'size')}
            >
              <FormControlLabel
                className="px-0"
                value="common"
                control={<Radio color="primary" />}
                label={<Trans i18nKey={'settings.constants.common'}></Trans>}
                labelPlacement="end"
              />
              <FormControlLabel
                className="px-4"
                value="independent"
                control={<Radio color="primary" />}
                label={<Trans i18nKey={'global_configuration.independant'}></Trans>}
                labelPlacement="end"
              />
            </RadioGroup>
          </FormControl>
        </div>
      </>
    );
  };
  const DocumentMaxSize = () => {
    return (
      <>
        {!isIndependent ? (
          <>
            <div className="offset-xl-4 offset-lg-5 col-md-4 col-5 col-xl-3 col-lg-3">
              <div className="d-flex align-items-center">
                <div className="w-50">
                  <MaterialInput
                    elementType={'materialInput'}
                    type={'text'}
                    variant={'outlined'}
                    size={'small'}
                    value={documents.get('documentMaximumSize', 10)}
                    changed={(e) => handleFormatChecked(e, 'maxSize')}
                  />
                </div>
                <p className="pl-2 text-lightgray mt-3">MB</p>
              </div>
            </div>
          </>
        ) : (
          ''
        )}
      </>
    );
  };
  const handleMultipleDocumentCheck = (e, index) => {
    e.stopPropagation();
    const checked = e.target.checked;
    const id = e.target.value;
    postMultipleDocumentCheck(id, settingId, index, checked, type);
  };
  const AccordionHeader = ({ document, index }) => {
    const name = document.get('documentCategory', '');
    const desc = document.get('documentCategoryDescription', '');
    const catId = document.get('_id', '');
    return (
      <div className="row w-100 no-gutters">
        <div className="col-md-4 col-3 col-xl-7 col-lg-6">
          <div className="f-16 digi-brown">
            <b>{name}</b>
            <p className="text-lightgray mb-0">{desc}</p>
          </div>
        </div>
        <div
          className="col-md-8 col-9 col-xl-5 col-lg-6 text-right pl-0 mt--10"
          onClick={(e) => e.stopPropagation()}
        >
          {' '}
          <FormControlLabel
            className="digi-brown mb-0 mr-0"
            control={
              <Checkbox
                classes={{ root: classes.root }}
                defaultChecked
                color="primary"
                checked={document.get('allowMultipleDocuments', false)}
                onChange={(e) => handleMultipleDocumentCheck(e, index)}
              />
            }
            value={document.get('_id', false)}
            label={t('global_configuration.Allow_multiple_documents')}
          />
          <MenuOptions data={{ name, desc, catId }} />
        </div>
      </div>
    );
  };
  AccordionHeader.propTypes = {
    document: PropTypes.instanceOf(Map),
    index: PropTypes.number,
  };

  const handleDocumentModalOpen = ({
    doc = {},
    categoryId,
    categoryName,
    operation,
    documents = [],
  }) => {
    setCategoryIdState(categoryId);

    if (operation === 'edit') {
      setItemState({
        labelName: doc.get('labelName', ''),
        size: doc.get('size', ''),
        isMandatory: doc.get('isMandatory', ''),
        id: doc.get('_id', ''),
        isActive: doc.get('isActive', ''),
        categoryName: categoryName,
      });
      setAddEditDocumentModal(true);
    } else if (operation === 'add') {
      setSelectedCategory(documents);
      setItemState({ categoryName: categoryName });
      setAddEditDocumentModal(true);
    } else {
      setItemState({ labelName: doc.get('labelName', ''), id: doc.get('_id', '') });
      setDeleteDocumentModal(true);
    }
  };
  const handleCategoryModalOpen = (item, operation) => {
    setCategoryIdState(item.catId);

    if (operation === 'edit') {
      setItemState({
        categoryId: item._id,
        documentCategory: item.name,
        documentCategoryDescription: item.desc,
      });
      setAddCategoryOpenModal(true);
    } else if (operation === 'add') {
      setItemState({});
      setAddCategoryOpenModal(true);
    } else {
      setItemState({ documentCategory: item.name, documentCategoryDescription: item.desc });
      setDeleteCategoryModal(true);
    }
  };
  const handleMandatorySwitch = (e, item, documentIndex, categoryId, categoryIndex) => {
    updateDocument({
      operation: 'update',
      settingId,
      name: item.get('labelName', ''),
      size: item.get('size', 1),
      isActive: item.get('isActive', 1),
      categoryId,
      isMandatory: e.target.checked,
      documentId: item.get('_id', 1),
      documentIndex,
      categoryIndex,
      type,
    });
  };
  const handleDocumentMaxSizeChange = (
    e,
    item,
    documentIndex,
    categoryId,
    categoryIndex,
    documentId
  ) => {
    const value = e.target.value;
    if (!checkOnlyNumber(e.target.value) && e.target.value !== '') {
      return false;
    } else {
      setTimeout(() => {
        updateDocument({
          operation: 'update',
          settingId,
          name: item.get('labelName', ''),
          size: value,
          isActive: item.get('isActive', false),
          categoryId,
          isMandatory: item.get('isMandatory', false),
          documentId,
          documentIndex,
          categoryIndex,
          type,
        });
      }, 500);
    }
  };
  const handleDocumentCheck = (e, item, documentIndex, categoryId, categoryIndex) => {
    const { value, checked } = e.target;
    updateDocument({
      operation: 'update',
      settingId,
      name: item.get('labelName', ''),
      size: item.get('size', 1),
      isActive: checked,
      categoryId,
      isMandatory: !checked ? false : item.get('isMandatory', false),
      documentId: value,
      documentIndex,
      categoryIndex,
      type,
    });
  };
  const AccordionContent = ({ documentCategory, categoryIndex }) => {
    const docs = documentCategory.get('document', List());
    const categoryId = documentCategory.get('_id', '');
    const categoryName = documentCategory.get('documentCategory', '');

    return (
      <div className="container ml-4 pl-1 pr-0">
        {docs.size > 0 ? (
          docs.map((item, index) => (
            <div key={index} className="row align-items-center mt-2">
              <div className="col-4">
                <FormControlLabel
                  {...(!item.get('isActive', false) && { classes: { label: classes.label } })}
                  control={
                    <Checkbox
                      classes={{ root: classes.root }}
                      color="primary"
                      checked={item.get('isActive', false)}
                      onChange={(e) =>
                        handleDocumentCheck(e, item, index, categoryId, categoryIndex)
                      }
                    />
                  }
                  value={item.get('_id', false)}
                  label={item.get('labelName', '')}
                  className="mb-0 mr-0 break-word"
                />
              </div>
              {isIndependent ? (
                <>
                  <div className="col-4">
                    <div className="d-flex align-items-center">
                      <div className=" text-lightgray text-right pr-2 f-14">Max</div>
                      <div className="w-50">
                        <MaterialInput
                          disabled={!item.get('isActive', true)}
                          elementType={'materialInput'}
                          type={'text'}
                          variant={'outlined'}
                          size={'small'}
                          value={item.get('size', 1)}
                          changed={(e) =>
                            handleDocumentMaxSizeChange(
                              e,
                              item,
                              index,
                              categoryId,
                              categoryIndex,
                              item.get('_id', '')
                            )
                          }
                        />
                      </div>
                      <p className="text-lightgray mb-0 pl-2 f-14">MB</p>
                    </div>
                  </div>
                </>
              ) : (
                <div className="col-4"></div>
              )}

              <div className="col-4 d-flex justify-content-between align-items-center p-0">
                <div className="text-right pl-5 ml-2">
                  <img
                    src={EditIcon}
                    alt="edit"
                    className={`digi-pr-12 ${
                      !item.get('isActive', false) ? 'hide_icons' : 'remove_hover'
                    }`}
                    onClick={(e) => {
                      item.get('isActive', true) &&
                        handleDocumentModalOpen({
                          doc: item,
                          categoryId,
                          categoryName,
                          operation: 'edit',
                        });
                    }}
                  />

                  <img
                    src={DeleteIcon}
                    alt="Delete"
                    className={`digi-pr-12 ${
                      !item.get('isActive', false) ? 'hide_icons' : 'remove_hover'
                    }`}
                    onClick={(e) => {
                      item.get('isActive', true) &&
                        handleDocumentModalOpen({ doc: item, categoryId, operation: 'delete' });
                    }}
                  />
                </div>
                <div className="text-right">
                  <MSwitch
                    checked={item.get('isMandatory', true)}
                    onChange={(e) =>
                      handleMandatorySwitch(e, item, index, categoryId, categoryIndex)
                    }
                    disabled={!item.get('isActive', true)}
                  />
                </div>
              </div>
            </div>
          ))
        ) : (
          <p className="text-center">No documents added</p>
        )}
        <MButton
          variant="text"
          className="text-blue mt-2 mb-3 pl-0"
          clicked={() =>
            handleDocumentModalOpen({ categoryId, categoryName, operation: 'add', documents: docs })
          }
        >
          + <Trans i18nKey={'global_configuration.add_new_document'} />
        </MButton>
        {categoryIndex !== documents.get('chooseDocuments', List()).size - 1 && <hr />}
      </div>
    );
  };
  AccordionContent.propTypes = {
    documentCategory: PropTypes.instanceOf(Map),
    categoryIndex: PropTypes.number,
  };

  const deleteCallBack = () => {
    setDeleteCategoryModal(false);
    setDeleteDocumentModal(false);

    settingId && getDocumentConfig(settingId, type);
  };
  const onDocumentDelete = () => {
    updateDocument({
      operation: 'delete',
      settingId,
      callBack: deleteCallBack,
      categoryId: categoryIdState,
      documentId: itemState.id,
      type,
    });
  };
  const handleCategoryOpen = (index) => {
    setCategoryOpen({ [index]: !categoryOpen[index] });
  };
  const DetailsComponent = () => {
    return (
      <div className="container ml-3">
        <div className="mb-2 row">
          <DocumentFormat />
        </div>

        <div className="mb-2 row">
          <DocumentSize />
        </div>
        <div className="mb-2 row">{!isIndependent && <DocumentMaxSize />}</div>

        <div className="mb-2 mt-1 bold text-right">
          <Trans i18nKey={'global_configuration.set_mandatory'}></Trans>
        </div>
        {documents.get('chooseDocuments', List()).map((doc, index) => (
          <div key={index} className="ml-0">
            {' '}
            <AccordionProgramInput
              onClick={() => {
                handleCategoryOpen(index);
              }}
              expanded={categoryOpen[index] || false}
              summaryChildren={<AccordionHeader document={doc} index={index} />}
              detailChildren={<AccordionContent documentCategory={doc} categoryIndex={index} />}
            />
          </div>
        ))}
        <MButton
          variant="outlined"
          className="digi-blue-button"
          clicked={(e) => handleCategoryModalOpen(e, 'add')}
        >
          + <Trans i18nKey={'global_configuration.add_new_category'}></Trans>
        </MButton>
        <hr />
        <MailConfig
          updateDocumentRemainder={updateDocumentRemainder}
          documents={documents}
          settingId={settingId}
          type={type}
        />
      </div>
    );
  };

  const handleClick = () => {
    settingId &&
      !accordionOpen &&
      !documents.size &&
      count !== 0 &&
      getDocumentConfig(settingId, type);
    setAccordionOpen();
  };
  const handleCategoryModalClose = () => {
    setAddCategoryOpenModal(false);
    getDocumentConfig(settingId, type);
  };

  const handleDocumentModalClose = () => {
    setAddEditDocumentModal(false);
    getDocumentConfig(settingId, type);
  };

  const onDelete = () => {
    updateDocumentCategory({
      operation: 'delete',
      settingId,
      callBack: deleteCallBack,
      id: categoryIdState,
      type,
    });
  };
  return (
    <>
      <AccordionProgramInput
        expanded={accordionOpen || false}
        onClick={handleClick}
        summaryChildren={setAccordionHeader(
          'documents',
          count + ' ' + t('document'),
          !accordionOpen
        )}
        detailChildren={<DetailsComponent />}
      />
      <hr />
      {addCategoryModalOpen && (
        <CategoryModal
          settingId={settingId}
          setData={setData}
          open={addCategoryModalOpen}
          setOpen={setAddCategoryOpenModal}
          close={handleCategoryModalClose}
          documents={documents}
          getDocumentConfig={getDocumentConfig}
          postDocumentCategory={postDocumentCategory}
          state={fromJS(itemState)}
          categoryId={categoryIdState}
          updateDocumentCategory={updateDocumentCategory}
          handleCallBack={handleCallBack}
          type={type}
        />
      )}
      {addEditDocumentModal && (
        <DocumentModal
          settingId={settingId}
          maxSize={documents.get('documentMaximumSize', 10)}
          open={addEditDocumentModal}
          setOpen={setAddEditDocumentModal}
          close={handleDocumentModalClose}
          documents={documents}
          getDocumentConfig={getDocumentConfig}
          postDocument={postDocument}
          state={fromJS(itemState)}
          categoryId={categoryIdState}
          updateDocument={updateDocument}
          isIndependent={isIndependent}
          selectedCategory={selectedCategory}
          setData={setData}
          handleCallBack={handleCallBack}
          type={type}
        />
      )}
      {deleteCategoryModal && (
        <Delete
          show={deleteCategoryModal}
          setShow={setDeleteCategoryModal}
          title={'Category'}
          description={t('delete_doc_category.desc', { category: 'document category' })}
          deleteName={itemState.documentCategory}
          deleteSelected={() => onDelete()}
        />
      )}
      {deleteDocumentModal && (
        <Delete
          show={deleteDocumentModal}
          setShow={setDeleteDocumentModal}
          title={'Document'}
          description={t('delete_doc_category.desc', { category: 'document' })}
          deleteName={itemState.labelName}
          deleteSelected={() => onDocumentDelete()}
        />
      )}
    </>
  );
};
const mapStateToProps = (state) => {
  return {
    documents: selectDocumentConfigurationDetails(state),
  };
};
DocumentIndex.propTypes = {
  setAccordionHeader: PropTypes.func,
  count: PropTypes.number,
  accordionOpen: PropTypes.bool,
  setAccordionOpen: PropTypes.func,
  settingId: PropTypes.string,
  type: PropTypes.string,
  getDocumentConfig: PropTypes.func,
  documents: PropTypes.instanceOf(Map),
  updateDocumentFormatCheck: PropTypes.func,
  postDocumentCategory: PropTypes.func,
  updateDocumentCategory: PropTypes.func,
  postDocument: PropTypes.func,
  updateDocument: PropTypes.func,
  postMultipleDocumentCheck: PropTypes.func,
  setData: PropTypes.func,
  updateDocumentRemainder: PropTypes.func,
};

export default connect(mapStateToProps, actions)(DocumentIndex);
