import React from 'react';
import { withRouter, useLocation } from 'react-router-dom';
import { connect } from 'react-redux';
import { Map } from 'immutable';
import PropTypes from 'prop-types';

import NavigationItem from 'Widgets/NavigationItem/NavigationItem';
import IndependentCourseNav from './IndependentCourseNav';
import { selectUserInfo } from '_reduxapi/Common/Selectors';
import { eString } from 'utils';
import UserManageNav from './UserManageNav';
import i18n from '../../../i18n';
import LocalStorageService from 'LocalStorageService';

function SideNavigationComponent(props) {
  const { toggleClick, loggedInUserData } = props;
  const location = useLocation();
  const component = location.pathname.split('/')[1];
  const id = location.pathname.split('/')[2];
  const name = location.pathname.split('/')[3];

  function isInstitute() {
    return component === 'i' && id !== undefined && name !== undefined;
  }

  SideNavigationComponent.propTypes = {
    toggleClick: PropTypes.func,
    loggedInUserData: PropTypes.instanceOf(Map),
  };
  const isUniversityAdmin = loggedInUserData.getIn(['university', 'status'], false);
  const universityId = loggedInUserData.getIn(['university', 'institutionId'], false);

  function getInstituteName() {
    const instituteData = LocalStorageService.getCustomToken('insData', true);
    if (instituteData !== null) {
      return instituteData.name !== undefined ? instituteData.name : 'N/A';
    }
    return 'N/A';
  }

  function checkActive(link) {
    const url = location.pathname;
    if (url.includes(link)) return 'sideNavActive';
    return '';
  }

  const instituteName = getInstituteName();

  return (
    <div>
      <React.Fragment>
        {isUniversityAdmin && !isInstitute() && (
          <>
            <NavigationItem
              to={`/allColleges`}
              menu={i18n.t('role_management.modules_list.Colleges')}
              //src={require('Assets/overview.svg')}
              clicked={toggleClick}
              active={checkActive('/allColleges')}
            />
          </>
        )}
        {!isInstitute() && (
          <>
            <NavigationItem
              // to="/global-configuration"
              to={`/university/${eString(universityId)}/${eString(
                instituteName
              )}/global-configuration/u-sity`}
              menu={i18n.t('side_nav.menus.global_configuration')}
              //src={require('Assets/overview.svg')}
              clicked={toggleClick}
              active={checkActive('/global-configuration')}
            />
            <NavigationItem
              to={`/university/${eString(universityId)}/${eString(instituteName)}/roles`}
              menu={i18n.t('side_nav.menus.roles_permissions')}
              //src={require('Assets/overview.svg')}
              clicked={toggleClick}
              active={checkActive('/roles')}
            />
            <NavigationItem
              to={`/university/${eString(universityId)}/${eString(instituteName)}/pgm-input`}
              menu={i18n.t('side_nav.menus.program_inputs')}
              //src={require('Assets/overview.svg')}
              clicked={toggleClick}
              active={checkActive('/pgm-input')}
            />
            <IndependentCourseNav
              toggleClick={toggleClick}
              active={checkActive('/independent-course')}
              to={`/university/${eString(universityId)}/${eString(
                instituteName
              )}/independent-course`}
            />
            <UserManageNav
              toggleClick={toggleClick}
              active={
                checkActive('/user-management/staff') || checkActive('/user-management/student')
              }
              staffActive={checkActive('/user-management/staff')}
              studentActive={checkActive('/user-management/student')}
              to={`/university/${eString(universityId)}/${eString(instituteName)}/user-management`}
            />
            <NavigationItem
              to={`/university/${eString(universityId)}/${eString(
                instituteName
              )}/independent/department`}
              menu={i18n.t('add_colleges.departments_subjects')}
              //src={require('Assets/overview.svg')}
              clicked={toggleClick}
              active={checkActive('/independent/department')}
            />
          </>
        )}
        {isInstitute() && (
          <>
            <NavigationItem
              to={`/i/${id}/${name}/dashboard`}
              menu={i18n.t('side_nav.menus.dashboard')}
              //src={require('Assets/overview.svg')}
              clicked={toggleClick}
              active={checkActive('/dashboard')}
            />
            <NavigationItem
              to={`/i/${id}/${name}/roles`}
              menu={i18n.t('side_nav.menus.roles_permissions')}
              //src={require('Assets/overview.svg')}
              clicked={toggleClick}
              active={checkActive('/roles')}
            />
            <NavigationItem
              to={`/i/${id}/${name}/global-configuration/u-sity`}
              menu={i18n.t('side_nav.menus.global_configuration')}
              //src={require('Assets/overview.svg')}
              clicked={toggleClick}
              active={checkActive('/global-configuration')}
            />
            <NavigationItem
              to={`/i/${id}/${name}/pgm-input`}
              menu={i18n.t('side_nav.menus.program_inputs')}
              //src={require('Assets/overview.svg')}
              clicked={toggleClick}
              active={checkActive('/pgm-input')}
            />
            <IndependentCourseNav
              toggleClick={toggleClick}
              active={checkActive('/independent-course')}
              to={`/i/${id}/${name}/independent-course`}
            />
            <UserManageNav
              toggleClick={toggleClick}
              active={
                checkActive('/user-management/staff') || checkActive('/user-management/student')
              }
              staffActive={checkActive('/user-management/staff')}
              studentActive={checkActive('/user-management/student')}
              to={`/i/${id}/${name}/user-management`}
            />
            <NavigationItem
              toggleClick={toggleClick}
              menu={i18n.t('add_colleges.departments_subjects')}
              active={checkActive('/independent/department')}
              to={`/i/${id}/${name}/independent/department`}
            />
          </>
        )}
      </React.Fragment>
    </div>
  );
}

const mapStateToProps = (state) => {
  return {
    loggedInUserData: selectUserInfo(state),
  };
};

const SideNavigationV2 = SideNavigationComponent;
export default connect(mapStateToProps)(withRouter(SideNavigationV2));
