import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import * as actions from '_reduxapi/assessment/action';
import { selectAttainmentList } from '_reduxapi/assessment/selector';
import MButton from 'Widgets/FormElements/material/Button';
import SettingsIcon from 'Assets/attainment_settings.svg';
import AttainmentSettingsModal from '../../modals/AttainmentSettingsModal';
import RegulationsList from './RegulationsList';
import { List, Map } from 'immutable';
import { getURLParams, eString } from 'utils';
import DeleteModal from 'Containers/Modal/Delete';
import { useHistory } from 'react-router-dom';

function AttainmentSettings({ getAttainmentList, attainmentList, addAttainment }) {
  const history = useHistory();
  const programId = getURLParams('pid', true);
  const programName = getURLParams('pname');
  const type = getURLParams('type', true);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [deleteAttainmentModal, setDeleteAttainmentModal] = useState(false);
  const [selectedAttainment, setSelectedAttainment] = useState(Map());

  useEffect(() => {
    getAttainmentList(programId, type);
  }, [programId]); //eslint-disable-line

  const handleSettingsModal = () => setShowSettingsModal(!showSettingsModal);

  const handleShowSettingsModal = (data, deleteMode = false) => {
    setSelectedAttainment(data);
    if (!deleteMode) handleSettingsModal();
    else setDeleteAttainmentModal(true);
  };

  const handleDeleteAttainment = () => {
    addAttainment({
      mode: 'delete',
      data: { _id: selectedAttainment.get('_id', ''), type: type },
      callBack: () => {
        getAttainmentList(programId, type);
        setDeleteAttainmentModal(false);
      },
    });
  };

  const handleView = (id) => {
    history.push(
      `/attainment-calculator/attainment-settings/regulations/${eString(id)}?pid=${eString(
        programId
      )}&pname=${programName}&type=${eString(type)}`
    );
  };

  return (
    <div className="mt-5 pb-5 container">
      {attainmentList.size ? (
        <RegulationsList
          showSettingsModal={handleShowSettingsModal}
          data={attainmentList}
          handleView={handleView}
        />
      ) : (
        <div className="course_master text-center mb-5 pt-4">
          <img src={SettingsIcon} alt="attainment settings" />
          <div>
            <h3 className="font-weight-normal pt-3">Attainment Settings</h3>
            <p className="bold">Plan your attainment calculation with the tree structure</p>
          </div>
          <div>
            <MButton
              variant="contained"
              color="primary"
              clicked={() => handleShowSettingsModal(Map())}
            >
              Create Attainment Tree
            </MButton>
          </div>
        </div>
      )}

      {showSettingsModal && (
        <AttainmentSettingsModal
          show={showSettingsModal}
          onClose={handleSettingsModal}
          attainmentList={attainmentList}
          data={selectedAttainment}
          handleView={handleView}
        />
      )}

      {deleteAttainmentModal && (
        <DeleteModal
          show={deleteAttainmentModal}
          setShow={() => setDeleteAttainmentModal(false)}
          title={'attainment_tree'}
          description={'delete_attainment_desc'}
          deleteName={selectedAttainment.get('regulationName', '')}
          deleteSelected={() => handleDeleteAttainment()}
        />
      )}
    </div>
  );
}

AttainmentSettings.propTypes = {
  attainmentList: PropTypes.instanceOf(List),
  getAttainmentList: PropTypes.func,
  addAttainment: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    attainmentList: selectAttainmentList(state),
  };
};

export default connect(mapStateToProps, actions)(AttainmentSettings);
