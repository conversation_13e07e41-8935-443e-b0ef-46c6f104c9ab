import React, { createContext, lazy, Suspense, useState, useRef, useEffect } from 'react';
import { List, Map as IMap, fromJS } from 'immutable';
import { useDispatch, useSelector } from 'react-redux';

import { getQADashboardGraphData } from '_reduxapi/q360/actions';
import { selectQapcDashboardGraphData } from '_reduxapi/q360/selectors';
import { DashBoardContextData } from '../DashboardContext';

// import bot_logo from 'Assets/q360_dashboard/bot_logo.svg';
import settings_with_outlined from 'Assets/q360_dashboard/settings_with_outlined.svg';

import Settings from './Settings';
import GraphIndex from '../Graph/GraphIndex';
import MonthAndTagFilter from '../Graph/MonthAndTagFilter';
import MaterialInput from 'Widgets/FormElements/material/Input';
import { selectSelectedRole } from '_reduxapi/Common/Selectors';
import { selectAcademicYear } from '_reduxapi/q360/selectors';
import { getAcademicYears } from '_reduxapi/q360/actions';
import LocalStorageService from 'LocalStorageService';
import { useCustomCalendarListHook } from '../../QualityAssuranceProcess/FormList/utils';
import Heba from 'Modules/Heba';

const SettingModal = lazy(() => import('./SettingModal'));

export const DashBoardContext = createContext();

export default function Dashboard() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [filterData, setFilterData] = useState(
    fromJS({
      month: {
        start: '',
        end: '',
      },
      tags: [],
    })
  );

  const [queryFormInitiatorIds, setQueryFormInitiatorIds] = useState('');
  const [queryCategoryFormGroupIds, setQueryCategoryFormGroupIds] = useState('');

  const dispatch = useDispatch();
  const academicYear = useSelector(selectAcademicYear);
  const { calendarDetails, onChangeCalendar, selectedCalendar } = useCustomCalendarListHook();
  const qapcDashboardGraphData = useSelector(selectQapcDashboardGraphData);
  const activeRole = useSelector(selectSelectedRole);
  const hasLevels = qapcDashboardGraphData.get('levels', List()).size > 0;

  const getLocalStorageTagsList = ({ tags }) => {
    const existingTags = LocalStorageService.getCustomToken('savedTagsList');
    if (existingTags === null && tags.size > 0) {
      LocalStorageService.setCustomToken('savedTagsList', JSON.stringify(tags.toJS()));
      return tags !== null ? tags : fromJS([]);
    } else {
      return existingTags !== null ? fromJS(JSON.parse(existingTags)) : fromJS([]);
    }
  };

  const tagsList = getLocalStorageTagsList({
    tags: qapcDashboardGraphData.get('tagsList', List()),
  });

  const startMonth = filterData.getIn(['month', 'start'], '');
  const endMonth = filterData.getIn(['month', 'end'], '');

  useEffect(() => {
    if (!academicYear.size) {
      dispatch(getAcademicYears());
    }
  }, []);

  useEffect(() => {
    return () => LocalStorageService.removeCustomToken('savedTagsList');
  }, []);

  useEffect(() => {
    const callBack = () => {
      setFilterData((prev) => prev.set('tags', List()));
      setQueryFormInitiatorIds('');
      setQueryCategoryFormGroupIds('');
    };
    if (selectedCalendar !== '')
      dispatch(getQADashboardGraphData({ institutionCalendarId: selectedCalendar, callBack }));
  }, [dispatch, selectedCalendar, activeRole]);

  useEffect(() => {
    if (startMonth !== '' && endMonth !== '') {
      const callBack = () => {};
      dispatch(
        getQADashboardGraphData({
          institutionCalendarId: selectedCalendar,
          callBack,
          startMonth,
          endMonth,
          queryFormInitiatorIds: queryFormInitiatorIds,
          queryCategoryFormGroupIds: queryCategoryFormGroupIds,
        })
      );
    }
  }, [startMonth, endMonth]);

  const tagsCallBack = ({ formInitiatorIds, categoryFormGroupIds }) => {
    dispatch(
      getQADashboardGraphData({
        institutionCalendarId: selectedCalendar,
        startMonth: startMonth !== '' ? startMonth : 1,
        endMonth: endMonth !== '' ? endMonth : 12,
        queryFormInitiatorIds: formInitiatorIds,
        queryCategoryFormGroupIds: categoryFormGroupIds,
      })
    );
    setQueryFormInitiatorIds(formInitiatorIds);
    setQueryCategoryFormGroupIds(categoryFormGroupIds);
  };

  const colorState = List([
    IMap({ name: 'To Do', color: '#FDE68A' }),
    IMap({ name: 'Missed', color: '#FECACA' }),
    IMap({ name: 'Incorporated', color: '#A5B4FC' }),
    IMap({ name: 'Not - Incorporated', color: '#C7D2FE' }),
    IMap({ name: 'Published', color: '#FEF3C7' }),
    IMap({ name: 'In Review', color: '#A5B4FC' }),
    IMap({ name: 'In Draft', color: '#3E8752' }),
    IMap({ name: 'Rejected', color: '#FDE68A' }),
    IMap({ name: 'Resubmit', color: '#DCFCE7' }),
  ]);

  const modalDataRef = useRef(
    new Map([
      ['selectedCourseLevelSetting', IMap()],
      ['colorPickerState', List()],
      ['selectedCategory', List()],
    ])
  );

  function handleOpenOrClose() {
    setDialogOpen((prev) => !prev);
  }

  const settingsModalCallBack = () => {
    setQueryFormInitiatorIds('');
    setQueryCategoryFormGroupIds('');
    setFilterData((prev) => prev.set('tags', List()));
  };

  return (
    <DashBoardContextData modalDataRef={modalDataRef} colorState={colorState}>
      <div className="p-3 bg-grey">
        <div className="d-flex">
          <div className="flex-grow-1 f-24 pt-1">Quality Assurance Dashboard</div>
          {/* <div className="cursor-pointer">
            <img src={bot_logo} alt="bot_logo" />
          </div> */}

          <div className="ml-auto">
            <div className="d-flex">
              <div>
                <MonthAndTagFilter
                  filterData={filterData}
                  setFilterData={setFilterData}
                  tagsList={tagsList}
                  tagsCallBack={tagsCallBack}
                />
              </div>
              <div className="mr-3">
                <MaterialInput
                  elementType={'materialSelect'}
                  type={'text'}
                  size={'small'}
                  elementConfig={{ options: calendarDetails.toJS() }}
                  labelclass={'mb-1 f-15'}
                  changed={(e) => onChangeCalendar(e.target.value)}
                  value={selectedCalendar}
                />
              </div>
              {!qapcDashboardGraphData.isEmpty() && (
                <div className="cursor-pointer mr-2">
                  <Heba accessUrl="?role=q360admin" activeTab="q360" />
                </div>
              )}
              <div className="cursor-pointer">
                <img
                  src={settings_with_outlined}
                  alt="settings_with_outlined"
                  style={{ width: '40px' }}
                  onClick={handleOpenOrClose}
                />
              </div>
            </div>
          </div>
        </div>

        {hasLevels ? (
          <GraphIndex
            graphData={qapcDashboardGraphData}
            queryFormInitiatorIds={queryFormInitiatorIds}
            queryCategoryFormGroupIds={queryCategoryFormGroupIds}
          />
        ) : (
          <Settings handleOpenOrClose={handleOpenOrClose} />
        )}
        {dialogOpen && (
          <Suspense fallback="">
            <SettingModal
              ref={modalDataRef}
              open={dialogOpen}
              handleClose={handleOpenOrClose}
              settingsModalCallBack={settingsModalCallBack}
            />
          </Suspense>
        )}
      </div>
    </DashBoardContextData>
  );
}
