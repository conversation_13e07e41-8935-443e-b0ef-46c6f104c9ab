import React, { useEffect, useMemo, useRef, useState } from 'react';
import Header from '../Header';
import Parameter from './Parameters/Parameter';
import Badges from './Badges/Badges';
import { List, Map } from 'immutable';
import { useDispatch } from 'react-redux';
import { getParameter, saveLeaderBoard } from '_reduxapi/global_configuration/v1/actions';
import { useSelector } from 'react-redux';
import { selectLeaderBoardSetting } from '_reduxapi/global_configuration/v1/selectors';
import { setData } from '_reduxapi/global_configuration/v1/actions';
import { onParameterValidate, onBadgeValidate } from '../utils';
import StudentRatingSystem from './StudentRatingSystem';
// import { onParameterValidate, onBadgeValidate, checkObjectIsEqual } from '../utils';

export default function LeaderBoard() {
  const dispatch = useDispatch();
  const [render, triggerRender] = useState(false); //eslint-disable-line
  const leaderBoardSetting = useSelector(selectLeaderBoardSetting);

  const parameterRef = useRef(List());
  const badgeRef = useRef(Map());

  const validate = () => {
    // if (
    //   checkObjectIsEqual(
    //     leaderBoardSetting,
    //     fromJS({
    //       parameters: parameterRef.current,
    //       ...badgeRef.current,
    //     })
    //   )
    // ) {
    //   return dispatch(setData(Map({ message: 'You should change something for update' })));
    // }
    let message = onParameterValidate(parameterRef.current);
    if (message !== '') {
      return dispatch(setData(Map({ message })));
    }
    message = onBadgeValidate(badgeRef.current?.get('badgeName', List()));
    if (message !== '') {
      return dispatch(setData(Map({ message })));
    }
    const badgeList = [];
    for (const badge of badgeRef.current?.get('badgeName', List())) {
      if (badge.get('startRange', '') !== '') {
        badgeList.push({
          name: badge.get('name', ''),
          startRange: Number(badge.get('startRange', '')),
          endRange: Number(badge.get('endRange', '')),
        });
      }
    }
    const payload = {
      leaderBoard: {
        parameters: parameterRef.current?.toJS(),
        badgeStyle: badgeRef.current?.get('badgeStyle', ''),
        badgeName: badgeList,
      },
    };
    dispatch(saveLeaderBoard(payload));
  };

  const makeDuplicateCriteriaAsDisabled = useMemo(() => {
    const criteriaContainer = [];
    return leaderBoardSetting.get('parameters', List()).map((parameter) => {
      return parameter.set(
        'criteria',
        parameter.get('criteria', List()).map((criteria) => {
          if (criteriaContainer.includes(criteria.get('criteriaName', ''))) {
            criteria = criteria.set('disabled', true);
          } else {
            criteria = criteria.set('disabled', false);
            criteriaContainer.push(criteria.get('criteriaName', ''));
          }
          return criteria;
        })
      );
    });
  }, [leaderBoardSetting]);

  function wantToSetApiResponse() {
    parameterRef.current = makeDuplicateCriteriaAsDisabled;
    triggerRender((prev) => !prev);
  }

  useEffect(() => {
    dispatch(getParameter());
  }, []); //eslint-disable-line

  useEffect(() => {
    if (leaderBoardSetting.isEmpty()) return;
    wantToSetApiResponse();
  }, [leaderBoardSetting]); //eslint-disable-line

  return (
    <div className="m-3">
      <Header
        boardName="Leader Board"
        validate={validate}
        wantToSetApiResponse={wantToSetApiResponse}
      />
      <Parameter ref={parameterRef} render={render} />
      <StudentRatingSystem />
      <Badges ref={badgeRef} leaderBoardSetting={leaderBoardSetting} render={render} />
    </div>
  );
}
