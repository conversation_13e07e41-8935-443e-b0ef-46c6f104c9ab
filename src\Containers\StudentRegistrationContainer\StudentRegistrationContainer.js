import React, { Component, Suspense, lazy } from 'react';
import { Badge } from 'react-bootstrap';
import { withRouter } from 'react-router-dom';
import { Trans } from 'react-i18next';
import PropTypes from 'prop-types';

import Loader from '../../Widgets/Loader/Loader';
import AllStudent from './AllStudent';
import ImportedStudent from './ImportedStudent';
import InvalidStudent from './InvalidStudent';
import ValidStudent from './ValidStudent';
import ExpiredStudent from './ExpiredStudent';
import CompletedStudent from './CompletedStudent';
import InactiveStudent from './InactiveStudent';
import axios from '../../axios';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import { getLang } from 'utils';
import UserRegistration from './UserRegistration';

const ReRegisterRequests = lazy(() =>
  import('../../Components/StaffManagement/ReRegisterRequests')
);

const tab = [
  {
    name: 'All',
  },
  {
    name: 'Imported',
  },
  {
    name: 'Invited',
  },
  {
    name: 'Submitted',
  },
  {
    name: 'Mismatch',
  },
  {
    name: 'Invalid',
  },
  {
    name: 'Valid',
  },
  {
    name: 'Expired',
  },
];
class StudentRegistration extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      selectedTab: 0,
      completeView: true,
      pendingView: false,
      inactiveView: false,
      registrationView: false,
      reRegisterView: false,
      name: '',
      faceReRegisterCount: 0,
    };
  }

  componentDidMount() {
    if (
      CheckPermission('tabs', 'User Management', 'Student Management', '', 'Registered', 'View') ||
      CheckPermission(
        'tabs',
        'User Management',
        'Student Management',
        '',
        'Registration Pending',
        'View'
      ) ||
      CheckPermission('tabs', 'User Management', 'Student Management', '', 'Inactive', 'View')
    ) {
      this.fetchApi();
    }
    if (this.props.location.state) {
      this.setState(this.props.location.state);
    } else {
      if (
        CheckPermission('tabs', 'User Management', 'Student Management', '', 'Registered', 'View')
      ) {
        this.completeTab();
      } else if (
        CheckPermission(
          'tabs',
          'User Management',
          'Student Management',
          '',
          'Registration Pending',
          'View'
        )
      ) {
        this.pendingTab();
      } else if (
        CheckPermission('tabs', 'User Management', 'Student Management', '', 'Inactive', 'View')
      ) {
        this.inactiveTab();
      }
      if (
        CheckPermission(
          'tabs',
          'User Management',
          'Student Management',
          '',
          'Registration Pending',
          'View'
        )
      ) {
        var tabValue = tab.map((data, index) => {
          if (
            CheckPermission(
              'subTabs',
              'User Management',
              'Student Management',
              '',
              'Registration Pending',
              '',
              data.name,
              'View'
            ) === true
          ) {
            return index;
          } else {
            return null;
          }
        });
        tabValue = tabValue.filter((tab) => tab !== null);
        this.setState({ selectedTab: tabValue.length > 0 ? tabValue[0] : 0 });
      }
    }
  }

  fetchApi = () => {
    this.setState({
      isLoading: true,
    });
    axios
      .get(`user/get_all/student/all?limit=10&pageNo=1`)
      .then((res) => {
        this.setState({
          inactive_user_count: res.data.count.inactive_user_count,
          completed_count: res.data.count.completed_count,
          all_active_user_count: res.data.count.all_active_user_count,
          faceReRegisterCount: res.data.count.faceReRegisterCount,
          isLoading: false,
        });
      })
      .catch((ex) => {
        this.setState({
          isLoading: false,
        });
      });
  };

  onSelect = (index, data) => {
    this.setState({
      selectedTab: index,
      name: data.name,
    });
  };

  completeTab = () => {
    this.setState({
      completeView: true,
      pendingView: false,
      inactiveView: false,
      reRegisterView: false,
      registrationView: false,
    });
  };
  pendingTab = () => {
    this.setState({
      completeView: false,
      pendingView: true,
      inactiveView: false,
      reRegisterView: false,
      registrationView: false,
    });
  };
  inactiveTab = () => {
    this.setState({
      completeView: false,
      pendingView: false,
      inactiveView: true,
      reRegisterView: false,
      registrationView: false,
    });
  };

  userRegistration = () => {
    this.setState({
      reRegisterView: false,
      completeView: false,
      pendingView: false,
      inactiveView: false,
      registrationView: true,
    });
  };

  reResisterTab = () => {
    this.setState({
      reRegisterView: true,
      completeView: false,
      pendingView: false,
      inactiveView: false,
      registrationView: false,
    });
  };

  handleReRequestCount = () => {
    const { faceReRegisterCount } = this.state;
    if (faceReRegisterCount === 0) return;
    this.setState((prevState) => ({
      faceReRegisterCount: prevState.faceReRegisterCount - 1,
    }));
  };

  render() {
    let name;

    if (this.state.name === '') {
      var tabValue = tab.map((data, index) => {
        if (
          CheckPermission(
            'subTabs',
            'User Management',
            'Student Management',
            '',
            'Registration Pending',
            '',
            data.name,
            'View'
          ) === true
        ) {
          return index;
        } else {
          return null;
        }
      });
      tabValue = tabValue.filter((tab) => tab !== null);
      name = tabValue.length > 0 ? tab[tabValue[0]].name.toLowerCase() : 'all';
    } else {
      name = this.state.name.toLowerCase();
    }

    const {
      selectedTab,
      reRegisterView,
      completeView,
      pendingView,
      inactiveView,
      registrationView,
      completed_count,
      all_active_user_count,
      inactive_user_count,
      isLoading,
      faceReRegisterCount,
    } = this.state;
    console.log({ reRegisterView: reRegisterView });
    return (
      <React.Fragment>
        <div className="headerbar headerbar_breadcrumb ham_nav nav" style={{ color: '#fff' }}>
          <Trans i18nKey={'student_management'}></Trans>
        </div>

        <React.Fragment>
          <div className="customize_tab">
            <ul id="menu">
              {CheckPermission(
                'tabs',
                'User Management',
                'Student Management',
                '',
                'Registered',
                'View'
              ) && (
                <a
                  onClick={this.completeTab}
                  href="##"
                  className={`tabaligment ${completeView ? 'tabactive' : ''}`}
                >
                  <Trans i18nKey={'registered'}></Trans>{' '}
                  <Badge variant="success">
                    {completed_count === undefined ? 0 : completed_count}
                  </Badge>
                </a>
              )}
              {CheckPermission(
                'tabs',
                'User Management',
                'Student Management',
                '',
                'Registration Pending',
                'View'
              ) && (
                <a
                  onClick={this.pendingTab}
                  href="##"
                  className={`tabaligment ${pendingView ? 'tabactive' : ''}`}
                >
                  <Trans i18nKey={'registration_pending'}></Trans>{' '}
                  {CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    'All',
                    'View'
                  ) && (
                    <Badge variant="success">
                      {all_active_user_count === undefined ? 0 : all_active_user_count}{' '}
                    </Badge>
                  )}
                </a>
              )}
              {CheckPermission(
                'tabs',
                'User Management',
                'Student Management',
                '',
                'Inactive',
                'View'
              ) && (
                <a
                  onClick={this.inactiveTab}
                  href="##"
                  className={`tabaligment ${inactiveView ? 'tabactive' : ''}`}
                >
                  <Trans i18nKey={'inactive'}></Trans>{' '}
                  <Badge variant="success">
                    {' '}
                    {inactive_user_count === undefined ? 0 : inactive_user_count}{' '}
                  </Badge>{' '}
                </a>
              )}
              {/* <a
                href="##"
                onClick={this.reResisterTab}
                className={`tabaligment ${reRegisterView ? 'tabactive' : ''}`}
              >
                Re-Register Requests{' '}
              </a> */}
              <a
                onClick={this.userRegistration}
                href="##"
                className={`tabaligment ${registrationView ? 'tabactive' : ''}`}
              >
                Self Registration
              </a>
              <a
                href="##"
                onClick={this.reResisterTab}
                className={`tabaligment ${reRegisterView ? 'tabactive' : ''}`}
              >
                Re-Register Requests{' '}
                <Badge variant="success">
                  {' '}
                  {faceReRegisterCount === undefined ? 0 : faceReRegisterCount}{' '}
                </Badge>{' '}
              </a>
            </ul>
          </div>

          {/* tab view start  */}
          <React.Fragment>
            {completeView === true &&
              CheckPermission(
                'tabs',
                'User Management',
                'Student Management',
                '',
                'Registered',
                'View'
              ) && <CompletedStudent name="completed" />}

            {pendingView === true && (
              <React.Fragment>
                <div className={`tabs_main ${getLang() === 'ar' ? 'text-left' : ''}`}>
                  {tab.map((data, index) => {
                    if (
                      CheckPermission(
                        'subTabs',
                        'User Management',
                        'Student Management',
                        '',
                        'Registration Pending',
                        '',
                        data.name,
                        'View'
                      ) === true
                    ) {
                      return (
                        <span
                          key={index}
                          className={
                            'tab_custom ' + (selectedTab === index ? 'tab_custom_active' : '')
                          }
                          onClick={() => this.onSelect(index, data)}
                        >
                          <Trans i18nKey={`user_management.tabs.${data.name}`}></Trans>
                        </span>
                      );
                    } else {
                      return <React.Fragment key={index}></React.Fragment>;
                    }
                  })}
                </div>
                {selectedTab === 0 &&
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    'All',
                    'View'
                  ) && <AllStudent fetchApi={this.fetchApi} name={name} />}
                {this.state.selectedTab === 1 &&
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    'Imported',
                    'View'
                  ) && (
                    <ImportedStudent
                      name={name}
                      fetchApi={this.fetchApi}
                      selectedTab={selectedTab}
                    />
                  )}
                {selectedTab === 2 &&
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    'Invited',
                    'View'
                  ) && (
                    <AllStudent name={name} fetchApi={this.fetchApi} selectedTab={selectedTab} />
                  )}
                {selectedTab === 3 &&
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    'Submitted',
                    'View'
                  ) && <AllStudent name={name} selectedTab={selectedTab} />}
                {selectedTab === 4 &&
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    'Mismatch',
                    'View'
                  ) && <AllStudent name={name} selectedTab={selectedTab} />}
                {selectedTab === 5 &&
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    'Invalid',
                    'View'
                  ) && <InvalidStudent name={name} selectedTab={selectedTab} />}
                {selectedTab === 6 &&
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    'Valid',
                    'View'
                  ) && <ValidStudent name={name} selectedTab={selectedTab} />}
                {selectedTab === 7 &&
                  CheckPermission(
                    'subTabs',
                    'User Management',
                    'Student Management',
                    '',
                    'Registration Pending',
                    '',
                    'Expired',
                    'View'
                  ) && (
                    <ExpiredStudent
                      name={name}
                      fetchApi={this.fetchApi}
                      selectedTab={selectedTab}
                    />
                  )}
              </React.Fragment>
            )}

            {inactiveView === true &&
              CheckPermission(
                'tabs',
                'User Management',
                'Student Management',
                '',
                'Inactive',
                'View'
              ) && <InactiveStudent name="inactive" fetchApi={this.fetchApi} />}

            {registrationView === true && <UserRegistration name="userRegistration" />}

            {reRegisterView ? (
              <Suspense fallback="">
                <ReRegisterRequests userType="student" countCallBack={this.handleReRequestCount} />
              </Suspense>
            ) : (
              ''
            )}
          </React.Fragment>
        </React.Fragment>

        <Loader isLoading={isLoading} />
      </React.Fragment>
    );
  }
}

StudentRegistration.propTypes = {
  location: PropTypes.object,
};

export default withRouter(StudentRegistration);
