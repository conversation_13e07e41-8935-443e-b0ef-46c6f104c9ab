import React from 'react';
import { Trans } from 'react-i18next';
import i18n from 'i18next';

import { Map } from 'immutable';
import PropTypes from 'prop-types';
import {
  isAlphaNumericWithSpace,
  CheckEmpty,
  checkMinLengthLessThanThree,
  checkOnlyNumber,
  checkMinLengthLessThanFour,
} from 'v2/utils';

export const BasicDetails = (title, value) => {
  return (
    <>
      <div className="d-flex float-left wd_120">
        <Trans i18nKey={title}></Trans>
      </div>
      <div className="float-left">
        <b>{value}</b>
      </div>
      <div className="clearfix"></div>
    </>
  );
};

export const checkPage = (currentPage, firstPage, secondPage) => {
  return currentPage === 1 ? firstPage : currentPage === 2 ? secondPage : '';
};

export const checkValidation = (data, type, setData) => {
  const getName = `${type === 'group' || type === 'University' ? 'University' : 'College'}`;
  if (CheckEmpty(data.name)) {
    setData(Map({ message: i18n.t(`add_colleges.${getName}Name_Required`) }));
    return false;
  }

  if (!isAlphaNumericWithSpace(data.name)) {
    setData(Map({ message: i18n.t(`add_colleges.${getName}Name_AlphaNum`) }));
    return false;
  }
  if (checkMinLengthLessThanThree(data.name)) {
    setData(Map({ message: i18n.t(`add_colleges.${getName}Name_MinLength`) }));
    return false;
  }

  if (data.accreditation?.length > 0) {
    let check = true;
    return data.accreditation
      .map((item) => {
        if (CheckEmpty(item.accreditationAgencyName)) {
          setData(Map({ message: i18n.t(`add_colleges.accreditationAgencyName_required`) }));
          check = false;
        }
        if (CheckEmpty(item.accreditationType)) {
          setData(Map({ message: i18n.t(`add_colleges.accreditationType_required`) }));
          check = false;
        }
        if (CheckEmpty(item.accreditationNumber)) {
          setData(Map({ message: i18n.t(`add_colleges.accreditationNumber_required`) }));
          check = false;
        }
        if (CheckEmpty(item.accreditationValue)) {
          setData(Map({ message: i18n.t(`add_colleges.accreditationValue_required`) }));
          check = false;
        }
        if (CheckEmpty(item.validityStart)) {
          setData(Map({ message: i18n.t(`add_colleges.validityStart_required`) }));
          check = false;
        }
        if (CheckEmpty(item.validityEnd)) {
          setData(Map({ message: i18n.t(`add_colleges.validityEnd_required`) }));
          check = false;
        }
        if (CheckEmpty(item.others)) {
          setData(Map({ message: i18n.t(`add_colleges.others_required`) }));
          check = false;
        }
        return check ? true : false;
      })
      .every((element) => element);
  }

  if (CheckEmpty(data.code)) {
    setData(Map({ message: i18n.t(`add_colleges.${getName}Code_Required`) }));
    return false;
  }
  if (checkMinLengthLessThanThree(data.code)) {
    setData(Map({ message: i18n.t(`add_colleges.${getName}Code_MinLength`) }));
    return false;
  }

  if (CheckEmpty(data.noOfColleges) && (type === 'group' || data?.isUniversity)) {
    setData(Map({ message: i18n.t(`add_colleges.NoOfCollege_Required`) }));
    return false;
  }
  if (!checkOnlyNumber(data.noOfColleges) && (type === 'group' || data?.isUniversity)) {
    setData(Map({ message: i18n.t(`add_colleges.NoOfCollege_Number`) }));
    return false;
  }
  if (CheckEmpty(data.address)) {
    setData(Map({ message: i18n.t(`add_colleges.Address_Required`) }));
    return false;
  }
  if (CheckEmpty(data.country)) {
    setData(Map({ message: i18n.t(`add_colleges.Country_Required`) }));
    return false;
  }
  if (CheckEmpty(data.district)) {
    setData(Map({ message: i18n.t(`add_colleges.District_Required`) }));
    return false;
  }
  if (CheckEmpty(data.state)) {
    setData(Map({ message: i18n.t(`add_colleges.State_Required`) }));
    return false;
  }
  if (CheckEmpty(data.city)) {
    setData(Map({ message: i18n.t(`add_colleges.City_Required`) }));
    return false;
  }
  if (CheckEmpty(data.zipCode)) {
    setData(Map({ message: i18n.t(`add_colleges.ZipCode_Required`) }));
    return false;
  }
  if (checkMinLengthLessThanFour(data.zipCode)) {
    setData(Map({ message: i18n.t('add_colleges.ZipCode_MinLength') }));
    return false;
  }
  if (CheckEmpty(data.logo)) {
    setData(Map({ message: i18n.t(`add_colleges.Logo_Required`) }));
    return false;
  }
  return true;
};

export const Video = ({ url }) => (
  <video width="100%" key={url} controls>
    <source src={url} type="video/mp4" />
    {/* not working to check */}
    <source src={url} type="video/x-flv" />
    <source src={url} type="application/x-mpegURL" />
    <source src={url} type="video/MP2T" />
    <source src={url} type="video/x-msvideo" />
    <source src={url} type="video/x-ms-wmv" />
    {/* not loading */}
    <source src={url} type="video/3gp2" />
  </video>
);

Video.propTypes = {
  url: PropTypes.string,
};

export const Audio = ({ url }) => (
  <audio width="100%" key={url} controls>
    <source src={url} type="audio/mpeg" />
  </audio>
);

Audio.propTypes = {
  url: PropTypes.string,
};

export function getFullAddress(data) {
  const address = data.get('address', '');
  const district = data.get('district', '');
  const city = data.getIn(['city', 'name'], '');
  const state = data.getIn(['state', 'name'], '');
  const country = data.getIn(['country', 'name'], '');
  const zipCode = data.get('zipCode', '');

  return `${address}, ${district}, ${city}, ${state} ${zipCode}, ${country}`;
}

export const EDITOR_TOOLBAR = {
  options: [
    'inline',
    'blockType',
    'fontSize',
    'fontFamily',
    'list',
    'textAlign',
    'colorPicker',
    'link',
    'embedded',
    // 'emoji',
    'image',
    'remove',
    'history',
  ],

  inline: {
    inDropdown: false,
    options: ['bold', 'italic', 'underline', 'strikethrough'],
  },
  list: { inDropdown: true },
  textAlign: { inDropdown: true },
  link: { inDropdown: true },
  history: { inDropdown: true },
};

export const checkValidationEditor = (data, setData) => {
  if (data.getPlainText().length > 5000) {
    setData(Map({ message: `Description length must be less than or equal to 5000 character` }));
    return false;
  }
  return true;
};
