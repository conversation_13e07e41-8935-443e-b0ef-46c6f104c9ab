import React from 'react';
import { Route, Switch } from 'react-router';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import SnackBars from 'Modules/Utils/Snackbars';
import './GlobalSearchStyle.css';

// redux
import * as actions from '_reduxapi/UserGlobalSearch/action';
import { selectIsLoading, selectMessage } from '_reduxapi/UserGlobalSearch/selectors';
import {
  selectIsLoading as selectIsLoadingLms,
  selectMessage as selectMessageLms,
} from '_reduxapi/leave_management/selectors';
import * as lmsActions from '_reduxapi/leave_management/actions';

// utils
import Loader from 'Widgets/Loader/Loader';
import Breadcrumb from 'Widgets/Breadcrumb/Breadcrumb';
import UserGlobalSearch from './components/UserGlobalSearch';
import StudentOrStaffList from './components/StudentOrStaffList';

const UserGlobalSearchIndex = ({ message, isLoading, selectMessageLms, selectIsLoadingLms }) => {
  return (
    <div>
      {(message || selectMessageLms) && (
        <SnackBars show={true} message={message || selectMessageLms} />
      )}
      <Loader isLoading={isLoading || selectIsLoadingLms} />
      <Breadcrumb>{'User Global Search'}</Breadcrumb>
      <Switch>
        <Route path="/UserGlobalSearch" exact component={UserGlobalSearch}></Route>
        <Route
          path="/UserGlobalSearch/StudentOrStaffList/:userType/:institutionCalendarId/:userId/:userCode/:userName/:gender/:email/:academicNo/:userFirstName/:userLastName"
          component={StudentOrStaffList}
        ></Route>
      </Switch>
    </div>
  );
};

UserGlobalSearchIndex.propTypes = {
  message: PropTypes.string,
  isLoading: PropTypes.bool,
  selectIsLoadingLms: PropTypes.bool,
  selectMessageLms: PropTypes.string,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
    selectIsLoadingLms: selectIsLoadingLms(state),
    selectMessageLms: selectMessageLms(state),
  };
};
export default connect(mapStateToProps, { ...actions, ...lmsActions })(
  withRouter(UserGlobalSearchIndex)
);
