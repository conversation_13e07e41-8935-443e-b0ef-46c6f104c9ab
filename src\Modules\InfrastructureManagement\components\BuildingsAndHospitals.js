import React, { Component } from 'react';
import { List, Map } from 'immutable';
import PropTypes from 'prop-types';
import { Table } from 'react-bootstrap';
import AlertModal from '../modal/AlertModal';
import AddEditBuildingModal from '../modal/AddEditBuildingModal';
import Tooltip from '../../../_components/UI/Tooltip/Tooltip';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { getLang } from '../../../utils';

const lang = getLang();

class BuildingsAndHospitals extends Component {
  constructor() {
    super();
    this.state = {
      showModal: false,
      selectedBuilding: Map(),
      operation: '',
      modalData: {
        show: false,
      },
    };
  }

  componentDidMount() {
    this.props.getBuildingsAndHospitals();
  }

  addNewBuilding() {
    this.props.getLocations();
    this.setState({
      showModal: true,
      selectedBuilding: Map(),
      operation: 'create',
    });
  }

  onModalClose() {
    this.setState({
      showModal: false,
      selectedBuilding: Map(),
      operation: '',
      modalData: { show: false },
    });
  }

  onSave(requestBody) {
    this.setState(
      {
        showModal: false,
        selectedBuilding: Map(),
        operation: '',
      },
      () => this.props.updateBuildingOrHospital(requestBody)
    );
  }

  onEditBuildingClick(row) {
    this.props.getLocations();
    this.setState({
      showModal: true,
      selectedBuilding: row,
      operation: 'update',
    });
  }

  onDeleteBuildingClick(row, isConfirmed = false) {
    if (row.get('isAssigned')) {
      this.setModalData({
        show: true,
        title: t('infra_management.alerts.building_assigned.title'),
        description: t('infra_management.alerts.building_assigned.description'),
        variant: 'alert',
        cancelButtonLabel: t('ok'),
      });
      return;
    }
    if (!isConfirmed) {
      this.setModalData({
        show: true,
        title: t('infra_management.modals.confirm_delete'),
        description: t('infra_management.modals.building.description'),
        variant: 'confirm',
        data: { data: row },
        cancelButtonLabel: t('no'),
        confirmButtonLabel: t('yes'),
      });
      return;
    }
    this.props.updateBuildingOrHospital({
      operation: 'delete',
      _id: row.get('_id'),
    });
  }

  onConfirm({ data }) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => this.onDeleteBuildingClick(data, true)
    );
  }

  setModalData({ show, title, description, variant, confirmButtonLabel, cancelButtonLabel, data }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(description && { description }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  }

  render() {
    const { buildingsAndHospitals, locations } = this.props;
    const { showModal, selectedBuilding, operation, modalData } = this.state;
    return (
      <div className="row pt-5">
        <div className="col-md-12">
          <span className={lang === 'en' ? 'font-weight-bold' : 'font-weight-bold float-left'}>
            <Trans
              i18nKey={
                'infra_management.infra_settings.buildings_hospitals.buildings_and_hospitals'
              }
            ></Trans>
          </span>
          {CheckPermission(
            'subTabs',
            'Infrastructure Management',
            'Onsite',
            '',
            'Settings',
            '',
            'Buildings and Hospitals',
            'Add'
          ) && (
            <p
              className="mb-0 float-right remove_hover font-weight-bold"
              onClick={this.addNewBuilding.bind(this)}
            >
              <i className="fa fa-plus-circle pr-2" aria-hidden="true"></i>
              <Trans i18nKey={'add'}></Trans>
            </p>
          )}
        </div>
        <div className="col-md-12">
          <div className="infra_table_shadows">
            <div className="height-300 m-3">
              <Table className="border-radious-8 height-unset">
                <thead className="border">
                  <tr>
                    <th className="border_color_blue">
                      <Trans i18nKey={'infra_management.s_no'}></Trans>
                    </th>
                    <th className="border_color_blue">
                      <Trans
                        i18nKey={'infra_management.infra_settings.buildings_hospitals.location'}
                      ></Trans>
                    </th>
                    <th className="border_color_blue">
                      <Trans
                        i18nKey={
                          'infra_management.infra_settings.buildings_hospitals.building/hospital'
                        }
                      ></Trans>
                    </th>
                    <th className="border_color_blue">
                      <Trans
                        i18nKey={
                          'infra_management.infra_settings.buildings_hospitals.number_of_floors'
                        }
                      ></Trans>
                    </th>
                    <th className="border_color_blue">
                      <Trans
                        i18nKey={
                          'infra_management.infra_settings.buildings_hospitals.number_of_zones'
                        }
                      ></Trans>
                    </th>
                    <th className="border_color_blue"></th>
                  </tr>
                </thead>
                <tbody>
                  {buildingsAndHospitals.isEmpty() && (
                    <tr>
                      <td colSpan="6">
                        <Trans i18nKey={'infra_management.no_records'}></Trans>
                      </td>
                    </tr>
                  )}
                  {buildingsAndHospitals.map((row, i) => (
                    <tr key={row.get('_id')} className="tr-bottom-border tr-change">
                      <td>{i + 1}</td>
                      <td>{row.get('location', '')}</td>
                      <td>{row.get('name', '')}</td>
                      <td>{row.get('no_of_floors', '') || 'NA'}</td>
                      <td>{row.get('no_of_zones', '') || 'NA'}</td>
                      <td>
                        {CheckPermission(
                          'subTabs',
                          'Infrastructure Management',
                          'Onsite',
                          '',
                          'Settings',
                          '',
                          'Buildings and Hospitals',
                          'Edit'
                        ) && (
                          <b className="float-left pr-2 f-16">
                            <Tooltip title={t('edit')}>
                              <i
                                className="fa fa-pencil"
                                aria-hidden="true"
                                onClick={this.onEditBuildingClick.bind(this, row)}
                              ></i>
                            </Tooltip>
                          </b>
                        )}
                        {CheckPermission(
                          'subTabs',
                          'Infrastructure Management',
                          'Onsite',
                          '',
                          'Settings',
                          '',
                          'Buildings and Hospitals',
                          'Delete'
                        ) && (
                          <b className="float-left pl-2 f-16">
                            <Tooltip title={t('delete')}>
                              <i
                                className="fa fa-trash"
                                aria-hidden="true"
                                onClick={this.onDeleteBuildingClick.bind(this, row, false)}
                              ></i>
                            </Tooltip>
                          </b>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          </div>
        </div>
        {showModal && (
          <AddEditBuildingModal
            show
            locations={locations}
            selectedBuilding={selectedBuilding}
            onClose={this.onModalClose.bind(this)}
            onSave={this.onSave.bind(this)}
            operation={operation}
          />
        )}
        <AlertModal
          show={modalData.show}
          title={modalData.title || ''}
          description={modalData.description || ''}
          variant={modalData.variant || 'confirm'}
          confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
          cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
          onClose={this.onModalClose.bind(this)}
          onConfirm={this.onConfirm.bind(this)}
          data={modalData.data}
        />
      </div>
    );
  }
}

BuildingsAndHospitals.propTypes = {
  getBuildingsAndHospitals: PropTypes.func,
  buildingsAndHospitals: PropTypes.instanceOf(List),
  getLocations: PropTypes.func,
  updateBuildingOrHospital: PropTypes.func,
  locations: PropTypes.instanceOf(List),
};

export default BuildingsAndHospitals;
