import React from 'react';
import PropTypes from 'prop-types';
import { Dialog, DialogActions, DialogContent, DialogTitle, Typography } from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
import ErrorIcon from '@mui/icons-material/Error';
import { buttonSx } from '../utils';
import { PreviewDetails } from '../ScheduleDrawer';
import { List, Map } from 'immutable';

const dialogSx = {
  '& .MuiDialog-paper': {
    maxWidth: '800px',
    color: '#374151',
  },
};
const titleSx = {
  padding: '12px 16px',
};
const actionButtonSx = {
  ...buttonSx,
  minWidth: 140,
};
const warningIconSx = {
  mb: '2px',
  ml: '4px',
  color: '#D97706',
};
const previewDetailsSx = {
  mt: '12px',
  padding: 0,
  border: 'none',
};

const ScheduleConfirmationModal = ({ open, scheduleData, handleClose, handleConfirm }) => {
  return (
    <Dialog open={open} sx={dialogSx} fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        Confirmation Preview
      </DialogTitle>
      <DialogContent className="p-3">
        <div className="d-flex align-items-center mt-1">
          <Typography color="#6B7280">
            Selected topics:{' '}
            <Typography component="span" fontWeight={500} color="#374151">
              {scheduleData.get('selectedSchedules', List()).size}
            </Typography>
            /{scheduleData.get('totalNoOfSchedules', 0)}
          </Typography>
          <ErrorIcon fontSize="small" sx={warningIconSx} />
        </div>

        <PreviewDetails
          sx={previewDetailsSx}
          scheduleData={scheduleData.get('previewDetails', Map())}
        />

        <Typography mt={1.5} mb={0.5}>
          Are you sure want to schedule?
        </Typography>
      </DialogContent>
      <DialogActions className="p-3 border-top">
        <MButton variant="outlined" color="gray" clicked={handleClose} sx={actionButtonSx}>
          Back
        </MButton>
        <MButton variant="contained" color="primary" clicked={handleConfirm} sx={actionButtonSx}>
          Yes, Schedule
        </MButton>
      </DialogActions>
    </Dialog>
  );
};
ScheduleConfirmationModal.propTypes = {
  open: PropTypes.bool,
  scheduleData: PropTypes.instanceOf(Map),
  handleClose: PropTypes.func,
  handleConfirm: PropTypes.func,
};

export default ScheduleConfirmationModal;
