import React, { useEffect } from 'react';
import { useHistory, withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { List, Map } from 'immutable';
import * as actions from '../../../../_reduxapi/assessment/action';
import { Years } from '../../../../constants';
import { eString, formatTwoString, getURLParams, ucFirst } from '../../../../utils';
import { Header } from './Header';
import { selectAllAssessmentCourse } from '../../../../_reduxapi/assessment/selector';
import CustomReactProgressBar from 'Shared/CustomReactProgressBar';

function CourseList({
  getAssessmentAllCourse,
  programId,
  activeInstitutionCalendar,
  allAssessmentCourse,
}) {
  const history = useHistory();
  const year = getURLParams('year', true);
  const level = getURLParams('level', true);
  const term = getURLParams('term', true);
  const programName = getURLParams('pname', true);
  const institutionCalendar = activeInstitutionCalendar.get('_id', '');
  const formattedYear = year !== '' ? Years[year.replace('year', '')].name : '';

  useEffect(() => {
    if (programId !== '' && institutionCalendar !== '')
      getAssessmentAllCourse(programId, institutionCalendar, term, level);
  }, [getAssessmentAllCourse, programId, institutionCalendar, term, level]);

  function goToConfigure(course) {
    const { location } = history;
    const rotationCount = course.get('rotationCount', '');
    history.push(
      `/assessment-management/assessment_details/configure${location.search}&cId=${eString(
        course.get('_course_id', '')
      )}&cName=${eString(course.get('courses_name', ''))}&level=${eString(
        course.get('level', '')
      )}${
        rotationCount !== '' && rotationCount !== 0
          ? `&rotationCount=${eString(rotationCount)}`
          : ``
      }`
    );
  }
  return (
    <div className="main pb-5 bg-mainBackground">
      <Header
        history={history}
        programName={`${programName} Program / ${formattedYear}, ${level}`}
        isConfigurePages={true}
        type="cs"
      />
      <div className="container pl-0">
        <div className="p-3">
          <p className="f-20 mb-4 bold"> All Courses</p>

          <div className="border-bottom mb-4">
            <div className="row align-items-center">
              <div className="col-md-3">
                <p className="mb-0"> Course Name </p>
              </div>
              <div className="col-md-3">
                <p className="mb-0"> Direct Assessment </p>
              </div>
              <div className="col-md-3">
                <p className="mb-0"> Indirect Assessment </p>
              </div>
              <div className="col-md-3"></div>
            </div>
          </div>

          {allAssessmentCourse.get('courseList', List()).map((course, index) => {
            const rotationCount = course.get('rotationCount', '');
            return (
              <div className="levelBox mb-2 mt-3" key={index}>
                <div className="row align-items-center">
                  <div className="col-md-3 mb-0">
                    <p className="ml-2 mb-0">
                      {' '}
                      {course.get('courses_name', '')}{' '}
                      {rotationCount !== '' && rotationCount !== 0 ? ` - R${rotationCount}` : ``}{' '}
                    </p>
                  </div>

                  {course.get('assessmentMode', List()).map((assessment, aIndex) => {
                    return (
                      <React.Fragment key={aIndex}>
                        <div className="col-md-3 mb-0">
                          <p className="mb-2">
                            {' '}
                            {ucFirst(assessment.get('typeName', ''))} -{' '}
                            {formatTwoString(assessment.get('assessmentAssignedCount', '00'))}/
                            {formatTwoString(assessment.get('assessmentCount', '00'))} Assessment{' '}
                          </p>
                          <div className="">
                            <CustomReactProgressBar
                              variant="primary"
                              total={assessment.get('assessmentCount', 0)}
                              now={assessment.get('assessmentAssignedCount', 0)}
                              className="border-radious-8 height-13"
                            />
                          </div>
                        </div>
                      </React.Fragment>
                    );
                  })}
                  <div className="col-md-2 mb-0 remove_hover" onClick={() => goToConfigure(course)}>
                    <p className="ml-2 mb-0 mr-3 bold text-right"> View </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

CourseList.propTypes = {
  history: PropTypes.object,
  getAssessmentAllCourse: PropTypes.func,
  allAssessmentCourse: PropTypes.oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
  programId: PropTypes.string,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
};

const mapStateToProps = function (state) {
  return {
    allAssessmentCourse: selectAllAssessmentCourse(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(CourseList);
