import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { Checkbox } from '@mui/material';
import React from 'react';

const StaffAttendance = ({ state, setState, disabled, criteria }) => {
  return (
    <RadioGroup
      aria-labelledby="demo-radio-buttons-group-label"
      name="radio-buttons-group"
      value={state.get('noOfSession', false)}
      onChange={(e) => {
        setState((prev) =>
          prev
            .set('noOfSession', e.target.value === 'true')
            .setIn(
              ['sessionStarted', 'noOfStudent'],
              criteria.getIn(['sessionStarted', 'noOfStudent'], '')
            )
            .setIn(
              ['sessionStarted', 'minsOfStart'],
              criteria.getIn(['sessionStarted', 'minsOfStart'], '')
            )
        );
      }}
    >
      <FormControlLabel
        value={true}
        disabled={disabled}
        control={<Radio />}
        label={
          <span className={`f-12 ${!state.get('noOfSession', false) && 'text-grey'}`}>
            {' '}
            No. of Sessions Started On-Time / No. of Sessions Completed * 100
          </span>
        }
      />
      <FormControlLabel
        disabled={disabled}
        value={false}
        control={<Radio />}
        label={
          <div className={`f-12 ${state.get('noOfSession', false) && 'text-grey'}`}>
            No. of Sessions Started On-Time with minimum of
            <input
              disabled={state.get('noOfSession', false) || disabled}
              onChange={(e) => {
                const value = e.target.value;
                if (value !== '' && (Number(value) < 0 || isNaN(Number(value)))) return;
                setState((prev) => prev.setIn(['sessionStarted', 'noOfStudent'], value));
              }}
              type="text"
              className="criteria_input"
              value={state.getIn(['sessionStarted', 'noOfStudent'], '')}
            />
            student attendance marked within
            <input
              disabled={state.get('noOfSession', false) || disabled}
              type="text"
              value={state.getIn(['sessionStarted', 'minsOfStart'], '')}
              className="criteria_input"
              onChange={(e) => {
                const value = e.target.value;
                if (value !== '' && (Number(value) < 0 || isNaN(Number(value)))) return;
                setState((prev) => prev.setIn(['sessionStarted', 'minsOfStart'], value));
              }}
            />{' '}
            mins of starting the session / No. of Sessions Completed * 100
          </div>
        }
      />
    </RadioGroup>
  );
};
StaffAttendance.propTypes = {
  state: PropTypes.instanceOf(Map),
  criteria: PropTypes.instanceOf(Map),
  setState: PropTypes.func,
  disabled: PropTypes.bool,
};

const Engager = ({ state, setState, disabled, criteria }) => {
  return (
    <RadioGroup
      aria-labelledby="demo-radio-buttons-group-label"
      value={state.get('noOfEngager', false)}
      onChange={(e) => {
        setState((prev) =>
          prev
            .set('noOfEngager', e.target.value === 'true')
            .set('studentParticipated', criteria.get('studentParticipated', ''))
        );
      }}
      name="radio-buttons-group"
    >
      <FormControlLabel
        value={true}
        disabled={disabled}
        control={<Radio />}
        label={
          <span className="f-12">
            {' '}
            No. of Total Engager Taken by the staff for the course / No. of assigned sessions
            completed by the staff for the course * 100
          </span>
        }
      />
      <FormControlLabel
        disabled={disabled}
        value={false}
        control={<Radio />}
        label={
          <div className="text-grey f-12">
            No. of Total Engager Taken by the staff for the course with minimum{' '}
            <input
              disabled={disabled || state.get('noOfEngager', false)}
              onChange={(e) => {
                if (Number(e.target.value) < 0 || isNaN(Number(e.target.value))) return;
                setState((prev) => prev.set('studentParticipated', e.target.value));
              }}
              value={state.get('studentParticipated', '')}
              type="text"
              className="criteria_input"
            />
            student participated / No. of assigned sessions completed by the staff for the course *
            100
          </div>
        }
      />
    </RadioGroup>
  );
};
Engager.propTypes = {
  state: PropTypes.instanceOf(Map),
  criteria: PropTypes.instanceOf(Map),
  setState: PropTypes.func,
  disabled: PropTypes.bool,
};

const Activity = () => {
  return (
    <div className="f-12">
      No. of Total Quiz Results Published by the staff for the course / No. of assigned sessions
      completed by the staff for the course * 100
    </div>
  );
};

const SupportSession = ({ state, setState, disabled }) => {
  return (
    <div className="f-12">
      No. of Support Sessions Completed /
      <Checkbox
        disabled={disabled}
        size="small"
        checked={state.getIn(['supportSession', 'regular'], false)}
        onChange={(e) =>
          setState((prev) => prev.setIn(['supportSession', 'regular'], e.target.checked))
        }
      />
      Regular Session
      <Checkbox
        disabled={disabled}
        size="small"
        checked={state.getIn(['supportSession', 'activity'], false)}
        onChange={(e) =>
          setState((prev) => prev.setIn(['supportSession', 'activity'], e.target.checked))
        }
      />
      Activity (Quiz) * 100
      <br />(<span className="bold">Note:</span>
      {`"Choose the session type you'd like to calculate."`})
    </div>
  );
};
SupportSession.propTypes = {
  state: PropTypes.instanceOf(Map),
  setState: PropTypes.func,
  disabled: PropTypes.bool,
};

const CalculationLayout = Object.freeze({
  staff_attendance: StaffAttendance,
  engager: Engager,
  quiz: Activity,
  support_session: SupportSession,
});
export default CalculationLayout;
