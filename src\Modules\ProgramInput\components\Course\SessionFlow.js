import React, { useState, Suspense, useEffect } from 'react';
import PropTypes from 'prop-types';
import { fromJS, List, Map } from 'immutable';
import { Table } from 'react-bootstrap';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import ListItemText from '@mui/material/ListItemText';
import Select from '@mui/material/Select';
import Checkbox from '@mui/material/Checkbox';
import { ThemeProvider } from '@mui/styles';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import ContactHoursSummary from './ContactHoursSummary';
import { getSubjects, CHECKBOX_THEME } from './utils';
import Input from '../../../../Widgets/FormElements/Input/Input';
import { getRandomId } from '../../../InfrastructureManagement/utils';
import { capitalize } from '../../../InfrastructureManagement/utils';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { getURLParams, indVerRename, isModuleEnabled } from 'utils';

import Tab from '@mui/material/Tab';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import { Box, Divider, Drawer } from '@mui/material';
import PostAddIcon from '@mui/icons-material/PostAdd';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MaterialDialog from 'Widgets/FormElements/material/DialogModal';
import MButton from 'Widgets/FormElements/material/Button';
import UploadIcon from '@mui/icons-material/Upload';
import fileUpload from 'Assets/fileUploadImg.svg';
import DragAndDrop from './DragAndDrop';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import Tooltips from 'Widgets/FormElements/material/Tooltip';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import { generateSignedUrl } from '_reduxapi/program_input/action';
import { useDispatch, useSelector } from 'react-redux';
import { selectGetSignedUrl } from '_reduxapi/program_input/selectors';
import PhotoIcon from '@mui/icons-material/Photo';
import moment from 'moment';
import { getShortString } from 'Modules/Shared/v2/Configurations';

const CourseSessionOrder = React.lazy(() => import('./AddCourseSessionOrderModal.js'));

function SessionFlow(props) {
  const dispatch = useDispatch();
  const SignedUrl = useSelector(selectGetSignedUrl);
  const [openPopUp, setOpenPopUp] = useState(false);
  const [value, setValue] = useState('1');
  const {
    course,
    editedSessionFlow,
    setData,
    getCourseSessionOrderModule,
    postCourseSessionOrderModule,
    courseSessionOrderModule,
    maxHours,
    editedAdditionalSessionFlow,
  } = props;
  const [textModalOpen, setModalOpen] = useState(false);
  const [textAreaIndex, setTextAreaIndex] = useState(0);
  const [drawerIndex, setDrawerIndex] = useState(0);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [files, setFiles] = useState(List());

  const program_id = getURLParams('_id', true);
  const deliveryTypes = getDeliveryTypes();

  // const additionalDeliveryType = [
  //   {
  //     name: 'Feedback',
  //     value: '1',
  //   },
  //   {
  //     name: 'Counseling',
  //     value: '2',
  //   },
  //   {
  //     name: 'Lecture',
  //     value: '3',
  //   },
  //   {
  //     name: 'Tutorial',
  //     value: '4',
  //   },
  //   {
  //     name: 'Seminar',
  //     value: '5',
  //   },
  // ];

  useEffect(() => {
    getCourseSessionOrderModule();
  }, [getCourseSessionOrderModule]);

  function getTotalHours() {
    const hours = course.get('credit_hours', List()).map((c) => {
      const value = Number(c.get('credit_hours', '0') || '0');
      return value;
    });
    return `${hours.reduce((acc, h) => h + acc, 0)} ${t(
      'program_input.credit_hours'
    )} (${hours.join('+')})`;
  }

  function getDeliveryTypes() {
    return course
      .get('credit_hours', List())
      .reduce((cAcc, c) => {
        const sessionTypeId = c.get('_session_id');
        if (!Number(c.get('credit_hours'))) return cAcc;
        return cAcc.concat(
          c.get('delivery_type', List()).reduce((dAcc, d) => {
            if (!d.get('isActive', false)) return dAcc;
            return dAcc.push(
              d.merge(
                Map({
                  _session_id: sessionTypeId,
                  duration: `${d.get('duration')}`,
                  name: d.get('delivery_type'),
                  value: d.get('_delivery_id'),
                })
              )
            );
          }, List())
        );
      }, List())
      .toJS();
  }

  function handleChange(e, name, key) {
    let value;
    if (['subjects'].includes(name)) {
      value = fromJS(e);
    } else {
      value = e.target.value;
    }

    if (['duration'].includes(name)) {
      if (value !== '') {
        if (!/^\d+$/.test(value)) {
          return;
        }
      }
    }

    if (['delivery_type'].includes(name)) {
      const deliveryType = deliveryTypes.find((d) => d._delivery_id === value);
      if (!deliveryType) {
        setData(
          Map({
            editedSessionFlow: editedSessionFlow.mergeIn(
              [key],
              Map({
                delivery_type: '',
                _delivery_id: '',
                delivery_symbol: '',
                delivery_no: '',
              })
            ),
          })
        );
        return;
      }
      setData(
        Map({
          editedSessionFlow: editedSessionFlow.mergeIn(
            [key],
            Map({
              _session_id: deliveryType._session_id,
              delivery_type: deliveryType.delivery_type,
              _delivery_id: deliveryType._delivery_id,
              delivery_symbol: deliveryType.delivery_symbol,
              delivery_no:
                editedSessionFlow.filter(
                  (s) => s.get('delivery_symbol') === deliveryType.delivery_symbol
                ).size + 1,
              duration: deliveryType.duration,
            })
          ),
        })
      );
      return;
    }

    setData(
      Map({
        editedSessionFlow: editedSessionFlow.setIn([key, name], value),
      })
    );
  }

  function handleChangeAdditional(e, name, key) {
    let value;
    if (['subjects'].includes(name)) {
      value = fromJS(e);
    } else {
      value = e.target.value;
    }

    if (['duration'].includes(name)) {
      if (value !== '') {
        if (!/^\d+$/.test(value)) {
          return;
        }
      }
    }

    if (['delivery_type'].includes(name)) {
      const deliveryType = deliveryTypes.find((d) => d._delivery_id === value);
      if (!deliveryType) {
        setData(
          Map({
            editedAdditionalSessionFlow: editedAdditionalSessionFlow.mergeIn(
              [key],
              Map({
                delivery_type: '',
                _delivery_id: '',
                delivery_symbol: '',
                delivery_no: '',
              })
            ),
          })
        );
        return;
      }
      setData(
        Map({
          editedAdditionalSessionFlow: editedAdditionalSessionFlow.mergeIn(
            [key],
            Map({
              _session_id: deliveryType._session_id,
              delivery_type: deliveryType.delivery_type,
              _delivery_id: deliveryType._delivery_id,
              delivery_symbol: deliveryType.delivery_symbol,
              delivery_no:
                editedAdditionalSessionFlow.filter(
                  (s) => s.get('delivery_symbol') === deliveryType.delivery_symbol
                ).size + 1,
              duration: deliveryType.duration,
            })
          ),
        })
      );
      return;
    }

    setData(
      Map({
        editedAdditionalSessionFlow: editedAdditionalSessionFlow.setIn([key, name], value),
      })
    );
  }

  function handleAddRemove(type, index) {
    if (type === 'add') {
      if (value === '1') {
        setData(
          Map({
            editedSessionFlow: editedSessionFlow.push(
              Map({
                s_no: '',
                delivery_type: '',
                _delivery_id: '',
                delivery_symbol: '',
                delivery_no: '',
                delivery_topic: '',
                subjects: List(),
                duration: '',
                _id: getRandomId(),
                _module_id: '',
              })
            ),
          })
        );
        return;
      } else {
        setData(
          Map({
            editedAdditionalSessionFlow: editedAdditionalSessionFlow.push(
              Map({
                s_no: '',
                delivery_type: '',
                _delivery_id: '',
                delivery_symbol: '',
                delivery_no: '',
                delivery_topic: '',
                subjects: List(),
                duration: '',
                _id: getRandomId(),
                _module_id: '',
              })
            ),
          })
        );
        return;
      }
    }

    if (value === '1') {
      setData(
        Map({
          editedSessionFlow: editedSessionFlow.delete(index),
        })
      );
    } else {
      setData(
        Map({
          editedAdditionalSessionFlow: editedAdditionalSessionFlow.delete(index),
        })
      );
    }
  }

  function handleModal() {
    setOpenPopUp(!openPopUp);
  }

  function getCourseModuleList() {
    const arrayValue = courseSessionOrderModule.map((item) => {
      return { name: item.get('moduleName', ''), value: item.get('_id', '') };
    });
    return [{ name: '', value: '' }, ...arrayValue];
  }

  const handleClearAll = () => {
    setData(
      Map({
        ...(value === '1' && { editedSessionFlow: List() }),
        ...(value === '2' && { editedAdditionalSessionFlow: List() }),
      })
    );
  };

  const handleClickModalOpen = (i) => {
    setTextAreaIndex(i);
    setModalOpen(true);
  };

  const handleClickModalClose = () => {
    setModalOpen(false);
  };

  const handleClearPopup = () => {
    setData(
      Map({
        editedSessionFlow: editedSessionFlow.setIn([textAreaIndex, 'sessionData'], ''),
      })
    );
  };

  const handleDrawerOpen = (i) => {
    setDrawerIndex(i);
    setDrawerOpen(true);
    setFiles(editedSessionFlow.getIn([i, 'sessionDocumentDetails'], List()));
  };

  const handleDrawerClose = () => {
    setDrawerOpen(false);
  };

  const drawerPaperW40 = {
    '& .MuiDrawer-paper': { width: '35em' },
  };

  const handleSaveDrawer = () => {
    setData(
      Map({
        editedSessionFlow: editedSessionFlow.setIn(
          [drawerIndex, 'sessionDocumentDetails'],
          fromJS(files)
        ),
      })
    );
    setDrawerOpen(false);
  };

  const [open, setOpen] = useState(false);

  const handleClickOpen1 = (modalData) => {
    setOpen(true);
    dispatch(generateSignedUrl(modalData.get('url', '')));
  };
  const handleCloseBoard = () => {
    setOpen(false);
  };

  return (
    <>
      <div className="session_card border-bottom">
        <div className="d-flex justify-content-between mb-2">
          <div>
            <p className="mb-1 bold">
              {`${course.get('course_code', '')} - ${course.get('course_name', '')} (${t(
                `program_input.course_types.${capitalize(
                  indVerRename(course.get('course_type', ''), program_id)
                )}`
              )})`}
            </p>
            <span>{getTotalHours()}</span>
          </div>
          <small className="text-gray">
            {' '}
            <Trans i18nKey={'all_fields_mandatory'}></Trans>
          </small>
        </div>
      </div>

      <div className="session_card">
        <div className="row">
          <div className="col-md-6">
            <p className="mb-1 bold text-left">
              {' '}
              <Trans i18nKey={'session_flow'}></Trans>
            </p>
            <div className="row ml-0">
              <div className="pr-2">
                <span className="f-14">
                  {' '}
                  <Trans i18nKey={'contact_hours_summary'}></Trans> :{' '}
                </span>
              </div>
              <ContactHoursSummary
                course={course}
                sessionFlow={editedSessionFlow}
                programId={course.get('_program_id', '')}
              />
            </div>
          </div>
          <div className="col-md-2">
            {isModuleEnabled('SIS_SYNC') && (
              <Input
                elementType="floatinginput"
                elementConfig={{
                  type: 'text',
                }}
                value={maxHours}
                maxLength={3}
                floatingLabel={'Max Hours'}
                changed={(e) => {
                  const value = e.target.value;
                  if (value !== '') {
                    if (!/^\d+$/.test(value)) {
                      return;
                    }
                  }
                  setData(Map({ max_hours: value }));
                }}
              />
            )}
          </div>
          <div className="col-md-2" style={{ padding: '0px' }}>
            <p className="text-skyblue bold cursor-pointer" onClick={() => handleModal()}>
              <i className="fa fa-plus pr-2" aria-hidden="true"></i>
              Add Course Order
            </p>
          </div>

          <div className="col-md-2" style={{ padding: '0px' }}>
            {(CheckPermission('pages', 'Program Input', 'Programs', 'Add Program') ||
              CheckPermission('pages', 'Program Input', 'Programs', 'Add Pre-requisite')) && (
              <p
                className="mb-1 text-right text-skyblue bold cursor-pointer"
                onClick={() => handleAddRemove('add')}
              >
                <i className="fa fa-plus pr-2" aria-hidden="true"></i>
                <Trans i18nKey={'add_new_sessions'}></Trans>
              </p>
            )}
          </div>
        </div>

        <TabContext value={value}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <TabList
              onChange={(event, newValue) => {
                setValue(newValue);
              }}
            >
              <Tab sx={{ padding: 0, textTransform: 'none' }} label="Regular Session" value="1" />
              <Tab
                sx={{ padding: '10px 10px 10px 40px', textTransform: 'none' }}
                label="Additional Session"
                value="2"
              />
              <div
                className="ml-auto pt-2 pr-2 cursor-pointer text-blue bold"
                onClick={() => handleClearAll()}
              >
                Clear All
              </div>
            </TabList>
          </Box>
          <TabPanel
            value="1"
            sx={{
              paddingTop: '10px',
              '&.MuiTabPanel-root': {
                padding: '0px',
              },
            }}
          >
            <div className="col-md-12 mt-2 min_h overflow_height p-0">
              <Table responsive hover>
                <thead className="th-change">
                  <tr>
                    <th>
                      {' '}
                      <Trans i18nKey={'s_no'}></Trans>
                    </th>
                    <th>
                      <Trans i18nKey={'configuration.delivery_type'}></Trans>
                    </th>
                    <th>
                      <Trans i18nKey={'delivery_symbol_new'}></Trans>
                    </th>
                    <th>
                      <Trans i18nKey={'delivery_number_new'}></Trans>
                    </th>
                    <th>
                      <Trans i18nKey={'delivery_topic'}></Trans>
                    </th>
                    <th>
                      {' '}
                      <Trans i18nKey={'configuration.the_subject'}></Trans>
                    </th>
                    <th>
                      {' '}
                      <Trans i18nKey={'duration_in_min'}></Trans>
                    </th>
                    {courseSessionOrderModule?.size > 0 && <th> Unit/Topic/Themes</th>}
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {editedSessionFlow.map((s, i) => (
                    <tr key={s.get('_id')} className="tr-change">
                      <td>
                        <div className="mt-2">{i + 1}</div>
                      </td>
                      <td>
                        <div className="mt--25 w-150px">
                          <Input
                            elementType="floatingselect"
                            elementConfig={{
                              options: [{ name: '', value: '' }, ...deliveryTypes],
                            }}
                            value={s.get('_delivery_id', '')}
                            changed={(e) => handleChange(e, 'delivery_type', i)}
                          />
                        </div>
                      </td>
                      <td>
                        <div className="mt-2">{s.get('delivery_symbol', '')}</div>
                      </td>
                      <td>
                        <div className="mt-2">{s.get('delivery_no', '')}</div>
                      </td>
                      <td>
                        <div className="mt--25 w-150px">
                          <Input
                            elementType="floatinginput2"
                            elementConfig={{
                              type: 'text',
                            }}
                            value={s.get('delivery_topic', '')}
                            placeholder={t('program_input.labels.enter_topic')}
                            changed={(e) => handleChange(e, 'delivery_topic', i)}
                          />
                        </div>
                      </td>
                      <td>
                        <div className="mt--10 w-150px">
                          <MultiSelect
                            options={getSubjects(course)}
                            selected={s.get('subjects', List()).toJS()}
                            handleChange={handleChange}
                            data={{ index: i, key: 'subjects' }}
                          />
                        </div>
                      </td>
                      <td>
                        <div className="mt--25 w-100px">
                          <Input
                            elementType="floatinginput2"
                            elementConfig={{
                              type: 'text',
                            }}
                            value={s.get('duration', '')}
                            placeholder={t('program_input.labels.enter_duration')}
                            changed={(e) => handleChange(e, 'duration', i)}
                          />
                        </div>
                      </td>
                      {courseSessionOrderModule?.size > 0 && (
                        <td>
                          {' '}
                          <div className="mt--25">
                            <Input
                              elementType="floatingselect"
                              elementConfig={{
                                options: getCourseModuleList(),
                              }}
                              value={s.get('_module_id', '')}
                              changed={(e) => handleChange(e, '_module_id', i)}
                            />
                          </div>
                        </td>
                      )}
                      <td>
                        <div>
                          <PostAddIcon
                            color="primary"
                            className="cursor-pointer"
                            onClick={() => handleClickModalOpen(i)}
                          />
                        </div>
                      </td>
                      <td>
                        <div className="d-flex">
                          <div style={{ color: '#1976D2', fontSize: '14px', paddingTop: '1px' }}>
                            {String(
                              s.get('sessionDocumentDetails', List()).filter(Boolean).size
                            ).padStart(2, '0')}
                          </div>
                          {s.get('sessionDocumentDetails', List()).filter(Boolean).size ? (
                            <div>
                              <Tooltips
                                backgroundColor="#374151"
                                title={s
                                  .get('sessionDocumentDetails', List())
                                  .filter((i) => i)
                                  .map((item, index) => {
                                    return (
                                      <div
                                        key={index}
                                        className={`d-flex align-items-center py-2 ${
                                          index !== 0 && 'border-top'
                                        }`}
                                      >
                                        <div className="d-flex align-items-center pl-2 pr-2 mr-2 image-border-program-inputs">
                                          <div className="mr-2 ">
                                            {item.get('url', '').includes('.pdf') ? (
                                              <PictureAsPdfIcon
                                                fontSize="small"
                                                color="error"
                                                className="mt-1"
                                              />
                                            ) : (
                                              <PhotoIcon
                                                fontSize="small"
                                                color="error"
                                                className="mt-1"
                                              />
                                            )}
                                          </div>
                                          <div className="text-body f-15">
                                            <div>
                                              <div
                                                className="text-body f-15 ml-1 text-truncate cursor-pointer gray-text-tooltip-title"
                                                style={{ maxWidth: '400px' }}
                                              >
                                                <div>
                                                  {getShortString(item.get('fileName', ''), 10)}
                                                </div>
                                              </div>
                                              {item.get('uploadedAt', '') ? (
                                                <div className="gray-text-tooltip ml-1">
                                                  {moment(item.get('uploadedAt', '')).format(
                                                    'DD/MM/YYYY (hh:mm A)'
                                                  )}
                                                </div>
                                              ) : (
                                                ''
                                              )}
                                            </div>
                                          </div>
                                        </div>
                                        <div
                                          className="ml-auto cursor-pointer bold f-15 pr-2 pl-2 view-border d-flex align-items-center"
                                          onClick={() => handleClickOpen1(item)}
                                        >
                                          View
                                        </div>
                                      </div>
                                    );
                                  })}
                              >
                                <AttachFileIcon className="cursor-pointer" />
                              </Tooltips>
                            </div>
                          ) : (
                            <AttachFileIcon className="cursor-pointer" />
                          )}
                        </div>
                      </td>
                      <td>
                        <div>
                          <UploadIcon
                            color="primary"
                            className="cursor-pointer"
                            onClick={() => handleDrawerOpen(i)}
                          />
                        </div>
                      </td>
                      <td>
                        <div className="mt-2">
                          <i
                            className="fa fa-times fa-lg remove_hover f-14"
                            aria-hidden="true"
                            onClick={() => handleAddRemove('remove', i)}
                          ></i>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          </TabPanel>

          <TabPanel
            value="2"
            sx={{
              paddingTop: '10px',
              '&.MuiTabPanel-root': {
                padding: '0px',
              },
            }}
          >
            <div className="col-md-12 mt-2 min_h overflow_height p-0">
              <Table responsive hover>
                <thead className="th-change">
                  <tr>
                    <th>
                      {' '}
                      <Trans i18nKey={'s_no'}></Trans>
                    </th>
                    <th>
                      <Trans i18nKey={'configuration.delivery_type'}></Trans>
                    </th>
                    <th>
                      <Trans i18nKey={'delivery_symbol_new'}></Trans>
                    </th>
                    <th>
                      <Trans i18nKey={'delivery_number_new'}></Trans>
                    </th>
                    <th>
                      <Trans i18nKey={'delivery_topic'}></Trans>
                    </th>
                    <th>
                      {' '}
                      <Trans i18nKey={'configuration.the_subject'}></Trans>
                    </th>
                    <th>
                      {' '}
                      <Trans i18nKey={'duration_in_min'}></Trans>
                    </th>
                    {courseSessionOrderModule?.size > 0 && <th> Unit/Topic/Themes</th>}
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {editedAdditionalSessionFlow.map((s, i) => (
                    <tr key={s.get('_id')} className="tr-change">
                      <td>
                        <div className="mt-2">{i + 1}</div>
                      </td>
                      <td>
                        <div className="mt--25 w-150px">
                          <Input
                            elementType="floatingselect"
                            elementConfig={{
                              options: [
                                { name: '', value: '' },
                                // ...additionalDeliveryType,
                                ...deliveryTypes,
                              ],
                            }}
                            value={s.get('_delivery_id', '')}
                            changed={(e) => handleChangeAdditional(e, 'delivery_type', i)}
                          />
                        </div>
                      </td>
                      <td>
                        <div className="mt-2">{s.get('delivery_symbol', '')}</div>
                      </td>
                      <td>
                        <div className="mt-2">{s.get('delivery_no', '')}</div>
                      </td>
                      <td>
                        <div className="mt--25 w-150px">
                          <Input
                            elementType="floatinginput2"
                            elementConfig={{
                              type: 'text',
                            }}
                            value={s.get('delivery_topic', '')}
                            placeholder={t('program_input.labels.enter_topic')}
                            changed={(e) => handleChangeAdditional(e, 'delivery_topic', i)}
                          />
                        </div>
                      </td>
                      <td>
                        <div className="mt--10 w-150px">
                          <MultiSelect
                            options={getSubjects(course)}
                            selected={s.get('subjects', List()).toJS()}
                            handleChange={handleChangeAdditional}
                            data={{ index: i, key: 'subjects' }}
                          />
                        </div>
                      </td>

                      <td>
                        <div className="mt--25 w-100px">
                          <Input
                            elementType="floatinginput2"
                            elementConfig={{
                              type: 'text',
                            }}
                            value={s.get('duration', '')}
                            placeholder={t('program_input.labels.enter_duration')}
                            changed={(e) => handleChangeAdditional(e, 'duration', i)}
                          />
                        </div>
                      </td>
                      {courseSessionOrderModule?.size > 0 && (
                        <td>
                          {' '}
                          <div className="mt--25">
                            <Input
                              elementType="floatingselect"
                              elementConfig={{
                                options: getCourseModuleList(),
                              }}
                              value={s.get('_module_id', '')}
                              changed={(e) => handleChangeAdditional(e, '_module_id', i)}
                            />
                          </div>
                        </td>
                      )}
                      <td>
                        <div className="mt-2">
                          <i
                            className="fa fa-times fa-lg remove_hover f-14"
                            aria-hidden="true"
                            onClick={() => handleAddRemove('remove', i)}
                          ></i>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          </TabPanel>
        </TabContext>
      </div>
      {openPopUp && (
        <Suspense fallback="">
          <CourseSessionOrder
            callBack={handleModal}
            saveModal={postCourseSessionOrderModule}
            courseSessionOrderModule={courseSessionOrderModule}
            setData={setData}
          />
        </Suspense>
      )}

      <div>
        <MaterialDialog
          show={textModalOpen}
          onClose={handleClickModalClose}
          aria-labelledby="responsive-dialog-title"
          maxWidth={'sm'}
          fullWidth={true}
        >
          <div className="p-5">
            <div className="bold">Session Content</div>
            <MaterialInput
              elementType={'materialTextArea'}
              type={'text'}
              variant={'outlined'}
              placeholder={'Type your text here...'}
              minRows={'5'}
              multiline
              value={editedSessionFlow.getIn([textAreaIndex, 'sessionData'], '')}
              changed={(e) => handleChange(e, 'sessionData', textAreaIndex)}
            />
            <div className="d-flex">
              <div>
                <MButton variant="outlined" color={'gray'} clicked={() => handleClearPopup()}>
                  Clear
                </MButton>
              </div>
              <div className="ml-auto">
                <MButton variant="outlined" color={'gray'} clicked={() => setModalOpen(false)}>
                  Cancel
                </MButton>

                <MButton
                  variant="contained"
                  color="primary"
                  className="ml-2"
                  clicked={() => setModalOpen(false)}
                  disabled={!editedSessionFlow.getIn([textAreaIndex, 'sessionData'], '')?.trim()}
                >
                  Save
                </MButton>
              </div>
            </div>
          </div>
        </MaterialDialog>
      </div>
      <Drawer
        sx={drawerPaperW40}
        anchor="right"
        open={drawerOpen}
        onClose={() => handleDrawerClose()}
      >
        <div className="p-3">
          <div className="d-flex">
            <div>
              <div className="drawer-heading-24px">Sessions Documents</div>
              <div className="drawer-heading-14px">
                {getSubjects(course).map((subject) => {
                  if (
                    editedSessionFlow
                      .getIn([drawerIndex, 'subjects'], List())
                      .map((i) => i.subject_id)
                      .includes(subject.subject_id)
                  ) {
                    return subject.subject_name;
                  }
                })}{' '}
                . {editedSessionFlow.getIn([drawerIndex, 'delivery_type'], '')} .{' '}
                {editedSessionFlow.getIn([drawerIndex, 'delivery_topic'], '')}
              </div>
            </div>
            <div className="d-flex ml-auto">
              <div>
                <MButton variant="outlined" color="gray" clicked={() => setDrawerOpen(false)}>
                  Cancel
                </MButton>
              </div>
              <div className="ml-2">
                <MButton clicked={() => handleSaveDrawer()}>Save</MButton>
              </div>
            </div>
          </div>
          <Divider className="mt-1 mb-1" />
          <div className="text-center p-3">
            <img src={fileUpload} alt="fileUpload" className={``} />
          </div>

          <DragAndDrop files={files} setFiles={setFiles} />
        </div>
      </Drawer>

      {open && (
        <MaterialDialog maxWidth={'sm'} fullWidth={true} show={open} onClose={() => {}}>
          <div>
            <div className="text-center m-3">
              <div className="Img-view-height">
                {SignedUrl.includes('.pdf') ? (
                  <div className="embed-responsive embed-responsive-4by3 pdf-iframe-height">
                    <iframe src={SignedUrl} title="pdf" className="embed-responsive-item"></iframe>
                  </div>
                ) : (
                  <div className="row">
                    <img src={SignedUrl} alt="img" className="img-fluid rounded col-12" />
                  </div>
                )}
              </div>
              <div className="text-right">
                <MButton
                  variant="text"
                  clicked={() => {
                    handleCloseBoard();
                  }}
                  className="close-text-transform"
                >
                  Close
                </MButton>
              </div>
            </div>
          </div>
        </MaterialDialog>
      )}
    </>
  );
}

function MultiSelect({ options, selected, handleChange, data: { index, key } }) {
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
    getContentAnchorEl: null,
    anchorOrigin: {
      vertical: 'bottom',
      horizontal: 'center',
    },
    transformOrigin: {
      vertical: 'top',
      horizontal: 'center',
    },
    variant: 'menu',
  };

  const optionIdList = options.map((o) => o.value);

  const handleSelectAllClick = (e) => {
    const checked = e.target.checked;
    if (checked) {
      handleChange(optionIdList, key, index);
      return;
    }
    handleChange([], key, index);
  };

  const handleOptionChange = (e) => {
    let value = e.target.value;
    if (value[value.length - 1] === 'All Subjects') {
      handleChange(selected.length === options.length ? [] : optionIdList, key, index);
      return;
    }
    handleChange(
      value.filter((v) => v !== 'All Subjects'),
      key,
      index
    );
  };

  return (
    <FormControl fullWidth variant="standard" size="small" className="pt-2">
      <Select
        multiple
        value={selected}
        onChange={handleOptionChange}
        renderValue={(selected) =>
          options
            .reduce((acc, o) => (selected.includes(o.value) ? [...acc, o.name] : acc), [])
            .join(', ')
        }
        MenuProps={MenuProps}
      >
        <MenuItem className="white-space-normal" value="All Subjects">
          <ThemeProvider theme={CHECKBOX_THEME}>
            <Checkbox
              indeterminate={selected.length > 0 && selected.length < options.length}
              checked={options.length > 0 && selected.length === options.length}
              onChange={handleSelectAllClick}
              color="primary"
            />
          </ThemeProvider>
          <ListItemText
            primary={t('program_input.all_subjects_f')}
            classes={{ primary: 'font-weight-bold' }}
          />
        </MenuItem>
        {options.map((option) => (
          <MenuItem className="white-space-normal" key={option.value} value={option.value}>
            <ThemeProvider theme={CHECKBOX_THEME}>
              <Checkbox
                checked={Boolean(selected.find((s) => s === option.value))}
                color="primary"
              />
            </ThemeProvider>
            <ListItemText primary={option.name} />
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
}

SessionFlow.propTypes = {
  course: PropTypes.instanceOf(Map),
  sessionFlow: PropTypes.instanceOf(Map),
  editedSessionFlow: PropTypes.instanceOf(List),
  editedAdditionalSessionFlow: PropTypes.instanceOf(List),
  setData: PropTypes.func,
  courseSessionOrderModule: PropTypes.instanceOf(List),
  getCourseSessionOrderModule: PropTypes.func,
  postCourseSessionOrderModule: PropTypes.func,
  maxHours: PropTypes.number,
};

MultiSelect.propTypes = {
  options: PropTypes.array,
  selected: PropTypes.array,
  handleChange: PropTypes.func,
  data: PropTypes.object,
};

export default SessionFlow;
