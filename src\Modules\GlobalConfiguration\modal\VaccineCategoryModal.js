import React, { useState } from 'react';
import PropTypes from 'prop-types';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import MaterialInput from 'Widgets/FormElements/material/Input';
import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { checkIndividualData } from '../utils';
import CancelModal from 'Containers/Modal/Cancel';

const VaccineCategoryModal = ({ show, onClose, vaccineId, categoryName, handleSave, setData }) => {
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [category, setCategory] = useState(categoryName);
  const [edited, setEdited] = useState(false);

  const handleChange = (e) => {
    setCategory(e.target.value);
    setEdited(true);
  };

  const onClickSave = () => {
    const operation = vaccineId ? 'update' : 'add';
    const formData = { categoryName: category.trim() };
    if (!checkIndividualData('Category name', formData.categoryName, setData))
      handleSave(operation, formData);
  };

  const handleCancel = () => {
    edited ? setShowCancelModal(true) : onClose();
  };

  return (
    <>
      <DialogModal show={show} onClose={handleCancel}>
        <div className="p-4">
          <div className="digi-text-color">
            <p className="f-22">
              <Trans
                i18nKey={
                  vaccineId
                    ? 'role_management.role_actions.Edit Category'
                    : 'global_configuration.add_new_category'
                }
              />
            </p>
            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              label={<Trans i18nKey={'userManagement.category_name'} />}
              labelclass={'mb-1 f-14'}
              placeholder={t('userManagement.category_name')}
              value={category}
              changed={(e) => handleChange(e)}
              maxLength={100}
              size={'small'}
            />
            <div className="text-right mt-4">
              <MButton color="gray" variant="outlined" className="mr-2" clicked={handleCancel}>
                <Trans i18nKey={'cancel'} />
              </MButton>

              <MButton
                color="primary"
                variant="contained"
                clicked={onClickSave}
                disabled={!edited || !category.trim()}
              >
                <Trans i18nKey={'save'} />
              </MButton>
            </div>
          </div>
        </div>
      </DialogModal>

      <CancelModal showCancel={showCancelModal} setCancel={setShowCancelModal} setShow={onClose} />
    </>
  );
};

VaccineCategoryModal.propTypes = {
  show: PropTypes.bool,
  onClose: PropTypes.func,
  vaccineId: PropTypes.string,
  categoryName: PropTypes.string,
  handleSave: PropTypes.func,
  setData: PropTypes.func,
};

export default VaccineCategoryModal;
