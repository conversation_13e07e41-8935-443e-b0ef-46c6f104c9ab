import React, { useState } from 'react';

import AllCollegeGraph from './AllCollegeGraph';
import CollegeList from './CollegeList';
import parentContext from '../Context/university-context';

function AllColleges() {
  const [search, setSearch] = useState('');
  const [option, setSelectOption] = useState('');
  const [sort, setSort] = useState(true);
  return (
    <div className="main pb-5 bg-gray">
      <div className="container">
        <div className="p-3">
          <div className="row justify-content-center pt-3">
            <parentContext.filterSearchContext.Provider
              value={{ search, option, setSearch, setSelectOption, sort, setSort }}
            >
              <AllCollegeGraph />
              <CollegeList />
            </parentContext.filterSearchContext.Provider>
          </div>
        </div>
      </div>
    </div>
  );
}

export default AllColleges;
