import React, { Component } from 'react';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { Map } from 'immutable';
import { withRouter } from 'react-router-dom';
import InstitutionType from './InstitutionOnboarding/Component/InstitutionType';
import * as actions from '../../../_reduxapi/institution/actions';
import { selectCountryList } from '../../../_reduxapi/institution/selectors';
import { selectIsInstitutionOnboarded } from '../../../_reduxapi/Common/Selectors';
import { selectUserId } from '../../../_reduxapi/Common/Selectors';
//import Logo from '../../../Assets/top-logo-white.png';
import DS_logo from '../../../Assets/ds_logo.svg';
import '../css/institution.css';
import InstitutionForm from './InstitutionForm';
import { fileSizeTypeCheck } from '../../../utils';

class InstitutionOnboarding extends Component {
  constructor() {
    super();
    this.state = {
      institutionType: {
        name: '',
        type: '',
      },
      institution: Map(),
      showInstitutionForm: false,
      logo: null,
    };
    this.handleInstitutionTypeSelect = this.handleInstitutionTypeSelect.bind(this);
    this.handleNext = this.handleNext.bind(this);
    this.handleBack = this.handleBack.bind(this);
    this.handleChange = this.handleChange.bind(this);
    this.handleSubmit = this.handleSubmit.bind(this);
  }

  componentDidMount() {
    if (this.props.isInstitutionOnboarded) {
      this.props.history.replace('/overview');
      return;
    }
    this.props.getCountryList();
  }

  handleInstitutionTypeSelect(institutionType) {
    this.setState({
      institutionType,
    });
  }

  handleNext() {
    this.setState({
      showInstitutionForm: true,
      institution: Map({
        _user_id: this.props.userId,
        type: this.state.institutionType.type,
        name: 'test',
        accreditation: 'zfdsf',
        no_of_college: '2',
        code: '323',
        ...(this.state.institutionType.type === 'group' && { no_of_college: '' }),
        address_details: {
          address: 'dsadsa',
          country: 'India',
          state: 'tn',
          city: 'ch',
          district: 'ch',
          zipcode: '333',
        },
      }),
      logo: null,
    });
  }

  handleBack() {
    this.setState({
      showInstitutionForm: false,
      institution: Map(),
      logo: null,
    });
  }

  handleChange(event, name) {
    if (name === 'logo') {
      let files = event;
      if (!files) files = [];
      if (!fileSizeTypeCheck(this.props, files[0])) {
        this.setState({
          logo: null,
        });
        return;
      } else {
        this.setState({
          logo: files.length ? files[0] : null,
        });
        return;
      }
    }

    const value = event.target.value;

    if (['no_of_college', 'zipcode'].includes(name)) {
      if (value !== '') {
        if (!/^\d+$/.test(value)) {
          return;
        }
      }
    }

    const setOperation = ['address', 'country', 'state', 'city', 'district', 'zipcode'].includes(
      name
    )
      ? 'setIn'
      : 'set';

    this.setState((state) => {
      return {
        institution: state.institution[setOperation](
          setOperation === 'set' ? name : ['address_details', name],
          value
        ).mergeDeep(
          Map({
            address_details: Map({
              ...(name === 'country' && { state: '', city: '', district: '' }),
              ...(name === 'state' && { city: '', district: '' }),
              ...(name === 'city' && { district: value }),
            }),
          })
        ),
      };
    });
  }

  handleSubmit(data) {
    this.props.addInstitution({ ...data, history: this.props.history });
  }

  render() {
    const { showInstitutionForm, institutionType, institution, logo } = this.state;
    const { countryList } = this.props;

    return (
      <>
        <div className="login-bg">
          <div className="container">
            <div className="row justify-content-center pt-3">
              <div className="col-md-8 col-xl-4 col-lg-6 col-12">
                <h3 className="text-center">
                  <img src={DS_logo} alt="DigiScheduler" />
                </h3>
              </div>
            </div>
            {!showInstitutionForm && (
              <InstitutionType
                type={institutionType.type}
                handleTypeSelect={this.handleInstitutionTypeSelect}
                handleNext={this.handleNext}
              />
            )}
            {showInstitutionForm && (
              <InstitutionForm
                countryList={countryList} //temp change it while save
                institutionType={institutionType}
                institution={institution}
                logo={logo}
                handleChange={this.handleChange}
                handleBack={this.handleBack}
                handleSubmit={this.handleSubmit}
                operation="create"
              />
            )}
            <div className="row justify-content-center pt-4 pb-2">
              <p className="text-center text-white">
                &copy; {new Date().getFullYear()},{' '}
                <a href="https://digi-val.com/" target="blank" className="text-white">
                  Digival IT Solutions - UAE (in technology partnership with Digival Solutions Pvt
                  Ltd, India)
                </a>
                . All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </>
    );
  }
}

InstitutionOnboarding.propTypes = {
  userId: PropTypes.string,
  history: PropTypes.object,
  isInstitutionOnboarded: PropTypes.bool,
  countryList: PropTypes.instanceOf(Map),
  getCountryList: PropTypes.func,
  addInstitution: PropTypes.func,
};

const mapStateToProps = (state) => {
  return {
    userId: selectUserId(state),
    countryList: selectCountryList(state),
    isInstitutionOnboarded: selectIsInstitutionOnboarded(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(InstitutionOnboarding);
