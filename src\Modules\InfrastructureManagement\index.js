import React, { Component } from 'react';
import { Route, Switch } from 'react-router';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import { withRouter, <PERSON> } from 'react-router-dom';

import InfrastructureList from './components/InfrastructureList';
import InfrastructureSettings from './components/InfrastructureSettings';

import Loader from '../../Widgets/Loader/Loader';
import Snackbars from '../../Modules/Utils/Snackbars';
import Breadcrumb from '../../Widgets/Breadcrumb/Breadcrumb';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import { selectIsLoading, selectMessage } from '../../_reduxapi/infrastructure/selectors';
import { t } from 'i18next';
import { indVerRename, getEnvLabelChanged, jsUcfirst } from 'utils';

const breadcrumbItems = [
  {
    to: '/infrastructure-management',
    label: `Infrastructure Management - ${
      !getEnvLabelChanged() ? 'Onsite' : jsUcfirst(indVerRename('classroom'))
    }`,
  },
  { to: '/infrastructure-management/settings', label: 'Settings' },
];

class InfrastructureManagement extends Component {
  getBreadcrumbItems() {
    const { location } = this.props;
    const matchedIndex = breadcrumbItems.findIndex((item) => item.to === location.pathname);
    return breadcrumbItems.slice(0, matchedIndex !== -1 ? matchedIndex + 1 : 1);
  }

  render() {
    const { message } = this.props;
    return (
      <div>
        {message && <Snackbars show={true} message={message} />}
        <Loader isLoading={this.props.isLoading} />
        <Breadcrumb>
          {this.getBreadcrumbItems().map(({ to, label }) => (
            <Link className="breadcrumb-icon" style={{ color: '#fff' }} key={to} to={to}>
              {getEnvLabelChanged() ? label : t(`infra_management.bread_crumb.${label}`)}
            </Link>
          ))}
        </Breadcrumb>
        <Switch>
          <Route path="/infrastructure-management" exact component={InfrastructureList}></Route>
          {CheckPermission(
            'tabs',
            'Infrastructure Management',
            'Onsite',
            '',
            'Settings',
            'View'
          ) && (
            <Route
              path="/infrastructure-management/settings"
              component={InfrastructureSettings}
            ></Route>
          )}
        </Switch>
      </div>
    );
  }
}

InfrastructureManagement.propTypes = {
  isLoading: PropTypes.bool,
  message: PropTypes.string,
  location: PropTypes.object,
};

const mapStateToProps = function (state) {
  return {
    isLoading: selectIsLoading(state),
    message: selectMessage(state),
  };
};

export default compose(withRouter, connect(mapStateToProps))(InfrastructureManagement);
