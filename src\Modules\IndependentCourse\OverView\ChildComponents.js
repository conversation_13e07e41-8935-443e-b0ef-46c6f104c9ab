import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import Chip from '@mui/material/Chip';

import Pending from 'Assets/img/v2/alert.svg';
import Completed from 'Assets/img/v2/checkMark.svg';

import Stepper from 'Widgets/FormElements/material/Stepper';
import { independentCourseContext } from '../context';
const translateData = ({ key, tooltipComponent = '' }) => {
  if (tooltipComponent === '') return t(key);
  return <Trans components={tooltipComponent}>{key}</Trans>;
};
const constructData = (data) => {
  return data.map((item, index) => (
    <span key={index}>
      {item.value} {translateData(item.key)} {data.length - 1 !== index ? ' • ' : ''}
    </span>
  ));
};
const getChipData = (data, url, history, match) => (
  <Chip
    label={
      <div className="cursor-pointer" onClick={() => history.push(`${match.url}/${url}`)}>
        {data}
      </div>
    }
    variant="outlined"
  />
);
const sessionData = (data) => [
  { key: { key: 'session_types' }, value: data.get('sessionTypesCount', 0) },
  {
    key: { key: 'configuration.delivery_type' },
    value: data.get('deliveryTypesCount', 0),
  },
];
const getSessionCount = (independentData, historyData) => {
  const { independentOverview } = independentData;
  const { history, match } = historyData;
  if (independentOverview.get('sessionTypesCount', 0) === 0) {
    return translateData({ key: 'independentCourse.noSession' });
  } else {
    let data = sessionData(independentOverview);
    return getChipData(constructData(data), 'session-types', history, match);
  }
};
const getCourseCount = (independentData, historyData) => {
  const { independentOverview, getToolTipData } = independentData;
  const { history, match } = historyData;
  if (independentOverview.get('courseCount', 0) === 0) {
    return translateData({
      key: 'independentCourse.noCourse',
      tooltipComponent: { course: getToolTipData.course },
    });
  } else {
    let data = [
      {
        key: {
          key: 'independentCourse.course',
          tooltipComponent: { course: getToolTipData.course },
        },
        value: independentOverview.get('courseCount', 0),
      },
    ];
    return getChipData(constructData(data), 'course-master-list', history, match);
  }
};
export function Header() {
  const history = useHistory();
  const match = useRouteMatch();
  const historyData = { history, match };
  const independentData = useContext(independentCourseContext);

  return (
    <div className="pt-4 pb-2">
      {getSessionCount(independentData, historyData)} •{' '}
      {getCourseCount(independentData, historyData)}
    </div>
  );
}

Header.propTypes = {
  children: PropTypes.array,
};

const getIsConfigured = (independentOverview) => [
  independentOverview.get('sessionTypesCount', 0) &&
    independentOverview.get('deliveryTypesCount', 0),
  independentOverview.get('courseCount', 0),
];
const getSessionData = (independentOverview) => {
  if (independentOverview.get('sessionTypesCount', 0) === 0)
    return 'Session Types & Delivery types';
  let data = sessionData(independentOverview);
  return constructData(data);
};
const getCourseData = (independentOverview, getToolTipData) => {
  if (independentOverview.get('courseCount', 0) === 0)
    return (
      <>
        {t('Create')} {getToolTipData.course}
      </>
    );

  let data = [
    {
      key: { key: 'independentCourse.course', tooltipComponent: { course: getToolTipData.course } },
      value: independentOverview.get('courseCount', 0),
    },
  ];
  return constructData(data);
};

const showViewOrConfigure = (showView, url, history, match) => (
  <>
    <div
      className="col-md-3 col-xl-2 col-lg-2 col-3 mt-3"
      onClick={() => history.push(`${match.url}/${url}`)}
    >
      <p className="text-blue text-right remove_hover">
        <Trans i18nKey={!showView ? 'add_colleges.configure_s' : 'view_s'}></Trans>
      </p>
    </div>
    <div className="clearfix"></div>
  </>
);
const showStatusMessage = (tooltipComponent, key) => (
  <p className="font-weight-normal pt-0 mb-0 text-left f-12">
    {translateData({ tooltipComponent, key })}
  </p>
);
const showStatusImage = (status) => (
  <div className="float-left   mt-3 mr-2">
    <img alt="no Img" src={!status ? Pending : Completed} />
  </div>
);
export function StepperData() {
  const history = useHistory();
  const match = useRouteMatch();
  const { independentOverview, getToolTipData } = useContext(independentCourseContext);
  const [sessionTypesConfigured, courseConfigured] = getIsConfigured(independentOverview);
  const steps = [
    {
      label: t('independentCourse.step1'),
      description: (
        <div className="row bg-white border-blue rounded p-2 mt-2">
          <div className="d-flex col-md-9 col-xl-10 col-lg-10 col-9">
            {showStatusImage(sessionTypesConfigured)}
            <div className="">
              <p className="font-weight-normal pt-1 mb-0 text-left f-16">
                {getSessionData(independentOverview)}
              </p>
              {showStatusMessage(
                { course: getToolTipData.course },
                'independentCourse.configure_session'
              )}
            </div>
          </div>
          {showViewOrConfigure(sessionTypesConfigured, 'session-types', history, match)}
        </div>
      ),
    },
    {
      label: t('independentCourse.step2'),
      description: (
        <div
          className={`row border-blue rounded p-2 ${
            !sessionTypesConfigured ? 'disable-step bg-gray' : 'bg-white'
          }`}
        >
          <div className="d-flex col-md-9 col-xl-10 col-lg-10 col-9">
            {showStatusImage(courseConfigured)}
            <div className="">
              <p className="font-weight-normal pt-1 mb-0 text-left f-16">
                {getCourseData(independentOverview, getToolTipData)}
              </p>
              {showStatusMessage(
                { course: getToolTipData.course },
                'add_colleges.configure_course'
              )}
            </div>
          </div>
          {showViewOrConfigure(courseConfigured, 'course-master-list', history, match)}
        </div>
      ),
    },
  ];
  return <Stepper steps={steps} />;
}
