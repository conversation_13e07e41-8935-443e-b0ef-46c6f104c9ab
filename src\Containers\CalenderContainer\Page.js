import React from 'react';
import PropTypes from 'prop-types';

const Page = ({ children, singleMode, id }) => (
  <div
    id={id}
    className="bg-white shadow-1 center pa4"
    style={{ width: '210mm', position: 'absolute', top: '-560em', height: singleMode ? '' : '' }} //297mm position: "absolute",top: "-560em",
  >
    {children}
  </div>
);

Page.propTypes = {
  singleMode: PropTypes.string,
  id: PropTypes.string,
  children: PropTypes.node,
};

export default Page;
