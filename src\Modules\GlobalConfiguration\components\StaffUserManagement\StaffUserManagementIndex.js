import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { List, Map } from 'immutable';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { withRouter, useHistory } from 'react-router-dom';
import { t } from 'i18next';
import * as actions from '_reduxapi/global_configuration/actions';
import Designation from './Designation';
import DocumentIndex from './Documents/DocumentIndex';
import Biometrics from './Biometrics';
import {
  selectBasicDetails,
  selectStaffManagementDashboard,
} from '_reduxapi/global_configuration/selectors';
import EmailContent from './EmailContent';
import EmailSettings from './EmailSettings';
import LabelFieldConfiguration from './LabelFieldConfiguration';
import { getInstitutionHeader } from 'v2/utils';

export const setAccordionHeader = (heading, subHeading, collapsed, options) => {
  return (
    <div className="d-flex justify-content-between w-100">
      <div className="f-16 bold digi-brown">
        <b>{t(heading)}</b>
        {collapsed && (
          <p className="mb-0 f-14">
            <span>{subHeading}</span>
          </p>
        )}
      </div>
      <div>{!collapsed && options}</div>
    </div>
  );
};
function StaffUserManagement(props) {
  const { globalSettings, getStaffManagementDashboard, dashboard, type, setData } = props;
  const history = useHistory();
  const institutionHeader = getInstitutionHeader(history);
  const [accordionOpen, setAccordionOpen] = useState({});

  const settingId = globalSettings.get('_id', '');

  useEffect(() => {
    settingId && getStaffManagementDashboard({ settingId, headers: institutionHeader, type });
    setData(Map({ designations: List() }));
    setData(Map({ mailConfigurationDetails: List() }));
    setData(Map({ mailSettingDetails: Map() }));
    setData(Map({ labelConfigurationDetails: Map() }));
    setData(Map({ documentConfigurationDetails: Map() }));
    setData(Map({ bioConfigDetails: Map() }));
  }, []); //eslint-disable-line

  const onClickAccordion = (index) => {
    setAccordionOpen({ [index]: !accordionOpen[index] });
  };

  const commonProps = { institutionHeader, settingId, setAccordionHeader, type };

  return (
    <>
      <div>
        <div className="pt-3 pl-3">
          {type === 'staff' && (
            <Designation
              count={dashboard.get('designationConfiguration', 0)}
              accordionOpen={accordionOpen[1]}
              setAccordionOpen={() => onClickAccordion(1)}
              {...commonProps}
            />
          )}

          <EmailContent
            accordionOpen={accordionOpen[3]}
            setAccordionOpen={() => onClickAccordion(3)}
            count={dashboard.get('mailContentConfiguration', 0)}
            {...commonProps}
          />
          <EmailSettings
            accordionOpen={accordionOpen[4]}
            setAccordionOpen={() => onClickAccordion(4)}
            {...commonProps}
          />
          <LabelFieldConfiguration
            accordionOpen={accordionOpen[5]}
            setAccordionOpen={() => onClickAccordion(5)}
            count={dashboard.get('labelFieldConfiguration', 0)}
            {...commonProps}
          />
          <DocumentIndex
            accordionOpen={accordionOpen[6]}
            setAccordionOpen={() => onClickAccordion(6)}
            count={dashboard.get('documentConfiguration', 0)}
            {...commonProps}
          />
          <Biometrics
            accordionOpen={accordionOpen[7]}
            setAccordionOpen={() => onClickAccordion(7)}
            count={dashboard.get('biometricConfiguration', '')}
            {...commonProps}
          />
        </div>
      </div>
    </>
  );
}

StaffUserManagement.propTypes = {
  globalSettings: PropTypes.instanceOf(Map),
  staffManagementDashboard: PropTypes.instanceOf(List),
  getStaffManagementDashboard: PropTypes.func,
  setData: PropTypes.func,
  type: PropTypes.string,
  dashboard: PropTypes.instanceOf(Map),
};

const mapStateToProps = (state) => {
  return {
    globalSettings: selectBasicDetails(state),
    dashboard: selectStaffManagementDashboard(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(StaffUserManagement);
