import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { msalInstance, loginRequest } from 'authConfig';
import useMsTeamsHook from 'Hooks/useMsTeamsHook';
import MSTeams from 'Assets/microsoft-teams.svg';

const MicrosoftTeamsLogin = ({ callback }) => {
  const { initializeMsal } = useMsTeamsHook();

  useEffect(() => {
    initializeMsal(); // Call initialize on component mount
  }, []);

  function handleSignIn() {
    // loginRequest.prompt = 'login';
    loginRequest.prompt = 'select_account';
    msalInstance
      .loginPopup(loginRequest) // Request necessary scopes
      .then((loginResponse) => {
        if (loginResponse) {
          const accessToken = loginResponse.accessToken;
          const localAccountId = loginResponse.account.localAccountId;
          const emailId = loginResponse.account.username;

          const requestData = {
            ssoProvider: 'teams',
            msTeamToken: accessToken,
            userId: localAccountId,
            email: emailId,
          };

          callback(requestData);
        }
      })
      .catch((error) => {
        // Handle sign-in errors
        // console.log('Error during login:', error);
      });
  }

  return (
    <div className="text-center pt-2">
      <div
        onClick={() => handleSignIn()}
        className="login-width d-flex align-items-center justify-content-center btnVia ViaTeams"
      >
        <div>
          <img src={MSTeams} alt="MSTeams" />
        </div>
        <div className="pl-2 f-15">Login via Teams</div>
      </div>
    </div>
  );
};

MicrosoftTeamsLogin.propTypes = {
  callback: PropTypes.func,
};

export default MicrosoftTeamsLogin;
