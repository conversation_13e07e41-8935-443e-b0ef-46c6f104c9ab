import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import useMsTeamsHook from 'Hooks/useMsTeamsHook';
import MSTeams from 'Assets/microsoft-teams.svg';

const MicrosoftTeamsLogin = ({ callback, unifyData }) => {
  const { initializeMsal, msalInstance, loginRequest } = useMsTeamsHook({ unifyData });
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const init = async () => {
      try {
        await initializeMsal();
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize MSAL:', error);
      }
    };
    init();
  }, [initializeMsal]);

  async function handleSignIn() {
    if (!isInitialized || !msalInstance) {
      console.error('MSAL not initialized');
      return;
    }

    try {
      loginRequest.prompt = 'select_account';
      console.log('Attempting login with request:', loginRequest);

      const loginResponse = await msalInstance.loginPopup(loginRequest);
      if (loginResponse) {
        const accessToken = loginResponse.accessToken;
        const localAccountId = loginResponse.account.localAccountId;
        const emailId = loginResponse.account.username;

        const requestData = {
          ssoProvider: 'teams',
          msTeamToken: accessToken,
          userId: localAccountId,
          email: emailId,
        };

        callback(requestData);
      }
    } catch (error) {
      console.error('Error during login:', error);
    }
  }

  return (
    <div className="text-center pt-2">
      <div
        onClick={handleSignIn}
        className="login-width d-flex align-items-center justify-content-center btnVia ViaTeams"
      >
        <div>
          <img src={MSTeams} alt="MSTeams" />
        </div>
        <div className="pl-2 f-15">Login via Teams</div>
      </div>
    </div>
  );
};

MicrosoftTeamsLogin.propTypes = {
  callback: PropTypes.func,
};

export default MicrosoftTeamsLogin;
