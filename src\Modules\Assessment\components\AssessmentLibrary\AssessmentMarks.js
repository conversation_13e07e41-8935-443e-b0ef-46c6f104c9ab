import React, { useEffect, useState } from 'react';
import { useHistory, with<PERSON>outer } from 'react-router-dom';
import PropTypes from 'prop-types';
import { compose } from 'redux';
import { connect } from 'react-redux';
import { List, Map, fromJS } from 'immutable';
import * as actions from '../../../../_reduxapi/assessment/action';
import { MarksHeader } from './Header';
import FilteredHeader from './FilteredHeader';
import AssessmentMarksTable from './AssessmentMarksTable';
import { getURLParams, indVerRename, removeURLParams } from '../../../../utils';
import AddStudent from '../../modals/AddStudent';
import {
  selectAssessmentProgramCourse,
  selectOutComeLists,
  selectStudentAssessmentList,
} from '../../../../_reduxapi/assessment/selector';
import AssessmentDataExport from './AssessmentDataExport';
import { Years } from '../../../../constants';
function AssessmentMarks({
  programId,
  activeInstitutionCalendar,
  programName,
  getAssessmentLearningOutCome,
  outComeLists,
  getAssessmentStudentList,
  studentAssessmentList,
  saveAssessmentMarks,
  getAssessmentProgramCourse,
  setData,
  assessmentProgramCourse,
  publishAssessment,
}) {
  const [AddShow, setAddShow] = useState(false);
  const handleAddShow = () => {
    setAddShow(true);
  };
  const handleAddClose = () => {
    setAddShow(false);
  };
  const [exportDownloadTrue, setExportDownloadTrue] = useState(false);
  const history = useHistory();
  const planName = getURLParams('mName', true);
  const totalMarks = getURLParams('marks', true);
  const type = getURLParams('type', true);
  const institutionCalendarId = activeInstitutionCalendar.get('_id', '');
  const term = getURLParams('term', true);
  const assessmentId = getURLParams('mid', true);
  const courseId = getURLParams('cId', true);
  const level = getURLParams('level', true);
  const rotationCount = getURLParams('rotationCount', true);
  const mode = getURLParams('mode', false);

  const [data, setTotalData] = useState(
    Map({
      curriculumName: '',
      levelNo: List(),
      noQuestions: 0,
      questionOutcome: type === 'program' ? 'PLO' : 'CLO',
      benchMark: 0,
      questionMarks: List(),
      studentDetails: List(),
    })
  );
  useEffect(() => {
    setTotalData(data.set('studentDetails', List()));
  }, []); //eslint-disable-line

  const studentIds = () => {
    let tempArr = List();
    tempArr = data.get('studentDetails', List())?.map((student) => student.get('studentId', ''));
    return tempArr;
  };

  useEffect(() => {
    if (institutionCalendarId !== '') {
      getAssessmentProgramCourse(
        programId,
        institutionCalendarId,
        term,
        type,
        assessmentId,
        courseId,
        level
      );
    }
  }, [institutionCalendarId]); //eslint-disable-line
  const questionOutcome = data.get('questionOutcome', '');

  useEffect(() => {
    if (institutionCalendarId !== '') {
      getAssessmentLearningOutCome(
        programId,
        institutionCalendarId,
        term,
        questionOutcome.toLowerCase(),
        courseId
      );
    }
  }, [getAssessmentLearningOutCome, programId, institutionCalendarId, term, questionOutcome]); //eslint-disable-line
  function getStudentList(lvl, callBack) {
    getAssessmentStudentList(
      programId,
      institutionCalendarId,
      term,
      type === 'program' ? lvl : level,
      type,
      courseId,
      callBack,
      rotationCount !== '' && rotationCount !== 0 ? rotationCount : ''
    );
  }

  useEffect(() => {
    let updatedData = data;
    const preparedData = studentAssessmentList.map((item) => {
      return fromJS({
        studentId: item.get('academic_no', ''),
        name: item.get('name', ''),
        gender: item.get('gender', ''),
        attendance: item.get('attendance', true),
        isDefault: true,
        studentMarks: [],
      });
    });
    updatedData = updatedData.set('studentDetails', fromJS(preparedData));

    setTotalData(updatedData);
  }, [studentAssessmentList]); //eslint-disable-line

  useEffect(() => {
    if (!assessmentProgramCourse.isEmpty()) {
      let updatedData = data;
      const assessment = assessmentProgramCourse;
      const curriculumName = assessment.get('curriculumName', '');
      const levelNo = assessment.get('levelNo', List());
      updatedData = updatedData
        .set('curriculumName', curriculumName !== null ? curriculumName : '')
        .set('levelNo', levelNo !== null ? levelNo : List())
        .set('noQuestions', assessment.get('noQuestions', 0))
        .set('questionOutcome', assessment.get('questionOutcome', data.get('questionOutcome', '')))
        .set('benchMark', assessment.get('benchMark', 0))
        .set('studentDetails', assessment.get('studentDetails', List()))
        .set('questionMarks', assessment.get('questionMarks', List()));

      setTotalData(updatedData);
    }
  }, [assessmentProgramCourse]); //eslint-disable-line

  useEffect(() => {
    return () => {
      setData(Map({ assessmentProgramCourse: Map() }));
    };
  }, []); //eslint-disable-line

  function saveMarks() {
    let errorArray = [];
    data.get('studentDetails', List()).map((student, fIndex) => {
      student.get('studentMarks', List()).map((mark, secIndex) => {
        const value = mark.get('mark', 0) > data.getIn(['questionMarks', secIndex, 'totalMark'], 0);
        if (value) {
          const message = `${mark.get('questionName', '')} values should be less than totalMark`;
          errorArray.push(message);
        }
        return '';
      });

      return '';
    });
    if (errorArray.length) {
      setData(Map({ message: errorArray[0] }));
      errorArray = [];
      return;
    }
    const tId = getURLParams('tId', true);
    const tName = getURLParams('tName', true);
    const stId = getURLParams('stId', true);
    const stName = getURLParams('stName', true);
    const atId = getURLParams('atId', true);
    const atName = getURLParams('atName', true);
    let updatedData = data;
    updatedData = updatedData
      .set('_id', assessmentProgramCourse.get('_id', ''))
      .set('assessmentTypeId', atId)
      .set('assessmentTypeName', atName)
      .set('typeName', tName)
      .set('typeId', tId)
      .set('subTypeName', stName)
      .set('subTypeId', stId)
      .set(
        'studentDetails',
        data
          .get('studentDetails', List())
          .sort((a, b) => a.get('academic_no', '').localeCompare(b.get('academic_no', '')))
      );
    if (type !== 'program') {
      updatedData = updatedData.delete('curriculumName').delete('levelNo');
    }
    saveAssessmentMarks(updatedData.toJS(), () => {
      //changeAssessmentCourse('pending');
      if (checkView(updatedData)) {
        history.goBack(-1);
      } else {
        getAssessmentProgramCourse(
          programId,
          institutionCalendarId,
          term,
          type,
          assessmentId,
          courseId,
          level
        );
        history.push(
          `/assessment-management/assessment_details/configure/marks${removeURLParams(
            history.location,
            ['mode']
          )}&mode=view`
        );
      }
    });
  }

  function checkView(data) {
    return (
      data.get('benchMark', '') === '' ||
      data.get('benchMark', '') === 0 ||
      data.get('studentDetails', List()).size === 0 ||
      data.get('questionMarks', List()).size === 0 ||
      data.get('questionMarks', List()).some((item) => {
        return (
          item.get('attainmentBenchMark', '') === null ||
          item.get('attainmentBenchMark', '') === '' ||
          item.get('totalMark', '') === null ||
          item.get('totalMark', '') === '' ||
          item.get('outComeIds', List()).size === 0
        );
      })
    );
  }

  function changeAssessmentCourse(status) {
    const updatedState = assessmentProgramCourse.set('status', status);
    setData(Map({ assessmentProgramCourse: updatedState }));
  }

  function getOutcomes() {
    const curriculumPLO = outComeLists.get('curriculumPLO', List());
    const curriculumName = data.get('curriculumName', '');
    const noQuestions = data.get('noQuestions', 0);
    if (
      type === 'program' &&
      curriculumPLO.size > 0 &&
      curriculumName !== '' &&
      noQuestions !== 0
    ) {
      return curriculumPLO
        .filter((item) => item.get('curriculumName', '') === curriculumName)
        .map((item) => item.get('plo', List()))
        .get(0, List())
        .filter((item) => item.get('isActive') === true)
        .map((item) => {
          return {
            name: indVerRename('PLO', programId) + ' ' + item.get('no', ''),
            value: item.get('_id', ''),
            mapName: item.get('name', ''),
          };
        })
        .toJS();
    } else if (type === 'course') {
      return outComeLists
        .filter((item) =>
          data.get('questionOutcome', 'CLO') === 'CLO' ? item.get('isActive') === true : item
        )
        .map((item) => {
          const firstLetter = data.get('questionOutcome', 'PLO').substr(0, 1);
          return {
            name: indVerRename(`${firstLetter}LO`, programId) + ' ' + item.get('no', ''),
            value: item.get('_id', ''),
            mapName: item.get('name', ''),
          };
        })
        .toJS();
    }
    return [];
  }
  const globalOutcomeList = getOutcomes();
  const cName = getURLParams('cName', true);
  const programFullName = getURLParams('pname', true);
  const year = getURLParams('year', true);
  const formattedYear = year !== '' ? Years[year.replace('year', '')].name : '';
  const heading =
    type === 'program'
      ? programName
      : `${programFullName} Program / ${formattedYear}, ${level} / ${cName}`;

  const headingForExcel =
    type === 'program'
      ? `${programName} / ${data.get('curriculumName', '')} / ${data.get('levelNo', '').join(', ')}`
      : `${programFullName} Program / ${formattedYear}, ${level} / ${cName}`;

  const NeededDataForExport = {
    programName,
    globalOutcomeList,
    planName,
    totalMarks,
    data: data.toJS(),
    type,
    heading,
  };

  const studentList = data.get('studentDetails', List());
  const defaultCount = 10;

  const [list, setList] = useState(
    Map({
      itemsDisplayed: defaultCount,
      studentData: List(),
      loadOnce: true,
    })
  );

  useEffect(() => {
    if (list.get('loadOnce') && studentList.size > 0) {
      setList(list.set('studentData', studentList.slice(0, defaultCount)).set('loadOnce', false));
    }
  }, [studentList]); //eslint-disable-line

  const checkQuestionMark = () => {
    return data
      .get('questionMarks', List())
      .every(
        (s) =>
          s.getIn(['outComeIds', 0], '') &&
          s.get('attainmentBenchMark', '') &&
          s.get('totalMark', '')
      );
  };

  const publishCallBack = (id, callBack) => {
    publishAssessment(id, () => {
      callBack();
      changeAssessmentCourse('published');
    });
  };
  const assessmentData = Map({
    status: assessmentProgramCourse.get('status', ''),
    _id: assessmentProgramCourse.get('_id', ''),
  });
  return (
    <div className="main pb-5">
      {exportDownloadTrue && AssessmentDataExport({ setExportDownloadTrue, NeededDataForExport })}
      <MarksHeader
        history={history}
        programName={mode === 'edit' ? `${heading} / Enter Mark` : `View ${heading}`}
        isConfigurePages={true}
        saveMarks={saveMarks}
        mode={mode}
        setExportDownloadTrue={setExportDownloadTrue}
        planName={planName}
        publishCallBack={publishCallBack}
        assessmentData={assessmentData}
      />
      <FilteredHeader
        handleAddShow={handleAddShow}
        splittingHeading={headingForExcel}
        planName={planName}
        totalMarks={totalMarks}
        outComeLists={outComeLists}
        data={data}
        setDataError={setData}
        setData={setTotalData}
        handleAddClose={handleAddClose}
        getStudentList={getStudentList}
        assessmentProgramCourse={assessmentProgramCourse}
        type={type}
        term={term}
        institutionCalendarId={institutionCalendarId}
        mode={mode}
        NeededDataForExport={NeededDataForExport}
        list={list}
        setList={setList}
        defaultCount={defaultCount}
        checkQuestionMark={checkQuestionMark()}
      />
      {(type !== 'program'
        ? data.get('noQuestions', 0) >= 1
        : data.get('curriculumName', '') !== '' &&
          data.get('levelNo', '') !== '' &&
          data.get('noQuestions', 0) >= 1) && (
        <AssessmentMarksTable
          globalOutcomeList={globalOutcomeList}
          data={data}
          type={type}
          maxMarks={totalMarks}
          setData={setTotalData}
          toastFunc={setData}
          studentIds={studentIds()}
          getStudentList={getStudentList}
          mode={mode}
          list={list}
          setList={setList}
          defaultCount={defaultCount}
        />
      )}
      {AddShow && (
        <AddStudent
          show={AddShow}
          cancel={handleAddClose}
          data={data}
          setData={setTotalData}
          toastFunc={setData}
          list={list}
          setList={setList}
          defaultCount={defaultCount}
        />
      )}
    </div>
  );
}

AssessmentMarks.propTypes = {
  programId: PropTypes.string,
  programName: PropTypes.string,
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  getAssessmentLearningOutCome: PropTypes.func,
  outComeLists: PropTypes.oneOfType([PropTypes.instanceOf(List), PropTypes.instanceOf(Map)]),
  getAssessmentStudentList: PropTypes.func,
  studentAssessmentList: PropTypes.instanceOf(List),
  saveAssessmentMarks: PropTypes.func,
  setData: PropTypes.func,
  getAssessmentProgramCourse: PropTypes.func,
  assessmentProgramCourse: PropTypes.instanceOf(Map),
  publishAssessment: PropTypes.func,
};

const mapStateToProps = function (state) {
  return {
    outComeLists: selectOutComeLists(state),
    studentAssessmentList: selectStudentAssessmentList(state),
    assessmentProgramCourse: selectAssessmentProgramCourse(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(AssessmentMarks);
