import React, { Fragment, lazy, Suspense, useEffect, useMemo, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  IconButton,
  InputAdornment,
  ListItemText,
  ListSubheader,
  MenuItem,
  Popover,
  Select,
  Switch,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
// import CloseIcon from '@mui/icons-material/Close';
import { fromJS, List, Map } from 'immutable';
import MaterialInput from 'Widgets/FormElements/material/Input';
import AddIcon from '@mui/icons-material/Add';
import { styled } from '@mui/material/styles';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectDeliveryTypes,
  selectGeneratedGroupName,
  selectProgramYearLevel,
} from '_reduxapi/studentGroupV2/selectors';
import {
  getDeliveryTypes,
  getGeneratedGroupName,
  resetMessage,
  setData,
} from '_reduxapi/studentGroupV2/action';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';
import { formatToTwoDigitString, getVersionName, isGenderMerge, levelRename } from 'utils';
import { constructLevelData, useCallGroupSettings } from '../Dashboard';
import ErrorIcon from '@mui/icons-material/Error';
import { getShortString } from 'Modules/Shared/v2/Configurations';
import { getImportedList } from './ImportStudentsModal';
import SearchInput from '../SearchInput';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';

const ImportConfirmModal = lazy(() => import('./ImportConfirmModal'));

const titleSx = {
  padding: '12px 16px',
  color: '#374151',
  borderTop: '4px solid #0064C8',
};
const subTitleSx = {
  marginLeft: '8px',
  padding: '4px 8px',
  fontSize: '14px',
  borderRadius: '4px',
  backgroundColor: '#F3F4F6',
};
const addIconSx = {
  padding: '8px',
  backgroundColor: '#EFF9FB',
  borderRadius: '50%',
  marginRight: '16px',
  color: '#0064C8',
};
// const closeIconSx = {
//   position: 'absolute',
//   right: 8,
//   top: 16,
// };
const noteSx = {
  marginBottom: '16px',
  padding: '8px',
  fontSize: '14px',
  backgroundColor: '#F3F4F6',
  border: '1px solid #D1D5DB',
  borderRadius: '8px',
};
const genderButtonSx = {
  padding: '8px 24px',
  border: '1px solid #D1D5DB !important',
  textTransform: 'none',
  borderRadius: '100px !important',
  color: '#4B5563',
  fontWeight: 'normal',
  '&.Mui-selected': {
    color: '#0064C8',
    backgroundColor: '#F7FCFD',
    fontWeight: 500,
    borderColor: '#0064C8 !important',
    '&:hover': {
      backgroundColor: '#F7FCFD',
    },
  },
  '&:not(:last-of-type)': {
    marginRight: '8px',
  },
};
const checkboxSx = {
  padding: 0,
  marginRight: '8px',
  color: '#6B7280',
};
const deliveryGroupNameSx = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '7px 12px',
  fontSize: 12,
  backgroundColor: '#F9FAFB',
};
const subheaderSx = {
  fontSize: 14,
  lineHeight: '32px',
  color: '#9CA3AF',
};
const inputSx = {
  '& .MuiOutlinedInput-root': {
    fontSize: '14px',
  },
};
const menuItemSx = {
  backgroundColor: '#F3F4F6',
};
const errorIconSx = {
  fontSize: 16,
};
const validationErrorSx = {
  display: 'flex',
  alignItems: 'center',
  gap: '2px',
  color: '#DC2626',
  fontSize: 12,
};
const buttonSx = {
  minWidth: 120,
  minHeight: 40,
};
const courseSelectBtnSx = {
  paddingRight: 0,
  textTransform: 'none !important',
  color: 'rgba(0, 0, 0, 0.87)',
  borderColor: 'rgba(0, 0, 0, 0.23)',
  '&:hover': {
    borderColor: 'rgba(0, 0, 0, 0.87)',
    backgroundColor: 'transparent',
  },
  '& .MuiSvgIcon-root': {
    color: 'rgba(0, 0, 0, 0.54)',
  },
  '&.Mui-disabled': {
    color: 'rgba(0, 0, 0, 0.38) !important',
    '& .MuiSvgIcon-root': {
      color: 'rgba(0, 0, 0, 0.38)',
    },
  },
};
const menuProps = {
  PaperProps: {
    sx: {
      maxHeight: 250,
    },
  },
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'left',
  },
  transformOrigin: {
    vertical: 'top',
    horizontal: 'left',
  },
};
const yearPaperProps = {
  sx: {
    width: 160,
  },
};

const MaterialUISwitch = styled(Switch)(() => ({
  width: 51,
  height: 31,
  padding: '6px 3px',
  '& .MuiSwitch-switchBase': {
    padding: 0,
    '&.Mui-checked': {
      color: '#fff',
      transform: 'translateX(21px)',
      '& .MuiSwitch-thumb': {
        border: '1px solid #0064C8',
      },
      '& + .MuiSwitch-track': {
        opacity: 1,
        backgroundColor: '#0064C8',
      },
      '&.Mui-disabled': {
        color: '#fff',
        '& .MuiSwitch-thumb': {
          borderColor: 'rgb(167, 202, 237)',
        },
      },
    },
    '&.Mui-disabled': {
      '& + .MuiSwitch-track': {
        opacity: 0.5,
      },
    },
    '&.Mui-focusVisible .MuiSwitch-thumb': {
      boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.5)',
    },
  },
  '& .MuiSwitch-thumb': {
    margin: '3px',
    width: 24,
    height: 24,
    transition: 'all 0.2s ease',
  },
  '& .MuiSwitch-track': {
    opacity: 1,
    backgroundColor: '#D1D5DB',
    borderRadius: '200px',
  },
}));

const isGrouped = false;
const maxNoOfGroups = 99;

export const getModalSubtitle = (data) => {
  const programId = data.get('programId', '');
  const year = data.get('year', '');
  const selectedType = data.get('type', '');

  return selectedType === 'year'
    ? year.replace('year', 'Year ')
    : selectedType === 'level'
    ? levelRename(data.getIn(['selectedLevel', 'level_no'], ''), programId)
    : data.getIn(['selectedCourse', 'courses_number'], '');
};

const constructLevelWiseCourses = (item, programId, data) => {
  const type = data.get('type');
  const year = data.get('year');
  const level = data.getIn(['selectedLevel', 'level_no']);
  const courses = item.get('course', List()).map((c) => {
    const hasHierarchySettings = c.get('hasHierarchySettings');
    const settingsFrom = c.get('settingsFrom', Map());
    const isDisabled =
      hasHierarchySettings &&
      (settingsFrom.get('type') !== type ||
        (settingsFrom.get('type') === 'year'
          ? settingsFrom.get('year') !== year
          : settingsFrom.get('level') !== level));
    return Map({
      label: `${c.get('courses_number', '')} - ${c.get('courses_name', '')}${getVersionName(c)}`,
      value: c.get('_course_id', ''),
      isDisabled: isDisabled || c.get('courseNumberOfGroup', 0) > 0,
    });
  });
  return Map({ levelName: levelRename(item.get('level_no', ''), programId), courses });
};

const constructDeliveryTypes = ({ sessions, autoGenerate, generatedGroupName, isEdit }) => {
  return sessions.entrySeq().reduce((newList, [gender, groups]) => {
    let deliveryTypes = List();
    groups.forEach((group) => {
      const typeName = group.get('type_name', '');
      const selectedType = group
        .get('delivery_type', List())
        .filter((type) => !type.get('isFromHierarchy') && type.get('checked'))
        .map((type) => {
          return Map({
            deliveryType: type.get('delivery_type', ''),
            deliverySymbol: type.get('delivery_symbol', ''),
            noOfGroups: type.get('noOfGroups', ''),
            groupName: autoGenerate ? generatedGroupName : type.get('groupName', ''),
            ...(isEdit && { isGrouped: type.get('isGrouped', false) }),
          });
        });
      if (selectedType.size) deliveryTypes = deliveryTypes.push(Map({ typeName, selectedType }));
    });

    return deliveryTypes.size ? newList.push(Map({ gender, deliveryTypes })) : newList;
  }, List());
};

const updateDeliveryTypes = (deliveryTypes, settings) => {
  const settingObj = settings.reduce((newObj, setting) => {
    const gender = setting.get('gender', '');
    setting.get('deliveryTypes', List()).forEach((type) => {
      const typeName = type.get('typeName', '');
      type.get('selectedType', List()).forEach((subType) => {
        const symbol = subType.get('deliverySymbol', '');
        newObj = newObj.setIn([gender, typeName, symbol], subType);
      }, Map());
    });

    return newObj;
  }, Map());

  return deliveryTypes.map((groups, gender) => {
    return groups.map((group, typeName) => {
      return group.update('delivery_type', List(), (types) =>
        types.map((type) => {
          const symbol = type.get('delivery_symbol', '');
          // const hierarchyData = hierarchySettings.getIn([gender, symbol]);
          // if (hierarchyData) {
          //   const noOfGroups = hierarchyData.get('noOfGroups', '');
          //   const groupName = hierarchyData.get('groupName', '');
          //   const isLowerHierarchy = hierarchyData.get('isLowerHierarchy', false);
          //   return type.merge(
          //     Map({
          //       checked: true,
          //       isDisabled: true,
          //       isFromHierarchy: true,
          //       isLowerHierarchy,
          //       noOfGroups,
          //       groupName,
          //     })
          //   );
          // }

          const settingData = settingObj.getIn([gender, typeName, symbol]);
          if (!settingData) return type;

          const noOfGroups = settingData.get('noOfGroups', '');
          const groupName = settingData.get('groupName', '');
          const isGrouped = settingData.get('isGrouped', false);
          return type.merge(
            Map({
              checked: true,
              isDisabled: isGrouped,
              noOfGroups,
              groupName,
              isGrouped,
              noOfGroupsBackup: noOfGroups,
            })
          );
        })
      );
    });
  });
};

const validateDeliveryGroups = ({ requestBody, upcomingYears, isGenderSegregationEnabled }) => {
  if (!requestBody.get('courseIds', List()).size) return 'Course is required';

  const deliveryGroups = requestBody.get('deliveryGroups', List());
  if (isGenderSegregationEnabled && deliveryGroups.size < 2)
    return 'Settings for each gender is required';
  else if (!deliveryGroups.size) return 'Delivery type is required';

  if (upcomingYears && !requestBody.get('upcomingYears', List()).size)
    return 'Upcoming years is required';

  return '';
};

const validateDeliveryTypes = ({ deliveryTypes, autoGenerate }) => {
  let hasError = false;
  const updatedData = deliveryTypes.map((groups) => {
    return groups.map((group) => {
      return group.update('delivery_type', List(), (types) =>
        types.map((type) => {
          if (!type.get('checked')) return type;

          const noOfGroups = Number(type.get('noOfGroups', ''));
          const noOfGroupsBackup = type.get('noOfGroupsBackup', 0);
          const isEmptyGroupName = !(autoGenerate || type.get('groupName', ''));
          let noOfGroupsErrorMsg = '';

          if (!noOfGroups) noOfGroupsErrorMsg = 'No of groups missing!';
          else if (type.get('isGrouped') && noOfGroups < noOfGroupsBackup)
            noOfGroupsErrorMsg = `No of groups should not be less than ${noOfGroupsBackup} since already grouped`;

          if (noOfGroupsErrorMsg || isEmptyGroupName) hasError = true;
          return type.merge(Map({ noOfGroupsErrorMsg, isEmptyGroupName }));
        })
      );
    });
  });

  return [hasError, updatedData];
};

const constructGenderWiseGroups = (settings) => {
  return settings.get('deliveryGroups', List()).reduce((acc, group) => {
    const gender = group.get('gender', '');
    group.get('deliveryTypes', List()).forEach((deliveryType) => {
      deliveryType.get('selectedType', List()).forEach((type) => {
        const symbol = type.get('deliverySymbol', '');
        acc = acc.setIn([gender, symbol], type);
      });
    });

    return acc;
  }, Map());
};

const getUpdatedSettings = (settings, hierarchySettings = Map(), isLowerHierarchy = false) => {
  const genderGroups = constructGenderWiseGroups(settings);
  return genderGroups.reduce((acc, types, gender) => {
    types.forEach((item, symbol) => {
      acc = acc.setIn([gender, symbol], item.set('isLowerHierarchy', isLowerHierarchy));
    });

    return acc;
  }, hierarchySettings);
};

const getCourseSettings = (level, settings, hierarchySettings) => {
  const levelNo = level.get('level_no', '');
  const courses = level.get('course', List());

  return courses.reduce((acc, course) => {
    const courseId = course.get('_course_id', '');
    const courseSettings = settings.getIn(['courses', `${levelNo}+${courseId}`], Map());
    return getUpdatedSettings(courseSettings, acc, true);
  }, hierarchySettings);
};

export const getHierarchySettings = (selectedData, settings) => {
  const selectedType = selectedData.get('type', '');
  const yearSettings = settings.get('settings', Map());

  if (selectedType === 'year') {
    const levels = selectedData.get('level', List());
    return levels.reduce((acc, level) => {
      const levelNo = level.get('level_no', '');
      const levelSettings = settings.getIn(['levels', levelNo], Map());
      acc = getUpdatedSettings(levelSettings, acc, true);
      return getCourseSettings(level, settings, acc);
    }, Map());
  }

  if (selectedType === 'level') {
    const level = selectedData.get('selectedLevel', Map());
    const hierarchySettings = getUpdatedSettings(yearSettings);
    return getCourseSettings(level, settings, hierarchySettings);
  }

  const levelNo = selectedData.getIn(['selectedLevel', 'level_no'], '');
  const levelSettings = settings.getIn(['levels', levelNo], Map());
  const hierarchySettings = getUpdatedSettings(yearSettings);
  return getUpdatedSettings(levelSettings, hierarchySettings);
};

const isDecrementDisabled = (type) => {
  const noOfGroups = Number(type.get('noOfGroups', ''));
  const noOfGroupsBackup = type.get('noOfGroupsBackup', 0);
  return type.get('isGrouped') && noOfGroups <= noOfGroupsBackup;
};

const updateSettings = (data, yearSettings, courseIds) => {
  const selectedType = data.get('type', '');
  if (selectedType === 'course') return yearSettings;

  if (selectedType === 'level') {
    const levelNo = data.getIn(['selectedLevel', 'level_no'], '');
    return yearSettings.setIn(['levels', levelNo, 'courseIds'], courseIds);
  }

  return yearSettings.setIn(['settings', 'courseIds'], courseIds);
};

const EmptyTD = () => {
  return <div className="d-flex align-items-center h-100 f-18 bold">-</div>;
};

const ArrowDropUp = () => {
  return (
    <svg width="8" height="7" viewBox="0 0 8 7" fill="currentColor">
      <path d="M7.75 6.35205H0.25L4 0.352051L7.75 6.35205Z" />
    </svg>
  );
};

const ArrowDropDown = () => {
  return (
    <svg width="8" height="7" viewBox="0 0 8 7" fill="currentColor">
      <path d="M7.75 0.352051H0.25L4 6.35205L7.75 0.352051Z" />
    </svg>
  );
};

export const NumberInput = ({ value, changed, disabled, error, isDecrementDisabled, max }) => {
  const isMaxReached = () => {
    return typeof value === 'number' && typeof max === 'number' && value === max;
  };

  const handleChange = (event) => {
    const val = Number(event.target.value);
    if (!val) return changed('');
    if (!isNaN(val) && val > 0 && (typeof max !== 'number' || val <= max)) changed(val);
  };

  const handleIncrement = () => {
    const newValue = value ? parseInt(value, 10) + 1 : 1;
    changed(newValue);
  };

  const handleDecrement = () => {
    const newValue = Number.isInteger(value) ? parseInt(value, 10) - 1 : parseInt(value, 10);
    changed(newValue);
  };

  // const formatValue = (val) => (val === '' ? '' : val.toString().padStart(2, '0'));

  return (
    <TextField
      size="small"
      // value={formatValue(value)}
      value={value}
      onChange={handleChange}
      type="number"
      inputProps={{
        style: {
          textAlign: 'center',
          // fontSize: '14px',
          // padding: '8px',
        },
      }}
      sx={{
        width: '100px',
        '& .MuiInputBase-root': {
          paddingRight: '8px',
        },
      }}
      InputProps={{
        ...(!disabled && {
          endAdornment: (
            <InputAdornment position="end" sx={{ m: 0 }}>
              <Box display="flex" flexDirection="column" alignItems="center">
                <IconButton
                  sx={{ p: '2px', color: '#374151' }}
                  onClick={handleIncrement}
                  disabled={isMaxReached()}
                >
                  <ArrowDropUp />
                </IconButton>
                <IconButton
                  sx={{ p: '2px', color: '#374151' }}
                  onClick={handleDecrement}
                  disabled={!value || isDecrementDisabled}
                >
                  <ArrowDropDown />
                </IconButton>
              </Box>
            </InputAdornment>
          ),
        }),
      }}
      error={error}
      disabled={disabled}
    />
  );
};
NumberInput.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  changed: PropTypes.func,
  disabled: PropTypes.bool,
  error: PropTypes.bool,
  isDecrementDisabled: PropTypes.bool,
  max: PropTypes.number,
};

const CourseSelect = ({ allCourseIds, options, value, handleChange, isDisabled }) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [search, setSearch] = useState('');
  const [paperWidth, setPaperWidth] = useState(null);
  const open = Boolean(anchorEl);
  const id = open ? 'simple-popover' : undefined;

  useEffect(() => {
    if (!open) setSearch('');
    else setPaperWidth(anchorEl.offsetWidth);
  }, [open]);

  const isAllChecked = useMemo(() => {
    if (!value.length) return false;
    return options.every((lvl) =>
      lvl.get('courses', List()).every((c) => value.includes(c.get('value')))
    );
  }, [value]);

  const filteredOptions = useMemo(() => {
    if (!search) return options;

    const searchValue = search.toLowerCase();
    return options
      .map((option) => {
        return option.update('courses', (courses) =>
          courses.filter((course) => course.get('label', '').toLowerCase().includes(searchValue))
        );
      })
      .filter((option) => option.get('courses', List()).size);
  }, [options, search]);

  const handleClick = (e) => setAnchorEl(e.currentTarget);

  const handleClose = () => setAnchorEl(null);

  const onChange = (val) => {
    if (val === 'all') return handleChange(isAllChecked ? [] : allCourseIds);

    const isSelected = value.includes(val);
    const newValue = isSelected ? value.filter((id) => id !== val) : [...value, val];
    handleChange(newValue);
  };

  return (
    <>
      <MButton
        aria-describedby={id}
        variant="outlined"
        clicked={handleClick}
        disabled={isDisabled}
        sx={courseSelectBtnSx}
        fullWidth
      >
        <div className="d-flex align-items-center justify-content-between w-100">
          <span className="f-16 fw-400">
            {value.length === 0
              ? 'Select'
              : `(${formatToTwoDigitString(value.length)}) ${
                  isAllChecked ? 'All courses selected' : 'courses selected'
                }`}
          </span>
          {open ? <ArrowDropUpIcon /> : <ArrowDropDownIcon />}
        </div>
      </MButton>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        slotProps={{ paper: { sx: { pb: '8px', maxHeight: 250, width: paperWidth } } }}
      >
        <ListSubheader sx={{ padding: '16px 8px 0' }}>
          <SearchInput placeholder="Search course" value={search} changed={setSearch} />
        </ListSubheader>
        <MenuItem sx={menuItemSx} className="py-2" value="all" onClick={() => onChange('all')}>
          <Checkbox
            sx={checkboxSx}
            checked={isAllChecked}
            indeterminate={value.length > 0 && !isAllChecked}
          />
          <ListItemText primary="All" />
        </MenuItem>
        {filteredOptions.map((option, index) => [
          <ListSubheader key={index} sx={subheaderSx} disableSticky>
            {option.get('levelName', '')}
          </ListSubheader>,
          option.get('courses', List()).size ? (
            option.get('courses', List()).map((course, cIndex) => (
              <MenuItem
                key={`${index}-${cIndex}`}
                className="py-2"
                value={course.get('value', '')}
                onClick={() => onChange(course.get('value', ''))}
                disabled={course.get('isDisabled', false)}
              >
                <Checkbox sx={checkboxSx} checked={value.includes(course.get('value'))} />
                <ListItemText primary={getShortString(course.get('label', ''), 40)} />
              </MenuItem>
            ))
          ) : (
            <ListSubheader sx={subheaderSx} disableSticky>
              No courses found
            </ListSubheader>
          ),
        ])}
      </Popover>
    </>
  );
};
CourseSelect.propTypes = {
  allCourseIds: PropTypes.array,
  options: PropTypes.instanceOf(List),
  value: PropTypes.array,
  handleChange: PropTypes.func,
  isDisabled: PropTypes.bool,
};

const YearSelect = ({ options, value, handleChange, disabled }) => {
  const allYears = useMemo(() => {
    return options.map((option) => option.get('value', '')).toJS();
  }, [options]);

  const isAllChecked = useMemo(() => {
    if (!value.length) return false;
    return allYears.every((year) => value.includes(year));
  }, [value]);

  const onChange = (e) => {
    const value = e.target.value;
    if (value.includes('all')) return handleChange(isAllChecked ? [] : allYears);
    handleChange(value);
  };

  return (
    <FormControl fullWidth>
      <Select
        size="small"
        value={value}
        onChange={onChange}
        renderValue={(selected) => {
          if (!selected.length) return '- Choose Year -';
          return 'Year ' + selected.map((year) => year.replace('year', '')).join(', ');
        }}
        displayEmpty
        multiple
        disabled={disabled}
        MenuProps={{ ...menuProps, PaperProps: yearPaperProps }}
      >
        <MenuItem sx={menuItemSx} className="py-2" value="all">
          <Checkbox
            sx={checkboxSx}
            checked={isAllChecked}
            indeterminate={value.length > 0 && !isAllChecked}
          />
          <ListItemText primary="All" />
        </MenuItem>
        {options.map((option, index) => (
          <MenuItem key={index} className="py-2" value={option.get('value', '')}>
            <Checkbox sx={checkboxSx} checked={value.includes(option.get('value'))} />
            <ListItemText primary={option.get('label', '')} />
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};
YearSelect.propTypes = {
  options: PropTypes.instanceOf(List),
  value: PropTypes.array,
  handleChange: PropTypes.func,
  disabled: PropTypes.bool,
};

export const ValidationError = ({ show, message }) => {
  if (!show) return null;
  return (
    <Box sx={validationErrorSx}>
      <ErrorIcon sx={errorIconSx} />
      <span>{message}</span>
    </Box>
  );
};

const DeliveryGroupsModal = ({ open, data, handleClose, handleSave, hasSettings }) => {
  const dispatch = useDispatch();
  const activeInstitutionCalendar = useSelector(selectActiveInstitutionCalendar);
  const programYearLevel = useSelector(selectProgramYearLevel);
  const deliveryTypesList = useSelector(selectDeliveryTypes);
  const generatedGroupName = useSelector(selectGeneratedGroupName);
  const { yearSettings, groupSettings, callGroupSettings } = useCallGroupSettings({
    selectedData: data,
  });
  const [show, setShow] = useState(open);
  const [modalState, setModalState] = useState(Map({ gender: 'male', autoGenerate: true }));
  const [deliveryTypes, setDeliveryTypes] = useState(Map());
  const [upcomingYears, setUpcomingYears] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(Map());
  const isAutoGenerateCalled = useRef(false);
  const institutionCalendarId = activeInstitutionCalendar.get('_id', '');
  const programId = data.get('programId', '');
  const term = data.get('term', '');
  const year = data.get('year', '');
  const selectedType = data.get('type', '');
  const gender = modalState.get('gender', '');
  const autoGenerate = modalState.get('autoGenerate', false);
  const isPublished = groupSettings.get('isPublished', false);
  const genderDeliveryTypes = deliveryTypes.get(gender, Map());

  useEffect(() => {
    const level = data.getIn(['selectedLevel', 'level_no']);
    const courseId = data.getIn(['selectedCourse', '_course_id']);
    const params = {
      institutionCalendarId,
      programId,
      term,
      year,
      ...(level && { level }),
      ...(courseId && { courseId }),
    };
    dispatch(getDeliveryTypes(params));

    return () => dispatch(setData(fromJS({ deliveryTypes: [], generatedGroupName: '' })));
  }, []);

  useEffect(() => {
    if (!deliveryTypesList.size) return;
    if (yearSettings.isEmpty()) callGroupSettings();

    const types = deliveryTypesList.reduce((newObj, item) => {
      item.get('credit_hours', List()).forEach((c) => {
        const creditHours = c.update('delivery_type', List(), (prev) =>
          prev.map((type) => type.set('checked', true))
        );
        newObj = newObj.set(c.get('type_name'), creditHours);
      });
      return newObj;
    }, Map());
    const typesObj = isGenderSegregationEnabled ? { male: types, female: types } : { both: types };
    setModalState((prev) => prev.set('gender', isGenderSegregationEnabled ? 'male' : 'both'));
    setDeliveryTypes(Map(typesObj));
  }, [deliveryTypesList]);

  useEffect(() => {
    if (!deliveryTypes.size || modalState.get('isLoaded')) return;
    if (yearSettings.isEmpty()) return;

    // const hierarchySettings = getHierarchySettings(data, yearSettings);
    const courseIds = groupSettings.get('courseIds', List());
    const deliveryGroups = groupSettings.get('deliveryGroups', List());
    const upcomingYears = groupSettings.get('upcomingYears', List());
    const autoGenerate = groupSettings.get('autoGenerate', false);
    setModalState((prev) =>
      prev.merge(
        Map({
          isLoaded: true,
          ...(courseIds.size && { courseIds }),
          upcomingYears,
          ...(deliveryGroups.size && { autoGenerate }),
        })
      )
    );
    setDeliveryTypes(updateDeliveryTypes(deliveryTypes, deliveryGroups));
    setUpcomingYears(upcomingYears.size > 0);
  }, [yearSettings, groupSettings, deliveryTypes]);

  const isGenderSegregationEnabled = useMemo(() => !isGenderMerge(), []);

  const modalSubTile = useMemo(() => getModalSubtitle(data), []);

  const courseOptions = useMemo(() => {
    if (selectedType === 'course') return List();
    return selectedType === 'year'
      ? data.get('level', List()).map((item) => {
          const level = constructLevelData(item, yearSettings);
          return constructLevelWiseCourses(level, programId, data);
        })
      : List([constructLevelWiseCourses(data.get('selectedLevel', Map()), programId, data)]);
  }, [data, yearSettings]);

  const allCourseIds = useMemo(() => {
    const courseIds = courseOptions
      .flatMap((option) =>
        option
          .get('courses', List())
          .filter((c) => !c.get('isDisabled', false))
          .map((c) => c.get('value', ''))
      )
      .toSet()
      .toJS();
    return courseIds;
  }, [courseOptions]);

  useEffect(() => {
    if (!modalState.get('courseIds', List()).size)
      setModalState((prev) => prev.set('courseIds', fromJS(allCourseIds)));
  }, [allCourseIds]);

  useEffect(() => {
    const courseIds =
      selectedType === 'course' ? [data.getIn(['selectedCourse', '_course_id'], '')] : allCourseIds;
    if (!courseIds.length || !autoGenerate || generatedGroupName || isAutoGenerateCalled.current)
      return;

    const level = data.getIn(['selectedLevel', 'level_no']);
    const params = {
      institutionCalendarId,
      programId,
      term,
      // selectedType,
      year,
      ...(level && { level }),
      courseIds,
    };
    dispatch(getGeneratedGroupName(params));
    isAutoGenerateCalled.current = true;
  }, [autoGenerate, allCourseIds]);

  const yearOptions = useMemo(() => {
    if (selectedType !== 'year') return List();

    const selectedYear = parseInt(year.replace('year', ''), 10);
    return programYearLevel
      .filter((item) => parseInt(item.get('year', '').replace('year', ''), 10) > selectedYear)
      .map((item) =>
        Map({
          label: item.get('year', '').replace('year', 'Year '),
          value: item.get('year', ''),
        })
      );
  }, [programYearLevel]);

  const { isAllChecked, isIndeterminate, isAllDisabled } = useMemo(() => {
    const types = deliveryTypes.get(gender, Map()).reduce((newObj, group) => {
      group.get('delivery_type', List()).map((type) => (newObj = newObj.push(type)));
      return newObj;
    }, List());

    const isAllChecked = types.every((type) => type.get('checked'));
    const isSelected = types.some((type) => type.get('checked'));
    // const isAllDisabled = types.every((type) => type.get('isDisabled'));
    return {
      isAllChecked: types.size > 0 && isAllChecked,
      isIndeterminate: !isAllChecked && isSelected,
      isAllDisabled: true,
    };
  }, [deliveryTypes, gender]);

  const handleChange = (key) => (e) => {
    const isAutoGenerate = key === 'autoGenerate';
    const value = ['courseIds', 'upcomingYears'].includes(key)
      ? fromJS(e)
      : isAutoGenerate
      ? e.target.checked
      : e.target.value;
    setModalState((prev) => prev.set(key, value));
    if (isAutoGenerate) clearGroupNames();
  };

  const clearGroupNames = () => {
    setDeliveryTypes((prev) =>
      prev.map((groups) => {
        return groups.map((group) => {
          return group.update('delivery_type', List(), (types) => {
            return types.map((type) => {
              if (type.get('isDisabled')) return type;
              return type.set('groupName', '');
            });
          });
        });
      })
    );
  };

  const handleDeliveryTypes = (key, group, deliveryTypeIndex) => (e) => {
    if (group === 'all') {
      return setDeliveryTypes((prev) =>
        prev.update(gender, Map(), (types) =>
          types.map((group) =>
            group.update('delivery_type', List(), (types) =>
              types.map((type) => {
                if (type.get('isDisabled')) return type;
                return type.set(key, !isAllChecked);
              })
            )
          )
        )
      );
    }

    const typeName = group.get('type_name', '');
    const updateKey = [gender, typeName, 'delivery_type', deliveryTypeIndex];
    let value = key === 'noOfGroups' ? e : e.target.value;
    if (key === 'noOfGroups' && value !== '' && !Number.isInteger(value)) return;

    if (key === 'checked')
      return setDeliveryTypes((prev) =>
        prev.updateIn([...updateKey, key], false, (checked) => !checked)
      );

    const errorKey = key === 'noOfGroups' ? 'noOfGroupsErrorMsg' : 'isEmptyGroupName';
    setDeliveryTypes((prev) =>
      prev.setIn([...updateKey, key], value).setIn([...updateKey, errorKey], false)
    );
  };

  const constructGroupName = (type) => {
    const noOfGroups = Number(type.get('noOfGroups', ''));
    if (!(generatedGroupName && noOfGroups)) return '';

    const genderSymbol = isGenderSegregationEnabled ? gender.charAt(0).toUpperCase() : 'MX';
    const deliverySymbol = type.get('delivery_symbol', '');
    const groupName = `${generatedGroupName}-${genderSymbol}-${deliverySymbol}-1`;
    return noOfGroups > 1
      ? `${groupName} to ${genderSymbol}-${deliverySymbol}-${noOfGroups}`
      : groupName;
  };

  const handleClickSave = (isConfirmed = false) => {
    const [hasDeliveryTypeError, validatedData] = validateDeliveryTypes({
      deliveryTypes,
      autoGenerate,
    });
    if (hasDeliveryTypeError) return setDeliveryTypes(validatedData);

    const groupSettingId = groupSettings.get('_id', '');
    const level = data.getIn(['selectedLevel', 'level_no']);
    const courseIds =
      selectedType === 'course'
        ? List([data.getIn(['selectedCourse', '_course_id'])])
        : modalState.get('courseIds', List());
    const importedList = getImportedList(data, updateSettings(data, yearSettings, courseIds));
    const levelSettingIds = importedList.get('levels', List()).map((level) => level.get('id'));
    const courseSettingIds = importedList.get('courses', List()).map((course) => course.get('id'));
    const removedImportedGroupingIds = levelSettingIds.merge(courseSettingIds);
    const requestBody = Map({
      ...(groupSettingId ? { groupSettingId } : { institutionCalendarId, programId, term }),
      selectedType,
      year,
      ...(level && { level }),
      courseIds,
      deliveryGroups: constructDeliveryTypes({
        sessions: deliveryTypes,
        autoGenerate,
        generatedGroupName,
        isEdit: groupSettingId !== '',
      }),
      autoGenerate,
      ...(selectedType === 'year' && {
        upcomingYears: upcomingYears ? modalState.get('upcomingYears', List()) : List(),
      }),
      ...(removedImportedGroupingIds.size && { removedImportedGroupingIds }),
    });

    const errorMsg = validateDeliveryGroups({
      requestBody,
      upcomingYears,
      isGenderSegregationEnabled,
    });
    if (errorMsg) return dispatch(resetMessage(errorMsg));

    if (importedList.size && !isConfirmed) {
      setShow(false);
      return setShowConfirmModal(Map({ show: true, importedList }));
    }

    handleSave(requestBody, handleClose);
  };

  return (
    <>
      <Dialog open={show} maxWidth="md" fullWidth>
        <DialogTitle className="border-bottom" sx={titleSx}>
          <Box display="flex" alignItems="center">
            <Box display="flex" sx={addIconSx}>
              <AddIcon />
            </Box>
            Define Student Groups per Delivery
            <Typography sx={subTitleSx}>{modalSubTile}</Typography>
          </Box>
        </DialogTitle>
        {/* <IconButton aria-label="close" onClick={handleClose} sx={closeIconSx}>
          <CloseIcon />
        </IconButton> */}
        <DialogContent className="p-3">
          {isGrouped ? (
            <p className="mb-3 bold">
              <span className="text-danger">*</span>Editing disabled as some students are already
              grouped to delivery groups!
            </p>
          ) : (
            <Typography sx={noteSx}>
              <span className="text-danger">*</span>
              <span className="bold">Note:</span> Once students are grouped into any delivery group,
              this setting cannot be edited. Please set the no. of groups carefully.
            </Typography>
          )}

          {selectedType !== 'course' && (
            <div className="row mb-3">
              <div className="col-6">
                <p className="f-14 text-light-grey">Applicable Courses</p>
                <CourseSelect
                  allCourseIds={allCourseIds}
                  options={courseOptions}
                  value={modalState.get('courseIds', List()).toJS()}
                  handleChange={handleChange('courseIds')}
                  isDisabled={isPublished}
                />
              </div>
            </div>
          )}

          {isGenderSegregationEnabled ? (
            <Box display="flex" alignItems="center" gap={1}>
              <p className="f-14 gray-neutral">Applying for:</p>
              <ToggleButtonGroup
                value={gender}
                exclusive
                onChange={handleChange('gender')}
                aria-label="gender"
              >
                <ToggleButton value="male" sx={genderButtonSx}>
                  Male
                </ToggleButton>
                <ToggleButton value="female" sx={genderButtonSx}>
                  Female
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>
          ) : (
            <p className="f-14 gray-neutral">
              Applying for <span className="bold">Mixed Gender:</span>
            </p>
          )}
          <div className="table-responsive border border-radious-8 mt-3">
            <table className="table delivery-groups-table">
              <thead>
                <tr>
                  <th width="35%">
                    <div className="d-flex align-items-center">
                      {false && (
                        <Checkbox
                          sx={checkboxSx}
                          checked={isAllChecked}
                          indeterminate={isIndeterminate}
                          onChange={handleDeliveryTypes('checked', 'all')}
                          disabled={isPublished || isAllDisabled}
                        />
                      )}
                      Delivery types
                    </div>
                  </th>
                  <th className="width_180px">
                    No. of
                    <br />
                    <span className="text-capitalize">
                      {isGenderSegregationEnabled ? gender : 'Mixed'}
                    </span>{' '}
                    Groups
                  </th>
                  <th>
                    <div className="d-flex align-items-center justify-content-between">
                      <p>Group Name</p>
                      <Box display="flex" alignItems="center" gap={1}>
                        Auto-Generate
                        <MaterialUISwitch
                          checked={autoGenerate}
                          onChange={handleChange('autoGenerate')}
                          inputProps={{ 'aria-label': 'controlled' }}
                          disabled={isPublished}
                        />
                      </Box>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {genderDeliveryTypes.size ? (
                  genderDeliveryTypes.valueSeq().map((group, index) => {
                    const types = group.get('delivery_type', List());
                    // const selected = types.filter((type) => type.get('checked'));
                    return (
                      <Fragment key={index}>
                        <tr>
                          <td colSpan={3} className="p-0">
                            <Box
                              sx={deliveryGroupNameSx}
                              {...(index !== 0 && { className: 'digi-mt-12' })}
                            >
                              <p className="text-uppercase">{group.get('type_name', '')}</p>
                              {/* <p>
                                {selected.size}/{types.size}
                              </p> */}
                            </Box>
                          </td>
                        </tr>
                        {types.map((type, index) => (
                          <tr key={index}>
                            <td>
                              <div className="d-flex align-items-center">
                                {/* <Checkbox
                                  sx={checkboxSx}
                                  checked={type.get('checked', false)}
                                  onChange={handleDeliveryTypes('checked', group, index)}
                                  // disabled={isPublished || type.get('isDisabled', false)}
                                  disabled
                                /> */}
                                {type.get('delivery_symbol', '')} - {type.get('delivery_type', '')}
                              </div>
                            </td>
                            <td className="vertical-align-top">
                              {type.get('isLowerHierarchy') ? (
                                <EmptyTD />
                              ) : (
                                <NumberInput
                                  value={type.get('noOfGroups', 0)}
                                  changed={handleDeliveryTypes('noOfGroups', group, index)}
                                  // disabled={
                                  //   isPublished ||
                                  //   type.get('isFromHierarchy', false) ||
                                  //   !type.get('checked', false)
                                  // }
                                  error={!!type.get('noOfGroupsErrorMsg')}
                                  isDecrementDisabled={isDecrementDisabled(type)}
                                  max={maxNoOfGroups}
                                />
                                // <MaterialInput
                                //   elementType="materialInput"
                                //   type="number"
                                //   variant="outlined"
                                //   size="small"
                                //   value={type.get('noOfGroups', '')}
                                //   changed={handleDeliveryTypes('noOfGroups', group, index)}
                                //   disabled={
                                //     isPublished ||
                                //     type.get('isFromHierarchy', false) ||
                                //     !type.get('checked', false)
                                //   }
                                //   sx={inputSx}
                                //   error={!!type.get('noOfGroupsErrorMsg')}
                                // />
                              )}
                              <ValidationError
                                show={type.get('noOfGroupsErrorMsg')}
                                message={type.get('noOfGroupsErrorMsg')}
                              />
                            </td>
                            <td className="vertical-align-top">
                              {!type.get('isLowerHierarchy') && type.get('checked', false) ? (
                                <>
                                  {autoGenerate ? (
                                    <div className="d-flex align-items-center h-100 f-12 text-light-grey">
                                      {constructGroupName(type)}
                                    </div>
                                  ) : (
                                    <>
                                      <MaterialInput
                                        elementType="materialInput"
                                        type="text"
                                        variant="outlined"
                                        size="small"
                                        placeholder="Enter a name"
                                        value={type.get('groupName', '')}
                                        changed={handleDeliveryTypes('groupName', group, index)}
                                        sx={inputSx}
                                        error={type.get('isEmptyGroupName', false)}
                                        disabled={isPublished || type.get('isDisabled', false)}
                                      />
                                      <ValidationError
                                        show={type.get('isEmptyGroupName', false)}
                                        message="Group name missing!"
                                      />
                                    </>
                                  )}
                                </>
                              ) : (
                                <EmptyTD />
                              )}
                            </td>
                          </tr>
                        ))}
                      </Fragment>
                    );
                  })
                ) : (
                  <tr>
                    <td colSpan={3}>
                      <p className="py-1 text-center text-gray">No delivery types found</p>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {yearOptions.size > 0 && (
            <div className="d-flex align-items-center mt-4 px-3">
              <FormControlLabel
                control={<Checkbox sx={checkboxSx} />}
                className="mb-0 gray-neutral"
                label="Applicable for upcoming academic years:"
                checked={upcomingYears}
                onChange={(e) => setUpcomingYears(e.target.checked)}
                disabled={isPublished}
              />
              <div>
                <YearSelect
                  options={yearOptions}
                  value={modalState.get('upcomingYears', List()).toJS()}
                  handleChange={handleChange('upcomingYears')}
                  disabled={isPublished || !upcomingYears}
                />
              </div>
            </div>
          )}
        </DialogContent>
        <DialogActions className="p-3 border-top">
          <MButton variant="outlined" color="gray" clicked={handleClose} sx={buttonSx}>
            Cancel
          </MButton>
          <MButton
            variant="contained"
            color="primary"
            clicked={() => handleClickSave()}
            sx={buttonSx}
            // disabled={isPublished}
          >
            Save
          </MButton>
        </DialogActions>
      </Dialog>

      {showConfirmModal.get('show') && (
        <Suspense fallback="">
          <ImportConfirmModal
            open
            data={data}
            importedList={showConfirmModal.get('importedList', Map())}
            handleClose={handleClose}
            handleConfirm={() => handleClickSave(true)}
            isDeliveryGroups
          />
        </Suspense>
      )}
    </>
  );
};
DeliveryGroupsModal.propTypes = {
  open: PropTypes.bool,
  data: PropTypes.instanceOf(Map),
  handleClose: PropTypes.func,
  handleSave: PropTypes.func,
  hasSettings: PropTypes.bool,
};

export default DeliveryGroupsModal;
