import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import MButton from 'Widgets/FormElements/material/Button';
import { Trans } from 'react-i18next';
import MaterialInput from 'Widgets/FormElements/material/Input';
import DialogModal from 'Widgets/FormElements/material/DialogModal';
import { List, Map } from 'immutable';
import { designationValidation } from '../utils';
import CancelModal from 'Containers/Modal/Cancel';

function StaffTypeModal({
  open,
  handleClose,
  updateDesignation,
  getDesignations,
  settingId,
  state,
  designations,
  setData,
  institutionHeader,
  type,
}) {
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [edited, setEdited] = useState(false);
  const [name, setName] = useState(state.get('designationName', ''));
  const [namesArray, setNamesArray] = useState(List);
  const id = state?.get('_id', '');
  const handleChange = (e) => {
    setName(e.target.value);
    setEdited(true);
  };

  useEffect(() => {
    let tempNamesArray = designations?.map((item, index) => {
      return item.get('designationName', '').toLowerCase();
    });
    tempNamesArray.size > 0 && setNamesArray(tempNamesArray);
  }, []); //eslint-disable-line

  const callBack = () => {
    handleClose();
    getDesignations({ headers: institutionHeader, settingId, type });
  };
  const handleSubmit = (operation) => {
    if (designationValidation(name, namesArray, setData)) {
      updateDesignation({
        operation,
        ...(operation === 'update' && { id }),
        settingId,
        name: name.trim(),
        callBack,
        headers: institutionHeader,
        type,
      });
    }
  };

  const handleCancel = () => {
    edited ? setShowCancelModal(true) : handleClose();
  };

  return (
    <>
      <DialogModal show={open} onClose={handleCancel}>
        <div className="p-4">
          <div className="mb-2">
            <p className="f-22">
              {' '}
              {state.size ? (
                <Trans i18nKey={'userManagement.edit_designation'}></Trans>
              ) : (
                <Trans i18nKey={'userManagement.add_designation'}></Trans>
              )}
            </p>

            <MaterialInput
              elementType={'materialInput'}
              type={'text'}
              variant={'outlined'}
              label={<Trans i18nKey={'userManagement.designation'}></Trans>}
              labelclass={'mb-1 f-15'}
              changed={(e) => handleChange(e)}
              value={name}
              maxLength={100}
              size={'small'}
            />
          </div>

          <div className="mt-4 text-right">
            <MButton className="mr-3" color="gray" variant="outlined" clicked={handleCancel}>
              <Trans i18nKey={'cancel'}></Trans>
            </MButton>
            <MButton
              color="primary"
              variant="contained"
              clicked={() => handleSubmit(state.size ? 'update' : 'create')}
              disabled={!edited || !name}
            >
              <Trans i18nKey={'save'}></Trans>
            </MButton>
          </div>
        </div>
      </DialogModal>

      <CancelModal
        showCancel={showCancelModal}
        setCancel={setShowCancelModal}
        setShow={handleClose}
      />
    </>
  );
}

StaffTypeModal.propTypes = {
  open: PropTypes.bool,
  handleClose: PropTypes.func,
  updateDesignation: PropTypes.func,
  getDesignations: PropTypes.func,
  settingId: PropTypes.string,
  type: PropTypes.string,
  state: PropTypes.instanceOf(Map),
  designations: PropTypes.instanceOf(List),
  setData: PropTypes.func,
  institutionHeader: PropTypes.object,
};

export default StaffTypeModal;
