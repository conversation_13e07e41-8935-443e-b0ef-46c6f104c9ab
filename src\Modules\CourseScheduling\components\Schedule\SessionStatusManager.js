import React, { useEffect, useState } from 'react';
import Accordion from '@mui/material/Accordion';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { record_session_status } from 'Modules/GlobalConfiguration/utils';
import { Divider, Switch } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import {
  getCourseSessionManagement,
  setCourseSessionManagement,
} from '_reduxapi/course_scheduling/action';
import {
  selectCourseSessionStatusManagement,
  selectIndividualCourseDetails,
} from '_reduxapi/course_scheduling/selectors';
import { Map } from 'immutable';
import LocalStorageService from 'LocalStorageService';

export default function SessionStatusManager() {
  const [expanded, setExpanded] = useState(false);
  const sessionStatusManagement = useSelector(selectCourseSessionStatusManagement);
  const dispatch = useDispatch();
  const individualCourseDetails = useSelector(selectIndividualCourseDetails);
  const course_assigned_details = individualCourseDetails.get('course_assigned_details', Map());
  const activeInstitutionCalendar = LocalStorageService.getCustomToken('activeInstitutionCalendar');
  const calender = activeInstitutionCalendar ? JSON.parse(activeInstitutionCalendar)?._id : '';
  const params = `?programId=${course_assigned_details.get(
    '_program_id',
    ''
  )}&curriculumId=${course_assigned_details.get(
    '_curriculum_id'
  )}&year=${individualCourseDetails.get('year_no', '')}&level=${individualCourseDetails.get(
    'level_no',
    ''
  )}&courseId=${individualCourseDetails.get('_course_id', '')}&term=${individualCourseDetails.get(
    'term',
    ''
  )}&institutionCalendarId=${calender}`;
  const getData = () =>
    dispatch(
      getCourseSessionManagement('/session-status-management/getCourseSessionStatusDetail' + params)
    );

  const handleChange = (key, value) => {
    dispatch(
      setCourseSessionManagement(
        sessionStatusManagement.get('_id', ''),
        { ...sessionStatusManagement.delete('_id').toJS(), [key]: value },
        () => getData()
      )
    );
  };

  useEffect(() => {
    getData();
  }, []); //eslint-disable-line

  return (
    <div>
      {' '}
      <Accordion
        disableGutters
        expanded={expanded}
        onChange={(e) => setExpanded((prev) => !prev)}
        sx={{ boxShadow: 'none', background: 'transparent', border: '1px solid #D1D5DB' }}
      >
        <AccordionSummary
          disableGutters
          expandIcon={<KeyboardArrowDownIcon className="text-blue" />}
          aria-controls="panel1a-content"
          id="panel1a-header"
          // sx={{ borderBottom: '1px solid #D1D5DB' }}
        >
          <div className="_webkit_fill_available d-flex align-items-center">
            <div>
              <div>Session Status Manager</div>
              <div className=" digi-gray-neutral mb-2">
                The system will automatically adjust the status according to these settings.
              </div>
            </div>
            <div
              className="ml-auto"
              onClick={function (e) {
                e.stopPropagation();
              }}
            >
              <Switch
                checked={sessionStatusManagement.get('isEnabled', false)}
                onChange={function (e) {
                  handleChange('isEnabled', e.target?.checked);
                }}
              />
            </div>
            {expanded && <Divider />}
          </div>
        </AccordionSummary>
        <AccordionDetails>
          <div>
            <div>Record the session status for the previously completed session :</div>
            <div className="row pt-2">
              <div className="col-6">
                <div>Active To Inactive</div>
                <div className="text-light-grey f-12 mb-1">Select Status</div>{' '}
                <TextField
                  fullWidth
                  id="outlined-select-currency"
                  select
                  value={sessionStatusManagement.get('activeToInactive', '')}
                  onChange={(e) => handleChange('activeToInactive', e.target?.value)}
                >
                  {record_session_status.map((option) => (
                    <MenuItem
                      key={option.value}
                      value={option.value}
                      selected={
                        option.value === sessionStatusManagement.get('activeToInactive', '')
                      }
                    >
                      {option.name}
                    </MenuItem>
                  ))}
                </TextField>
              </div>
              <div className="col-6">
                <div>Inactive To Active</div>
                <div className="text-light-grey f-12 mb-1">Select Status</div>{' '}
                <TextField
                  fullWidth
                  id="outlined-select-currency"
                  select
                  value={sessionStatusManagement.get('inactiveToActive', '')}
                  onChange={(e) => handleChange('inactiveToActive', e.target?.value)}
                >
                  {record_session_status.map((option) => (
                    <MenuItem
                      key={option.value}
                      value={option.value}
                      selected={
                        option.value === sessionStatusManagement.get('inactiveToActive', '')
                      }
                    >
                      {option.name}
                    </MenuItem>
                  ))}
                </TextField>
              </div>
            </div>
            <div className="text-light-grey  pt-3 f-14">
              For example : After 10 days of a student being inactive, when the admin changes their
              status back to active, the system adjusts the session records for the inactive period
              based on the chosen settings.
            </div>
          </div>
        </AccordionDetails>
      </Accordion>
    </div>
  );
}
