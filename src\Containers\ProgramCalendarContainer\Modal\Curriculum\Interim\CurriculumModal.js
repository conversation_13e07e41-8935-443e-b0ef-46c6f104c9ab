import React, { Fragment, useEffect, useReducer } from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { connect } from 'react-redux';
import {
  toggleInterimModal,
  saveInterimCurriculum,
} from '../../../../../_reduxapi/actions/interimCalendar';
import {
  PrimaryButton,
  Null,
  FlexWrapper,
  InlineBlockWrapper,
  BlockWrapper,
  BlockWrappers,
} from '../../../Styled';
import { initialState_interim_curriculum, curriculum_interim_reducer } from './reducers';
import { getLang, indVerRename } from '../../../../../utils';
import { t } from 'i18next';

const lang = getLang();

const dataAlign = (data, dispatchFn, userId, id, programId) => {
  //local
  let final = {};

  final._program_id = programId;
  final._institution_calendar_id = id;
  final._creater_id = userId;
  const term1Curriculum =
    data.term1.yearLevel &&
    data.term1.yearLevel.length > 0 &&
    data.term1.yearLevel.map((item) => {
      return {
        year_level:
          data.term1.selectedTerms === 'term1_level_wise_view'
            ? item.name
            : 'year' + item.name.replace('Year ', ''),
        version: item.value,
      };
    });

  const term2Curriculum =
    data.term2.yearLevel &&
    data.term2.yearLevel.length > 0 &&
    data.term2.yearLevel.map((item) => {
      return {
        year_level:
          data.term2.selectedTerms === 'term2_level_wise_view'
            ? item.name
            : 'year' + item.name.replace('Year ', ''),
        version: item.value,
      };
    });

  const preparingData = [
    {
      batch: 'regular',
      type: data.term1.selectedTerms === 'term1_level_wise_view' ? 'level' : 'year',
      curriculum: term1Curriculum,
    },

    {
      batch: 'interim',
      type: data.term2.selectedTerms === 'term2_level_wise_view' ? 'level' : 'year',
      curriculum: term2Curriculum,
    },
  ];
  final.data = preparingData;
  return dispatchFn(final); //local
};

const CurriculumModal = ({
  id,
  toggleInterimModal,
  saveInterimCurriculum,
  userId,
  programId,
  curriculum_levels,
  curriculum_years,
  curriculums,
}) => {
  const [curriculumVersion, setCurriculumVersion] = useReducer(
    curriculum_interim_reducer,
    initialState_interim_curriculum
  );

  useEffect(() => {
    const filteredLevel =
      curriculum_levels &&
      curriculum_levels.length > 0 &&
      curriculum_levels.map((item) => {
        return { name: item, value: curriculums.length > 0 ? curriculums[0] : '' };
      });
    const filteredYear =
      curriculum_years &&
      curriculum_years.length > 0 &&
      curriculum_years.map((item) => {
        return { name: 'Year ' + item, value: curriculums.length > 0 ? curriculums[0] : '' };
      });

    setCurriculumVersion({
      type: 'INITIAL_LOAD_MODIFY',
      curriculum_years: filteredYear,
      curriculum_levels: filteredLevel,
    });
  }, [setCurriculumVersion, curriculum_years, curriculum_levels, curriculums]);

  let curriculumOptions = [];
  if (curriculums && curriculums.length > 0) {
    curriculumOptions = curriculums.map((list) => (
      <option key={list} value={list}>
        {list}
      </option>
    ));
  }

  return (
    <Fragment>
      <BlockWrapper style={{ width: '650px' }}>
        <p className={`set-versions ${lang === 'ar' ? 'text-left' : ''}`}>
          <Trans i18nKey={'program_calendar.set_curriculum_versions'}></Trans>
        </p>
        <div className={`row pb-3 ${lang === 'ar' ? 'text-left' : ''}`}>
          <div className="col-md-6 ">
            <InlineBlockWrapper
              className={`text-center black-normal ${lang === 'ar' ? 'text-left' : ''}`}
            >
              <Trans
                i18nKey={'program_calendar.regular_term'}
                values={{ Term: indVerRename('Term', programId) }}
              ></Trans>{' '}
            </InlineBlockWrapper>

            <BlockWrappers>
              <select
                onChange={(e) =>
                  setCurriculumVersion({
                    type: 'CURRICULUM_CHANGE_MODIFY',
                    payload: e.target.value,
                    terms: 'term1',
                  })
                }
                value={curriculumVersion.term1.selectedTerms}
                className="p-2 form-control"
              >
                <option name="term1_curriculum_based_on" value="">
                  {t('program_calendar.select')}
                </option>
                <option name="term1_curriculum_based_on" value="term1_level_wise_view">
                  {t('program_calendar.level_wise_view', {
                    Level: indVerRename('Level', programId),
                  })}
                </option>
                <option name="term1_curriculum_based_on" value="term1_year_wise_view">
                  {t('program_calendar.year_wise_view')}
                </option>
              </select>
            </BlockWrappers>

            <div className="col-md-12 ">
              {curriculumVersion.term1.show && (
                <Fragment>
                  {curriculumVersion.term1.yearLevel &&
                    curriculumVersion.term1.yearLevel.length > 0 &&
                    curriculumVersion.term1.yearLevel.map((item, index) => {
                      return (
                        <div className="row pt-2" key={index}>
                          <div className="col-md-5">
                            <p className="mb-1 pt-2"> {item.name} &nbsp; </p>
                          </div>
                          <div className="col-md-7">
                            <select
                              onChange={(e) =>
                                setCurriculumVersion({
                                  type: 'VALUE_CHANGE_MODIFY',
                                  terms: 'term1',
                                  payload: e.target.value,
                                  name: index,
                                })
                              }
                              value={item.value}
                              className="p-2 form-control"
                            >
                              {curriculumOptions}
                            </select>
                          </div>
                        </div>
                      );
                    })}
                </Fragment>
              )}
            </div>
          </div>

          <div className="col-md-6  border-left">
            <InlineBlockWrapper className="black-normal">
              {' '}
              <Trans
                i18nKey={'program_calendar.interim_term'}
                values={{ Term: indVerRename('Term', programId) }}
              ></Trans>
            </InlineBlockWrapper>
            <div className="clearfix"> </div>

            <BlockWrappers>
              <select
                onChange={(e) =>
                  setCurriculumVersion({
                    type: 'CURRICULUM_CHANGE_MODIFY',
                    payload: e.target.value,
                    terms: 'term2',
                  })
                }
                value={curriculumVersion.term2.selectedTerms}
                className="p-2  form-control"
              >
                <option name="term2_curriculum_based_on" value="">
                  {t('program_calendar.select')}
                </option>
                <option name="term2_curriculum_based_on" value="term2_level_wise_view">
                  {t('program_calendar.level_wise_view', {
                    Level: indVerRename('Level', programId),
                  })}
                </option>
                <option name="term2_curriculum_based_on" value="term2_year_wise_view">
                  {t('program_calendar.year_wise_view')}
                </option>
              </select>
            </BlockWrappers>
            <div className="col-md-12">
              {curriculumVersion.term2.show && (
                <Fragment>
                  {curriculumVersion.term2.yearLevel &&
                    curriculumVersion.term2.yearLevel.length > 0 &&
                    curriculumVersion.term2.yearLevel.map((item, index) => {
                      return (
                        <div className="row pt-2" key={index}>
                          <div className="col-md-5">
                            <p className="mb-1 pt-2"> {item.name} &nbsp; </p>
                          </div>
                          <div className="col-md-7">
                            <select
                              onChange={(e) =>
                                setCurriculumVersion({
                                  type: 'VALUE_CHANGE_MODIFY',
                                  terms: 'term2',
                                  payload: e.target.value,
                                  name: index,
                                })
                              }
                              value={item.value}
                              className="p-2 form-control"
                            >
                              {curriculumOptions}
                            </select>
                          </div>
                        </div>
                      );
                    })}
                </Fragment>
              )}
            </div>
          </div>
        </div>
      </BlockWrapper>
      <FlexWrapper className="ji_end">
        <Null />
        <PrimaryButton className="light" onClick={() => toggleInterimModal()}>
          <Trans i18nKey={'cancel'}></Trans>
        </PrimaryButton>
        {curriculumVersion.term1.yearLevel.length > 0 &&
          curriculumVersion.term2.yearLevel.length > 0 && (
            <PrimaryButton
              className="bordernone"
              onClick={() =>
                dataAlign(curriculumVersion, saveInterimCurriculum, userId, id, programId)
              }
            >
              <Trans i18nKey={'save'}></Trans>
            </PrimaryButton>
          )}
      </FlexWrapper>
    </Fragment>
  );
};

CurriculumModal.propTypes = {
  id: PropTypes.string,
  userId: PropTypes.string,
  programId: PropTypes.string,
  toggleInterimModal: PropTypes.func,
  saveInterimCurriculum: PropTypes.func,
  curriculum_levels: PropTypes.array,
  curriculum_years: PropTypes.array,
  curriculums: PropTypes.array,
};

const mapStateToProps = ({ calender, auth, interimCalendar }) => ({
  id: interimCalendar.institution_Calender_Id,
  curriculum: interimCalendar.curriculum,
  userId: auth.token.replace(/"/g, ''),
  programId: calender.programId,
  curriculum_years: interimCalendar.curriculum_years,
  curriculum_levels: interimCalendar.curriculum_levels,
  curriculums:
    interimCalendar.curriculum && interimCalendar.curriculum.length > 0
      ? interimCalendar.curriculum
      : [],
});

export default connect(mapStateToProps, {
  toggleInterimModal,
  saveInterimCurriculum,
})(CurriculumModal);
