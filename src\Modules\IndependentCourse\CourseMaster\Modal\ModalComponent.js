import React, { useContext, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { t } from 'i18next';
import { List, Map } from 'immutable';
import { useStateHook } from '../../ICutil';

import MButton from 'Widgets/FormElements/material/Button';
import { Checkbox, Chip, FormControl, Input, ListItemText, MenuItem, Select } from '@mui/material';
import AutoComplete from 'Widgets/FormElements/material/Autocomplete';
import MaterialInput from 'Widgets/FormElements/material/Input';
import ClearIcon from '@mui/icons-material/Clear';
import { useStylesFunction, MenuProps } from 'Modules/ProgramInput/v2/piUtil';
import { independentCourseContext, courseMasterContext } from '../../context';

export function Header() {
  return (
    <div className="d-flex justify-content-between pb-3 pt-2">
      <p className="f-20 mb-2 ">
        <Trans i18nKey={'course_master.basic_details'}></Trans>{' '}
      </p>
      <span className="text-lightgray font-italic mt-2 f-14">
        {' '}
        <Trans i18nKey={'all_fields_mandatory'}></Trans>{' '}
      </span>
    </div>
  );
}

export function Footer({ onClickFunc, isEdited, isEdit }) {
  const { onClickCancel, onClickSave } = onClickFunc;
  return (
    <div className="row pt-2 pb-4">
      <div className="col-md-4 col-4">
        <MButton variant="outlined" color="gray" clicked={onClickCancel}>
          <Trans i18nKey={'cancel'}></Trans>
        </MButton>
      </div>
      <div className="col-md-8 col-8 text-right">
        <b className="pr-2">
          <MButton
            variant="outlined"
            clicked={() => onClickSave('save', isEdit)}
            disabled={!isEdited}
          >
            <Trans i18nKey={'curriculum_keys.save'}></Trans>
          </MButton>
        </b>
        <b>
          <MButton disabled={!isEdited} clicked={() => onClickSave('continue', isEdit)}>
            <Trans i18nKey={'curriculum_keys.save_continue'}></Trans>
          </MButton>
        </b>
      </div>
    </div>
  );
}

Footer.propTypes = {
  onClickFunc: PropTypes.object,
  isEdited: PropTypes.bool,
  isEdit: PropTypes.bool,
};
const textBox = ({ getToolTipData = {}, onChange, value = '', length = 10, title = '' }) => (
  <div className="col-md-12 pb-2 f-15">
    <MaterialInput
      elementType={'materialInput'}
      type={'text'}
      variant={'outlined'}
      size={'small'}
      label={
        <span>
          <Trans
            components={{ course: getToolTipData.course }}
          >{`label_config.course__${title}`}</Trans>
        </span>
      }
      name={`course${title}`}
      changed={onChange}
      placeholder={t(`label_config.course_${title}`, {
        course: getToolTipData.courseNT,
      })}
      value={value}
      maxLength={length}
    />
  </div>
);
const autoComplete = ({ label = '', value = '', placeholder = '', options = [], onChange }) => (
  <div className="col-md-12 pb-2 f-15">
    <div className="form-group d-flex flex-column">
      <label className="form-label ">{label}</label>
      <AutoComplete
        type={'text'}
        variant={'outlined'}
        value={value}
        size={'small'}
        isClearable={value && true}
        options={options}
        onChange={(event, value) => onChange(value)}
        placeholder={placeholder}
      />
    </div>
  </div>
);
const selectBox = ({
  classes,
  label,
  options,
  value,
  onChange,
  disabled = false,
  handleDelete,
}) => (
  <div className="col-md-12 pb-2 f-15">
    <div className="form-group d-flex flex-column">
      {label}
      <FormControl className={classes.formControl1}>
        <Select
          multiple
          value={value}
          onChange={onChange}
          name="participating"
          input={<Input id="select-multiple-chip" variant={'outlined'} />}
          MenuProps={MenuProps}
          className={`${classes.standardInput} ${classes.borderBottomUnset}`}
          classes={{ root: classes.selectRoot }}
          renderValue={(selected) => (
            <div>
              {options
                .filter((item) => selected.includes(item.get('_id', '')))
                .map((value, index) => (
                  <Chip
                    key={index}
                    label={value.get('name', '')}
                    className={classes.chip}
                    onDelete={() => handleDelete(value.get('_id', ''))}
                    deleteIcon={<ClearIcon onMouseDown={(event) => event.stopPropagation()} />}
                  />
                ))}
            </div>
          )}
        >
          {options.map((subject, index) => (
            <MenuItem key={index} value={subject.get('value', '')} disabled={disabled}>
              <Checkbox color="primary" checked={value.indexOf(subject.get('value', '')) > -1} />
              <ListItemText primary={subject.get('name', '')} className={'text-overflow-mui'} />
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </div>
  </div>
);
const getAdministrationLabel = (getToolTipData) => (
  <Trans
    components={{
      subject: getToolTipData.subject,
      department: getToolTipData.department,
      program: getToolTipData.program,
    }}
  >
    course_master.administrating
  </Trans>
);
const getAdministrationPlaceHolder = (getToolTipData) =>
  t('course_master.administratingLabel', {
    subject: getToolTipData.subjectNT,
    department: getToolTipData.departmentNT,
    program: getToolTipData.programNT,
  });
const getEditorLabel = (getToolTipData) => (
  <span>
    <Trans components={{ course: getToolTipData.course }}>label_config.course__editor</Trans>
  </span>
);
const getDeliveryLabel = (getToolTipData) => (
  <label className="form-label ">
    <span>
      <Trans components={{ subject: getToolTipData.subject }}>course_delivering_subjects</Trans>
    </span>
  </label>
);
const getEditorPlaceHolder = (getToolTipData) =>
  t('label_config.course_editor', {
    course: getToolTipData.courseNT,
  });

const constructOptions = (subjectsList) => {
  if (subjectsList.get('subjects', List()).size === 0) return [[], []];
  let deliveryOptions = subjectsList
    .get('subjects', List())
    .map((item) => item.set('name', item.get('subjectName', '')).set('value', item.get('_id', '')));
  let administrationOptions = subjectsList
    .get('subjects', List())
    .map((subject) => subject.get('adminstrating', ''))
    .toJS();

  return [deliveryOptions, administrationOptions];
};
export function Body({ saveDetails, enableSaveButton, isEdit, course }) {
  const classes = useStylesFunction();
  const { getToolTipData } = useContext(independentCourseContext);
  const { subjectsList } = useContext(courseMasterContext);
  const [code, setCode] = useStateHook(isEdit ? course.get('courseCode', '') : '');
  const [name, setName] = useStateHook(isEdit ? course.get('courseName', '') : '');
  const [administration, setAdministration] = useStateHook(
    isEdit
      ? `${course.getIn(['administration', 'subjectName'], '')} / ${course.getIn(
          ['administration', 'departmentName'],
          ''
        )} / ${course.getIn(['administration', 'programName'], 'admin')}`
      : ''
  );
  const [delivery, setDelivery] = useStateHook(
    isEdit
      ? course
          .get('participating', List())
          .map((item) => item.get('_subject_id', ''))
          .toJS()
      : []
  );
  //const [editor, setEditor] = useStateHook('');
  useEffect(() => {
    saveDetails.current = returnSavedData;
  });
  const returnSavedData = () => {
    const getParticipating = () => {
      let filteredData = subjectsList
        .get('subjects', List())
        .filter((subjects) => delivery.includes(subjects.get('_id', '')));
      let setData = filteredData
        .map((item) =>
          item.set('_subject_id', item.get('_id', '')).delete('adminstrating').delete('_id')
        )
        .toJS();
      return setData;
    };
    const getAdministration = () => {
      let filteredData = subjectsList
        .get('subjects', List())
        .filter((subject) => subject.get('adminstrating', '') === administration)
        .get(0, Map());
      let setData = filteredData
        .set('_subject_id', filteredData.get('_id', ''))
        .delete('adminstrating')
        .delete('_id')
        .toJS();
      return setData;
    };
    let request = {
      courseCode: code,
      courseName: name,
      courseType: 'independent',
      administration: getAdministration(),
      participating: getParticipating(),
    };
    return request;
  };
  const handleDelete = (data) => {
    enableSaveButton();
    setDelivery(delivery.filter((item) => item !== data));
  };
  const [deliveryOptions, administrationOptions] = useMemo(() => constructOptions(subjectsList), [
    subjectsList,
  ]);
  return (
    <div className="row ">
      {textBox({
        value: code,
        onChange: (e) => {
          setCode(e);
          enableSaveButton();
        },
        length: 20,
        getToolTipData,
        title: 'code',
      })}
      {textBox({
        value: name,
        onChange: (e) => {
          setName(e);
          enableSaveButton();
        },
        length: 150,
        getToolTipData,
        title: 'name',
      })}
      {selectBox({
        classes,
        label: getDeliveryLabel(getToolTipData),
        options: deliveryOptions,
        value: delivery,
        onChange: (e) => {
          setDelivery(e);
          enableSaveButton();
        },
        handleDelete,
      })}

      {autoComplete({
        label: getAdministrationLabel(getToolTipData),
        value: administration,
        placeholder: getAdministrationPlaceHolder(getToolTipData),
        options: administrationOptions,
        onChange: (e) => {
          setAdministration(e);
          enableSaveButton();
        },
      })}
      {autoComplete({
        label: getEditorLabel(getToolTipData),
        value: '',
        placeholder: getEditorPlaceHolder(getToolTipData),
        options: [],
        onChange: () => {},
      })}
    </div>
  );
}
Body.propTypes = {
  saveDetails: PropTypes.object,
  enableSaveButton: PropTypes.func,
  course: PropTypes.instanceOf(Map),
  isEdit: PropTypes.bool,
};
