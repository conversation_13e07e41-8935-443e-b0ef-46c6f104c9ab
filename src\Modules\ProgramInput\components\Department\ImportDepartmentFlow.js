import React, { Component } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';
import PropTypes from 'prop-types';
import readXlsxFile from 'read-excel-file';
import { Mo<PERSON>, But<PERSON>, Table } from 'react-bootstrap';
import { Trans } from 'react-i18next';
import { List } from 'immutable';

import ErrorModal from '../../../StudentGrouping/Modal/ErrorModal';
import * as actions from '../../../../_reduxapi/program_input/action';
import {
  selectInvalidDataDeptList,
  selectvalidDataDeptList,
} from '../../../../_reduxapi/program_input/selectors';
import { getURLParams, getLang } from '../../../../utils';
import { t } from 'i18next';
import { exportToExcel } from 'Modules/ProgramInput/utils';
const lng = getLang();

class ImportFromDeptFile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      isLoading: false,
      exportShow: true,
      importShow: false,
      importStudent: false,
      importStudentList: false,
      directValid: true,
      chooseNational: '',
      csvRecords: [],
      group1: true,
      group2: false,
      group3: false,
      importAlert: false,
      importTable: false,
      filename: null,
      dataCheckShow: true,
      invalidList: [],
      importFormatError: false,
      importBtnEnable: false,
      programId: getURLParams('_id', true),
      curriculumId: getURLParams('_curriculum_id', true),
    };
  }

  handleChange = (event) => {
    if (
      event.target.files[0].type !==
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      this.setState({ importFormatError: true });
    } else {
      this.setState({ importFormatError: false, tempMismatch: false });
      this.getRecords(event.target.files[0]);
    }
  };

  handleChangeData = () => {
    this.setState({
      filename: null,
      csvRecords: [],
      importBtnEnable: false,
      tempMismatch: false,
    });
  };

  sampleData = () => {
    const sampleData = [
      {
        [t('program_input.sample_data_headers.Program_Name')]: '',
        [t('program_input.sample_data_headers.Program_Code')]: '',
        [t('program_input.sample_data_headers.Department_Name')]: '',
        [t('the_subjects')]: '',
      },
    ];

    exportToExcel(sampleData, 'SampleDepartment');
  };
  async getRecords(csvFileObject) {
    const map = {
      [t('program_input.sample_data_headers.Program_Name')]: 'Program_Name',
      [t('program_input.sample_data_headers.Program_Code')]: 'Program_Code',
      [t('program_input.sample_data_headers.Department_Name')]: 'Department_Name',
      [t('the_subjects')]: 'Subjects',
    };
    var filename = csvFileObject.name;

    this.setState({ filename });
    let Department = ['Department_Name', 'Subjects'];
    await readXlsxFile(csvFileObject, { map }).then(({ rows }) => {
      if (rows[0] === undefined) {
        this.setState({ tempMismatch: true });
        return true;
      }
      let tempMismatch = Object.keys(rows[0]).filter((element) => Department.includes(element));
      if (tempMismatch.length === 0) {
        this.setState({ tempMismatch: true });
        return true;
      }
      this.setState({ csvRecords: rows, importBtnEnable: true });
    });
  }

  afterImport = () => {
    const { ImportDeptCsvCheck } = this.props;
    const { csvRecords } = this.state;
    this.setState({
      dataCheckShow: false,
    });
    let data = csvRecords.map((row) => {
      return {
        Program_Name:
          String(row.Program_Name) === 'undefined' ? '' : String(row.Program_Name).trim(),
        Program_Code:
          String(row.Program_Code) === 'undefined' ? '' : String(row.Program_Code).trim(),
        Department_Name:
          String(row.Department_Name) === 'undefined' ? '' : String(row.Department_Name).trim(),
        Subjects: String(row.Subjects) === 'undefined' ? '' : String(row.Subjects).trim(),
      };
    });
    let body = {
      department: data,
    };

    ImportDeptCsvCheck(
      `digi_department_subject/data_check_department_subject`,
      body,
      () => {
        this.inValidDataCheck();
      },
      () => {
        this.validDataCheck();
      }
    );
  };

  validDataCheck = () => {
    this.props.closed(false);
  };
  fileName = () => {
    this.setState({
      filename: null,
      tempMismatch: false,
    });
  };

  importContinue = () => {
    const { ValidDeptDataList, ImportDeptCsv } = this.props;

    if (ValidDeptDataList.size === 0) {
      this.props.closed(false);
    }
    if (ValidDeptDataList.size > 0) {
      let listData = this.props.ValidDeptDataList && this.props.ValidDeptDataList.toJS();

      let data = listData.map((row) => {
        return {
          Program_Name:
            String(row.data.Program_Name) === 'undefined'
              ? ''
              : String(row.data.Program_Name).trim(),
          Program_Code:
            String(row.data.Program_Code) === 'undefined'
              ? ''
              : String(row.data.Program_Code).trim(),
          Department_Name:
            String(row.data.Department_Name) === 'undefined'
              ? ''
              : String(row.data.Department_Name).trim(),
          Subjects:
            String(row.data.Subjects) === 'undefined' ? '' : String(row.data.Subjects).trim(),
        };
      });
      let body = {
        department: data,
      };

      ImportDeptCsv(`digi_department_subject/import_department_subject`, body, () => {
        this.props.closed(false);
      });
    }
  };
  importListBack = () => {
    this.setState({
      dataCheckShow: true,
      directValid: true,
    });
  };
  exportList = () => {
    let listData = this.props.InvalidDeptDataList && this.props.InvalidDeptDataList.toJS();

    let data = listData.map((row) => {
      return {
        [t('program_input.sample_data_headers.Program_Name')]: row.data.Program_Name,
        [t('program_input.sample_data_headers.Program_Code')]: row.data.Program_Code,
        [t('program_input.sample_data_headers.Department_Name')]: row.data.Department_Name,
        [t('the_subjects')]: row.data.Subjects,
      };
    });
    this.setState({ invalidList: data });

    exportToExcel(data, 'InvalidDeptDataList');
  };
  cancelBtn = () => {
    this.setState(
      {
        dataCheckShow: false,
      },
      () => {
        this.props.closed(false);
      }
    );
  };

  inValidDataCheck = () => {
    this.setState({ directValid: false });
  };
  render() {
    const {
      filename,
      csvRecords,
      directValid,
      dataCheckShow,
      importFormatError,
      tempMismatch,
      importBtnEnable,
    } = this.state;
    const { InvalidDeptDataList, ValidDeptDataList } = this.props;

    return (
      <div className="main pt-3 pb-5 bg-white">
        <Modal show={dataCheckShow} dialogClassName="model-800">
          <div className="container">
            <p className="f-20 mb-2 pt-3">
              {' '}
              <Trans i18nKey={'import_department'}></Trans>
            </p>
          </div>

          <Modal.Body>
            <div className="model-main bg-gray pl-3 border-radious-8">
              <p className="f-14 mb-2 pt-2 text-gray">
                {' '}
                <Trans i18nKey={'select_appropriate'}></Trans>
              </p>

              <div className="row pb-5">
                <div className="col-md-5">
                  <b className="f-16 mb-4 pt-3">
                    <Trans i18nKey={'select_a_file'}></Trans> <br></br>
                    <small>
                      <Trans i18nKey={'accepted_formats'}></Trans>
                    </small>
                  </b>
                  {filename === null ? (
                    ''
                  ) : (
                    <div className="bg-lightdark border-radious-8 w-75 ">
                      <b className=" pl-2 pr-2 f-14" onClick={this.handleChangeData}>
                        {filename.length > 15 ? filename.substring(0, 4) + '...' : filename}{' '}
                        <i
                          className="fa fa-times remove_hover import_remove_icon float-right"
                          aria-hidden="true"
                        ></i>
                      </b>
                    </div>
                  )}
                </div>

                <div className="col-md-7">
                  <b className="pl-5">
                    <label
                      htmlFor="fileUpload"
                      className="file-upload btn btn-primary  import-padding border-radious-8"
                    >
                      <Trans i18nKey={'browse'}></Trans>
                      <input
                        id="fileUpload"
                        type="file"
                        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        value=""
                        onChange={this.handleChange}
                      />
                    </label>
                  </b>

                  <Link>
                    <b className="pl-5 text-blue" onClick={() => this.sampleData()}>
                      {' '}
                      <i className="fa fa-download pr-2" aria-hidden="true"></i>
                      <Trans i18nKey={'download_template'}></Trans>
                    </b>
                  </Link>
                </div>
              </div>

              <div className="border-radious-8 bg-lightgray">
                <div className="row d-flex justify-content-center">
                  <div className="col-md-11 pt-5 ">
                    <div className="p-2 bg-lightdark border-radious-8">
                      <Table className="">
                        <thead>
                          <tr className="no-border">
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'program_name'}></Trans>
                              </Button>
                            </th>
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'program_code'}></Trans>{' '}
                              </Button>
                            </th>

                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'department_name'}></Trans>
                              </Button>
                            </th>
                            <th className="no-borders">
                              <Button variant="light" id="dropdown-split-basic">
                                <Trans i18nKey={'the_subjects'}></Trans>
                              </Button>
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {csvRecords &&
                            csvRecords
                              .filter((val, i) => i < 3)
                              .map((item, i) => {
                                return (
                                  <tr className="border-dark-import" key={i}>
                                    <td className="border-left-import overflow_wrap">
                                      {item.Program_Name}
                                    </td>
                                    <td className="border-left-import overflow_wrap">
                                      {item.Program_Code}
                                    </td>

                                    <td className="border-left-import overflow_wrap">
                                      {item.Department_Name}
                                    </td>
                                    <td className="border-left-import overflow_wrap">
                                      {item.Subjects}
                                    </td>
                                  </tr>
                                );
                              })}
                        </tbody>
                      </Table>
                    </div>
                  </div>

                  <div className="col-md-11 pt-2 pb-2">
                    <b className="float-right f-14">
                      {csvRecords.length} <Trans i18nKey={'rows_import'}></Trans>{' '}
                    </b>
                  </div>
                </div>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <div className={`${lng === 'ar' ? 'pl-4' : 'pr-4'}`}>
              <p className="text-blue mb-0 remove_hover" onClick={this.cancelBtn}>
                {' '}
                <Trans i18nKey={'cancel'}></Trans>{' '}
              </p>
            </div>

            <div className="pr-2">
              <Button
                variant={importBtnEnable && csvRecords.length > 0 ? 'primary' : 'secondary'}
                disabled={importBtnEnable && csvRecords.length > 0 ? false : true}
                onClick={this.afterImport}
              >
                <Trans i18nKey={'import'}></Trans>
              </Button>
            </div>
          </Modal.Footer>
        </Modal>
        {!directValid && (
          <Modal show={true} size="lg">
            <div className="container">
              <p className="f-20 mb-1 pt-3">
                {' '}
                <Trans i18nKey={'import_department'}></Trans>
              </p>

              <p className="f-14 mb-2 ">
                {' '}
                {InvalidDeptDataList?.size} <Trans i18nKey={'of'}></Trans> {csvRecords.length}{' '}
                <Trans i18nKey={'not_imported'}></Trans>
              </p>
            </div>

            <Modal.Body>
              <div className="pb-1">
                <p className="f-16 mb-2">
                  {' '}
                  <Trans i18nKey={'data_check'}></Trans>
                </p>
                <div className="row">
                  <div className="col-md-6">
                    <p className="f-14 mb-2">
                      <Trans i18nKey={'list_error_entity'}></Trans>
                    </p>
                  </div>
                  <p className="f-14 mb-2 float-right text-blue" onClick={() => this.exportList()}>
                    <Trans i18nKey={'export_entry'}></Trans>
                  </p>
                </div>

                <div className="go-wrapper">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>
                          <div className="aw-75">
                            <Trans i18nKey={'s_no'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-100">
                            <Trans i18nKey={'program_name'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-200">
                            <Trans i18nKey={'program_code'}></Trans>
                          </div>
                        </th>
                        <th>
                          <div className="aw-300">
                            <Trans i18nKey={'error_message'}></Trans>{' '}
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="go-wrapper-height tr-change">
                      {InvalidDeptDataList &&
                        InvalidDeptDataList.map((item, i) => {
                          return (
                            <tr key={i}>
                              <td>
                                <div className="aw-75">{i + 1}</div>
                              </td>
                              <td>
                                <div className="aw-100">{item.getIn(['data', 'Program_Name'])}</div>
                              </td>
                              <td>
                                <div className="aw-200">{item.getIn(['data', 'Program_Code'])}</div>
                              </td>

                              <td>
                                <div className="aw-300">
                                  {' '}
                                  {item.getIn(['message']).map((message, i) => (
                                    <li key={i}>{message}</li>
                                  ))}
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                    </tbody>
                  </table>
                </div>
              </div>
            </Modal.Body>

            <Modal.Footer>
              <div className="pr-4">
                <p className="text-blue mb-0 remove_hover" onClick={this.importListBack}>
                  {' '}
                  <Trans i18nKey={'back'}></Trans>{' '}
                </p>
              </div>

              <div className="pr-2">
                <Button
                  variant={
                    ValidDeptDataList && ValidDeptDataList.size > 0 ? 'primary' : 'secondary'
                  }
                  onClick={this.importContinue}
                  disabled={ValidDeptDataList && ValidDeptDataList.size === 0}
                >
                  <Trans i18nKey={'continue'}></Trans>
                </Button>
              </div>
            </Modal.Footer>
          </Modal>
        )}
        {importFormatError && (
          <div>
            <ErrorModal
              showDetail={importFormatError}
              title={t('modal.messages.invalid_format')}
              content={t('modal.messages.upload_error')}
              filename={this.fileName}
            />
          </div>
        )}

        {tempMismatch && (
          <div>
            <ErrorModal
              showDetail={tempMismatch}
              title={t('modal.messages.template_mismatch')}
              content={t('modal.messages.template_content')}
              filename={this.fileName}
            />
          </div>
        )}
      </div>
    );
  }
}

ImportFromDeptFile.propTypes = {
  ImportDeptCsvCheck: PropTypes.func,
  closed: PropTypes.func,
  ValidDeptDataList: PropTypes.instanceOf(List),
  ImportDeptCsv: PropTypes.func,
  InvalidDeptDataList: PropTypes.instanceOf(List),
};

const mapStateToProps = function (state) {
  return {
    InvalidDeptDataList: selectInvalidDataDeptList(state),
    ValidDeptDataList: selectvalidDataDeptList(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(ImportFromDeptFile);
