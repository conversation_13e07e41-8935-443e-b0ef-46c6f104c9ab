import { useSelector } from 'react-redux';
import { selectUserInfo } from '_reduxapi/Common/Selectors';

const useAuthHook = () => {
  const authData = useSelector(selectUserInfo);

  const isComprehensiveMode = authData.get('warningMode', '') === 'comprehensive';

  const userId = authData.get('_id', '');

  const userType = authData.get('user_type', '');

  const isStudent = authData.get('user_type', '') === 'student';

  return {
    isComprehensiveMode,
    userId,
    userType,
    isStudent,
  };
};

export default useAuthHook;
