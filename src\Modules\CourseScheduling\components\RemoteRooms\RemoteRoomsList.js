import React, { useState } from 'react';
import PropTypes from 'prop-types';
import Button from '@mui/material/Button';
import { ThemeProvider } from '@mui/styles';
import { Dropdown, Table } from 'react-bootstrap';
import { List, Map } from 'immutable';
import { validateIfRoomUpdated, validateRoom, getTrimmedValues } from './utils';
import AddEditRemoteRoomModal from '../../modal/AddEditRemoteRoomModal';
import AlertConfirmModal from '../../modal/AlertConfirmModal';
import { capitalize, MUI_THEME } from '../../../../utils';
import { CheckPermission } from '../../../../Modules/Shared/Permissions';
import { Trans } from 'react-i18next';
import { t } from 'i18next';

function RemoteRoomsList({ termName, level, programId, showMessage, saveRemoteRoom }) {
  const [showModal, setShowModal] = useState(false);
  const [mode, setMode] = useState('');
  const [activeRoom, setActiveRoom] = useState(Map());
  const [alertConfirmModalData, setAlertConfirmModalData] = useState({ show: false });

  const rooms = level.get('remote_schedule_data', List());

  function handleAddEditRoomClick(mode, room) {
    setMode(mode);
    setActiveRoom(room);
    setShowModal(true);
  }

  function handleCancelClick() {
    setMode('');
    setActiveRoom(Map());
    setShowModal(false);
  }

  function handleChange(name, value) {
    setActiveRoom(activeRoom.set(name, value));
  }

  function handleSaveClick() {
    let originalRoom = Map();
    let filteredRooms = rooms;
    const currentRoom = getTrimmedValues(activeRoom);
    const activeRoomId = activeRoom.get('_id');
    if (mode === 'update') {
      const roomIndex = rooms.findIndex((room) => room.get('_id') === activeRoomId);
      originalRoom = rooms.get(roomIndex, Map());
      filteredRooms = rooms.delete(roomIndex);
      if (validateIfRoomUpdated(originalRoom, currentRoom)) {
        return showMessage(t('infra_management.remote.error_msgs.no_changes_made'));
      }
    }
    const validationMessage = validateRoom(currentRoom, filteredRooms);
    if (validationMessage) {
      return showMessage(validationMessage);
    }
    const requestBody = {
      ...currentRoom.toJS(),
      term: termName,
      yearId: level.get('year_id'),
      yearName: level.get('year'),
      levelId: level.get('level_id'),
      levelName: level.get('level_name'),
    };
    saveRemoteRoom({
      mode,
      programId,
      ...(activeRoomId && { _id: activeRoomId }),
      requestBody,
      callback: handleCancelClick,
    });
  }

  function handleDeleteRoom(room, isConfirmed) {
    if (!isConfirmed) {
      setModalData({
        show: true,
        title: t('infra_management.remote.modal.delete.title'),
        body: (
          <div>
            {t('infra_management.remote.modal.delete.description') +
              `"${room.get('meetingTitle', '')}"?`}
          </div>
        ),
        variant: 'confirm',
        data: { data: room, operation: 'delete' },
        confirmButtonLabel: t('delete'),
        cancelButtonLabel: t('cancel'),
      });
      return;
    }
    saveRemoteRoom({
      mode: 'delete',
      programId,
      _id: room.get('_id'),
      callback: handleCancelClick,
    });
  }

  function onModalClose() {
    setAlertConfirmModalData({ show: false });
  }

  function onConfirm({ data, operation }) {
    setAlertConfirmModalData({ show: false });
    switch (operation) {
      case 'delete': {
        return handleDeleteRoom(data, true);
      }
      default:
        return;
    }
  }

  function setModalData({
    show,
    title,
    titleIcon,
    body,
    variant,
    confirmButtonLabel,
    cancelButtonLabel,
    data,
  }) {
    setAlertConfirmModalData({
      show,
      ...(title && { title }),
      ...(titleIcon && { titleIcon }),
      ...(body && { body }),
      ...(variant && { variant }),
      ...(confirmButtonLabel && { confirmButtonLabel }),
      ...(cancelButtonLabel && { cancelButtonLabel }),
      ...(data && { data }),
    });
  }

  return (
    <>
      <div className="bg-white border-radious-8">
        <div className="p-3">
          <div className="d-flex justify-content-between pb-3">
            <p className="bold mt-2 mb-0 f-17">
              <Trans i18nKey={'infra_management.remote.remote_room_link'}></Trans>
            </p>
            {CheckPermission(
              'subTabs',
              'Infrastructure Management',
              'Remote',
              '',
              'Level List',
              '',
              'Room Details',
              'Add'
            ) && (
              <div className="bold mb-0 f-16">
                <ThemeProvider theme={MUI_THEME}>
                  <Button
                    color="primary"
                    variant="contained"
                    onClick={() => handleAddEditRoomClick('create', Map())}
                  >
                    <Trans i18nKey={'infra_management.remote.add_room'}></Trans>
                  </Button>
                </ThemeProvider>
              </div>
            )}
          </div>
          {rooms.isEmpty() ? (
            <div className="pt-4 border-top d-flex justify-content-center align-items-center color-light-gray">
              <i className="fa fa-exclamation-circle" aria-hidden="true"></i>
              <p className="ml-2 mb-0">
                <Trans i18nKey={'infra_management.remote.error_msgs.no_room_created'}></Trans>
              </p>
            </div>
          ) : (
            <div className="min_h pt-2">
              <Table hover>
                <thead className="bg-white thead_border">
                  <tr>
                    <th>
                      <Trans
                        i18nKey={
                          'infra_management.remote.remote_rooms_list_table_headers.meetingTitle'
                        }
                      ></Trans>
                    </th>
                    <th>
                      <Trans
                        i18nKey={'infra_management.remote.remote_rooms_list_table_headers.gender'}
                      ></Trans>
                    </th>
                    <th>
                      <Trans
                        i18nKey={'infra_management.remote.remote_rooms_list_table_headers.app'}
                      ></Trans>
                    </th>
                    <th>
                      <Trans
                        i18nKey={
                          'infra_management.remote.remote_rooms_list_table_headers.meetingUrl'
                        }
                      ></Trans>
                    </th>
                    <th>
                      <Trans
                        i18nKey={
                          'infra_management.remote.remote_rooms_list_table_headers.meetingId'
                        }
                      ></Trans>
                    </th>
                    <th>
                      <Trans
                        i18nKey={
                          'infra_management.remote.remote_rooms_list_table_headers.meetingUsername'
                        }
                      ></Trans>
                    </th>
                    <th>
                      <Trans
                        i18nKey={'infra_management.remote.remote_rooms_list_table_headers.passCode'}
                      ></Trans>
                    </th>
                    <th>
                      <Trans
                        i18nKey={
                          'infra_management.remote.remote_rooms_list_table_headers.associatedEmail'
                        }
                      ></Trans>
                    </th>
                    <th>
                      <Trans
                        i18nKey={'infra_management.remote.remote_rooms_list_table_headers.password'}
                      ></Trans>
                    </th>
                    <th> </th>
                  </tr>
                </thead>
                <tbody>
                  {rooms.map((room) => (
                    <tr key={room.get('_id')}>
                      <td>
                        <div className="pt-1">{room.get('meetingTitle', '')}</div>
                      </td>
                      <td>
                        <div className="pt-1">{capitalize(room.get('gender', ''))}</div>
                      </td>
                      <td>
                        <div className="pt-1">
                          {room.get('remotePlatform', '') === 'zoom' ? 'Zoom' : 'Microsoft Teams'}
                        </div>
                      </td>
                      <td>
                        <div className="pt-1 text-skyblue cursor-pointer">
                          {room.get('meetingUrl', '') ? (
                            <a
                              href={room.get('meetingUrl')}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {room.get('meetingUrl')}
                            </a>
                          ) : (
                            '---'
                          )}
                        </div>
                      </td>
                      <td>
                        <div className="pt-1">{room.get('meetingId', '---')}</div>
                      </td>
                      <td>
                        <div className="pt-1">{room.get('meetingUsername', '---')}</div>
                      </td>
                      <td>
                        <div className="pt-1">{room.get('passCode', '---')}</div>
                      </td>
                      <td>
                        <div className="pt-1">{room.get('associatedEmail', '---')}</div>
                      </td>
                      <td>
                        <div className="pt-1">{room.get('password', '---')}</div>
                      </td>
                      <td>
                        {(CheckPermission(
                          'subTabs',
                          'Infrastructure Management',
                          'Remote',
                          '',
                          'Level List',
                          '',
                          'Room Details',
                          'Edit'
                        ) ||
                          CheckPermission(
                            'subTabs',
                            'Infrastructure Management',
                            'Remote',
                            '',
                            'Level List',
                            '',
                            'Room Details',
                            'Delete'
                          )) && (
                          <div className="pt-1">
                            <small className="f-18">
                              <Dropdown>
                                <Dropdown.Toggle
                                  variant=""
                                  id="dropdown-table"
                                  className="table-dropdown"
                                  size="sm"
                                >
                                  <div className="f-16">
                                    <i className="fa fa-ellipsis-v" aria-hidden="true"></i>
                                  </div>
                                </Dropdown.Toggle>
                                <Dropdown.Menu renderOnMount={false}>
                                  {CheckPermission(
                                    'subTabs',
                                    'Infrastructure Management',
                                    'Remote',
                                    '',
                                    'Level List',
                                    '',
                                    'Room Details',
                                    'Edit'
                                  ) && (
                                    <Dropdown.Item
                                      onClick={() => handleAddEditRoomClick('update', room)}
                                    >
                                      <Trans i18nKey={'edit'}></Trans>
                                    </Dropdown.Item>
                                  )}
                                  {CheckPermission(
                                    'subTabs',
                                    'Infrastructure Management',
                                    'Remote',
                                    '',
                                    'Level List',
                                    '',
                                    'Room Details',
                                    'Delete'
                                  ) && (
                                    <Dropdown.Item onClick={() => handleDeleteRoom(room, false)}>
                                      <Trans i18nKey={'delete'}></Trans>
                                    </Dropdown.Item>
                                  )}
                                </Dropdown.Menu>
                              </Dropdown>
                            </small>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </div>
      </div>
      <AddEditRemoteRoomModal
        show={showModal}
        mode={mode}
        data={activeRoom}
        onHide={handleCancelClick}
        onSave={handleSaveClick}
        onChange={handleChange}
      />
      <AlertConfirmModal
        show={alertConfirmModalData.show}
        title={alertConfirmModalData.title || ''}
        titleIcon={alertConfirmModalData.titleIcon}
        body={alertConfirmModalData.body || ''}
        variant={alertConfirmModalData.variant || 'confirm'}
        confirmButtonLabel={alertConfirmModalData.confirmButtonLabel || 'YES'}
        cancelButtonLabel={alertConfirmModalData.cancelButtonLabel || 'NO'}
        onClose={onModalClose}
        onConfirm={onConfirm}
        data={alertConfirmModalData.data}
      />
    </>
  );
}

RemoteRoomsList.propTypes = {
  termName: PropTypes.string,
  level: PropTypes.instanceOf(Map),
  programId: PropTypes.string,
  showMessage: PropTypes.func,
  saveRemoteRoom: PropTypes.func,
};

export default RemoteRoomsList;
