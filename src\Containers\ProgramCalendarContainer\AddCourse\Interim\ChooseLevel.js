import React, { Fragment, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { BlockWrapper, FlexWrapper, Label, Input } from '../../Styled';
import { indVerRename, levelRename, ucFirst } from '../../../../utils';
import { Trans } from 'react-i18next';

const ChooseLevel = (props) => {
  const { data, active, currentData, getCourses, currentProgramCalendarId, programId } = props;
  const [nonRotation, setNonRotation] = data;
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (nonRotation.disable_items && nonRotation.title !== '' && count === 0) {
      let splitTitle = nonRotation.title.split('-');
      getCourses(currentProgramCalendarId, splitTitle[0], splitTitle[1]);
      setCount(1);
    }
  }, [nonRotation, count, setCount, currentProgramCalendarId, getCourses]);

  return (
    <Fragment>
      <BlockWrapper>
        <h6>
          <Trans
            i18nKey={'program_calendar.select_a_level'}
            values={{ level: indVerRename('Level', programId).toLowerCase() }}
          ></Trans>
        </h6>

        {currentData && currentData.length > 0 && (
          <>
            {currentData.map((current, activeIndex) => {
              let level_title =
                current.term !== '' ? ucFirst(current.term + ' - ' + current.level_no) : '';
              let titleName = current.term + '-' + current.level_no;
              let term = current.term;
              let level_no = current.level_no;
              let start_date = current.start_date !== undefined ? current.start_date : '';
              let end_date = current.end_date !== undefined ? current.end_date : '';

              return (
                <FlexWrapper mg="10px 0" key={activeIndex}>
                  {level_title !== '' && (
                    <>
                      <Input
                        type="radio"
                        name="title"
                        value={titleName}
                        id="level_one"
                        disabled={nonRotation.disable_items}
                        checked={nonRotation.title === titleName ? true : false}
                        onChange={(e) => {
                          getCourses(currentProgramCalendarId, term, level_no);
                          setNonRotation({
                            type: 'LEVEL_SELECT',
                            payload: e.target.value,
                            name: e.target.name,
                            start_date: start_date,
                            end_date: end_date,
                            year: active,
                          });
                        }}
                      />
                      <Label mg="0px" fs="18px" htmlFor="level_one">
                        {levelRename(level_title, programId)}
                      </Label>
                    </>
                  )}
                </FlexWrapper>
              );
            })}
          </>
        )}
      </BlockWrapper>
    </Fragment>
  );
};

ChooseLevel.propTypes = {
  data: PropTypes.object,
  currentData: PropTypes.array,
  getCourses: PropTypes.func,
  currentProgramCalendarId: PropTypes.string,
  programId: PropTypes.string,
  active: PropTypes.string,
};

export default ChooseLevel;
