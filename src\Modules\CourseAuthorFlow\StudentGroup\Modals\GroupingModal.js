import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormControlLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Typography,
} from '@mui/material';
import MButton from 'Widgets/FormElements/material/Button';
// import CloseIcon from '@mui/icons-material/Close';
import { fromJS, List, Map } from 'immutable';
// import MaterialInput from 'Widgets/FormElements/material/Input';
import { formatToTwoDigitString } from 'utils';
import DriveFileMoveRoundedIcon from '@mui/icons-material/DriveFileMoveRounded';
import { useCallGroupSettings, useCallProgramYearLevel } from '../Dashboard';
import {
  getTotalCompletedSession,
  groupStudents,
  resetMessage,
  setData,
} from '_reduxapi/studentGroupV2/action';
import { useDispatch, useSelector } from 'react-redux';
import ErrorIcon from '@mui/icons-material/Error';
import { getModalSubtitle } from './DeliveryGroupsModal';
import { getSelectedGroups } from './MoveGroupedModal';
import { selectActiveInstitutionCalendar } from '_reduxapi/Common/Selectors';
import { selectTotalCompletedSession } from '_reduxapi/studentGroupV2/selectors';

const titleSx = {
  padding: '12px 16px',
  color: '#374151',
  borderTop: '4px solid #0064C8',
};
const subTitleSx = {
  marginLeft: '8px',
  padding: '4px 8px',
  fontSize: '14px',
  borderRadius: '4px',
  backgroundColor: '#F3F4F6',
};
const selectedCountSx = {
  // marginRight: '38px',
  fontSize: '14px',
  color: '#6B7280',
};
const titleIconSx = {
  padding: '8px',
  backgroundColor: '#EFF9FB',
  borderRadius: '50%',
  marginRight: '16px',
  color: '#0064C8',
};
// const closeIconSx = {
//   position: 'absolute',
//   right: 8,
//   top: 16,
// };
const buttonGroupSx = {
  padding: '4px',
  border: '1px solid #E5E7EB',
  borderRadius: '8px',
  backgroundColor: '#F3F4F6',
};
const groupButtonSx = {
  padding: '4px 12px',
  border: 0,
  textTransform: 'none',
  borderRadius: '8px !important',
  color: '#4B5563',
  fontWeight: 'normal',
  '&.Mui-selected': {
    color: '#374151',
    backgroundColor: '#fff',
    fontWeight: 500,
    boxShadow: '0 1px 3px 0 #11182733',
    '&:hover': {
      backgroundColor: '#fff',
    },
  },
};
const deliveryGroupNameSx = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '7px 16px',
  fontSize: 12,
  backgroundColor: '#F9FAFB',
};
const groupSx = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '9px',
  fontSize: 14,
  color: '#374151',
};
const selectedLabelSx = {
  backgroundColor: '#F7FCFD',
  border: '1px solid #0064C8',
  borderRadius: '8px',
};
const formLabelSx = {
  margin: 0,
  '& .MuiFormControlLabel-label': {
    fontSize: 14,
    color: '#374151',
  },
};
const radioSx = {
  color: '#6B7280',
  '&.Mui-checked': {
    color: '#0064C8 !important',
  },
};
const errorIconSx = {
  marginLeft: '4px',
  fontSize: 16,
  color: '#DC2626',
};
const checkboxSx = {
  padding: 0,
  marginRight: '8px',
};
const buttonSx = {
  minWidth: 120,
  minHeight: 40,
};
const dialogSx = {
  '& .MuiDialog-paper': {
    maxWidth: '700px',
  },
};
const sessionsBoxSx = {
  marginTop: '24px',
  fontSize: 14,
  color: '#374151',
};
const sessionCountSx = {
  marginLeft: '4px',
  fontSize: 14,
  fontWeight: 500,
  color: '#DC2626',
};
const statusSelectSx = {
  '& .MuiSelect-select': {
    paddingTop: '12.5px',
    paddingBottom: '12.5px',
    color: '#6B7280',
  },
};
const menuItemSx = {
  padding: '8px 16px',
  color: '#6B7280',
};
const menuProps = {
  PaperProps: {
    sx: {
      maxHeight: 250,
    },
  },
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'left',
  },
  transformOrigin: {
    vertical: 'top',
    horizontal: 'left',
  },
};

const groupingMethodOptions = fromJS([
  { label: 'Alphabetical Order', value: 'alphabetical' },
  { label: 'Academic ID Order', value: 'academic' },
  {
    label: 'Clustering Method',
    secondaryText: 'Each group will have a mix of students with low, medium, and high marks.',
    value: 'cluster',
  },
  {
    label: 'Descending Marks Order',
    secondaryText: 'Students will be allocated in order from highest to lowest marks.',
    value: 'mark',
  },
]);

const statusOptions = fromJS([
  { label: 'Present', value: 'present', color: '#15803D' },
  { label: 'Absent', value: 'absent', color: '#DC2626' },
  { label: 'Exclude', value: 'exclude', color: '#374151' },
]);

const getSymbolAndName = (type) => {
  return `${type.get('deliverySymbol', '')} - ${type.get('deliveryType', '')}`;
};

const createOptions = (noOfGroups) => {
  return List([...Array(noOfGroups)])
    .map((_, i) => Map({ name: `Group ${i + 1}`, value: i + 1 }))
    .unshift(Map({ name: '- Select -', value: '' }));
};

const getSelectedOption = (symbol, selectedGroups) => {
  if (!selectedGroups.has(symbol)) return '';
  const groupNoList = selectedGroups.get(symbol);
  return groupNoList.size === 1 && groupNoList.first() ? groupNoList.first() : '';
};

export const getProcessedGroups = (groups, selectedGroups = Map()) => {
  const deliveryTypes = groups.get('deliveryTypes', List());
  const processedGroups = deliveryTypes.map((group) =>
    group.update('selectedType', List(), (types) =>
      types.map((type) => {
        const noOfGroups = type.get('noOfGroups', 0);
        const symbol = type.get('deliverySymbol', '');
        const options = createOptions(noOfGroups);
        const selectedOption = getSelectedOption(symbol, selectedGroups);
        const isDisabled = noOfGroups === 1 && selectedOption === 1;
        return type.merge(
          Map({ options, selectedOption, isDisabled, assignedGroup: selectedOption })
        );
      })
    )
  );
  return processedGroups;
};

const filterUngrouped = (types) => types.filter((type) => !type.get('isGrouped'));

const constructDeliveryTypes = ({ groupingType, deliveryGroups }) => {
  const deliveryTypes = deliveryGroups.get(groupingType, List());
  if (groupingType === 'auto') {
    return deliveryTypes.flatMap((group) =>
      group
        .get('selectedType', List())
        .filter((type) => type.get('checked'))
        .map((type) => type.get('deliveryType', ''))
    );
  }

  return constructManualGroups(deliveryTypes);
};

export const constructManualGroups = (deliveryTypes) => {
  return deliveryTypes.flatMap((group) =>
    group
      .get('selectedType', List())
      .filter((type) => type.get('checked'))
      .map((type) => {
        return Map({
          deliveryType: type.get('deliveryType', ''),
          groupNo: type.get('selectedOption', ''),
        });
      })
  );
};

const validateGroups = ({ groupingType, deliveryTypes, totalCompletedSession, status }) => {
  if (!deliveryTypes.size) return 'Select groups to move';

  if (groupingType === 'manual') {
    const isEmptyGroupNo = deliveryTypes.some((type) => !type.get('groupNo'));
    if (isEmptyGroupNo) return 'Please select group for each selected delivery groups';

    if (totalCompletedSession && !status) return 'Select status';
  }

  return '';
};

const getCompletedSessionCacheKey = (type) => {
  return `${type.get('deliverySymbol', '')}+${type.get('selectedOption', '')}`;
};

export const getCompletedSessionCount = (deliveryTypes, completedSession) => {
  let total = 0;
  deliveryTypes.forEach((group) =>
    group
      .get('selectedType', List())
      .filter((type) => type.get('checked') && type.get('selectedOption'))
      .forEach((type) => {
        const cacheKey = getCompletedSessionCacheKey(type);
        total += completedSession.get(cacheKey, 0);
      })
  );

  return total;
};

export const useCompletedSessions = ({ selectedData, genderType }) => {
  const dispatch = useDispatch();
  const activeInstitutionCalendar = useSelector(selectActiveInstitutionCalendar);
  const totalCompletedSession = useSelector(selectTotalCompletedSession);

  const courseIds = useMemo(() => {
    const selectedType = selectedData.get('type');
    if (selectedType === 'course') {
      const courseId = selectedData.getIn(['selectedCourse', '_course_id']);
      return List([courseId]);
    }

    if (selectedType === 'year') {
      const levels = selectedData.get('level', List());
      return levels.flatMap((level) =>
        level.get('course', List()).map((course) => course.get('_course_id', ''))
      );
    }

    const courses = selectedData.getIn(['selectedLevel', 'course'], List());
    return courses.map((course) => course.get('_course_id', ''));
  }, [selectedData]);

  // const constructGroupsWithSymbol = (deliveryTypes) => {
  //   return deliveryTypes.flatMap((group) =>
  //     group
  //       .get('selectedType', List())
  //       .filter((type) => type.get('checked') && type.get('selectedOption'))
  //       .map((type) => {
  //         return Map({
  //           deliverySymbol: type.get('deliverySymbol', ''),
  //           groupNo: type.get('selectedOption', ''),
  //         });
  //       })
  //   );
  // };

  const fetchCompletedSessionsCount = (group) => {
    // const deliveryTypes = constructGroupsWithSymbol(group);
    // if (!deliveryTypes.size) return;

    const selectedOption = group.get('selectedOption');
    const isAssigned = selectedOption === group.get('assignedGroup');
    if (!group.get('checked') || !selectedOption || isAssigned) return;

    const cacheKey = getCompletedSessionCacheKey(group);
    if (totalCompletedSession.has(cacheKey)) return;

    const level = selectedData.getIn(['selectedLevel', 'level_no']);
    const deliverySymbol = group.get('deliverySymbol', '');
    const groupNo = group.get('selectedOption', '');
    const requestBody = {
      institutionCalendarId: activeInstitutionCalendar.get('_id'),
      programId: selectedData.get('programId'),
      term: selectedData.get('term'),
      gender: genderType,
      year: selectedData.get('year'),
      ...(level && { level }),
      courseIds,
      selectedDeliveryType: [{ deliverySymbol, groupNo }],
    };

    dispatch(getTotalCompletedSession({ requestBody, cacheKey }));
  };

  return [totalCompletedSession, fetchCompletedSessionsCount];
};

const MuiRadio = ({ label, value, isSelected }) => {
  return (
    <FormControlLabel
      value={value}
      control={<Radio sx={radioSx} size="small" />}
      label={label}
      sx={{ ...formLabelSx, ...(isSelected && selectedLabelSx) }}
    />
  );
};
MuiRadio.propTypes = {
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  value: PropTypes.string,
  isSelected: PropTypes.bool,
};

const StatusSelect = ({ value, changed }) => {
  return (
    <Select
      size="small"
      value={value}
      onChange={changed}
      displayEmpty
      fullWidth
      sx={statusSelectSx}
    >
      <MenuItem value="" disabled hidden>
        - Select status -
      </MenuItem>
      {statusOptions.map((option) => {
        const label = option.get('label', '');
        const value = option.get('value', '');
        const color = option.get('color', '');
        return (
          <MenuItem key={value} sx={menuItemSx} value={value}>
            Mark as{' '}
            <Box component="span" className="ml-1 bold" sx={{ color }}>
              {label}
            </Box>
          </MenuItem>
        );
      })}
    </Select>
  );
};
StatusSelect.propTypes = {
  value: PropTypes.string,
  changed: PropTypes.func,
};

const CompletedSessionsCount = ({ count }) => {
  if (!count) return null;
  return (
    <div className="d-flex align-items-center f-14 ml-4 pl-1">
      {/* <ErrorIcon sx={errorIconSx} /> */}
      <Box display="flex" alignItems="center" color="#374151">
        Completed Sessions: <Typography sx={sessionCountSx}>{count}</Typography>
      </Box>
    </div>
  );
};
CompletedSessionsCount.propTypes = {
  count: PropTypes.number,
};

const CompletedSessionsStatus = ({ status, handleStatus }) => {
  return (
    <Box sx={sessionsBoxSx}>
      <p className="mb-1">
        <Box component="span" sx={{ color: '#DC2626' }}>
          *
        </Box>
        Select student attendance status for previously completed sessions
      </p>
      <StatusSelect value={status} changed={handleStatus} />
    </Box>
  );
};
CompletedSessionsStatus.propTypes = {
  status: PropTypes.string,
  handleStatus: PropTypes.func,
};

const MaterialInput = ({
  size,
  elementConfig,
  assignedGroup,
  value,
  changed,
  disabled,
  MenuProps,
  displayEmpty,
}) => {
  const renderValue = () => {
    const selectedOption = elementConfig.find((option) => option.get('value') === value);
    return selectedOption ? selectedOption.get('name', '') : '- Select -';
  };

  return (
    <FormControl fullWidth size={size}>
      <Select
        value={value}
        onChange={changed}
        disabled={disabled}
        MenuProps={MenuProps}
        displayEmpty={displayEmpty}
        renderValue={renderValue}
      >
        {elementConfig.map((option) => {
          const value = option.get('value', '');
          const name = option.get('name', '');
          const isDisabled = !!assignedGroup && value === assignedGroup;
          return (
            <MenuItem key={value} value={value} disabled={isDisabled}>
              {name} {isDisabled && <span className="ml-2 font-italic">(Already grouped)</span>}
            </MenuItem>
          );
        })}
      </Select>
    </FormControl>
  );
};
MaterialInput.propTypes = {
  size: PropTypes.string,
  elementConfig: PropTypes.instanceOf(List),
  assignedGroup: PropTypes.number,
  value: PropTypes.number,
  changed: PropTypes.func,
  disabled: PropTypes.bool,
  MenuProps: PropTypes.object,
  displayEmpty: PropTypes.bool,
};

export const ManualGrouping = ({
  deliveryGroups,
  handleChange,
  completedSession,
  totalCompletedSession,
  status,
  handleStatus,
}) => {
  return (
    <>
      {deliveryGroups.map((item, index) =>
        item.get('selectedType', List()).map((type, typeIndex) => {
          const cacheKey = getCompletedSessionCacheKey(type);
          return (
            <div key={`${index}${typeIndex}`} className="mb-3">
              <p
                className={`f-14 ml-4 pl-1 ${
                  type.get('checked') ? 'text-light-grey' : 'text-NewLightgray'
                }`}
              >
                {type.get('deliveryType', '')}
              </p>
              <div className="d-flex align-items-center">
                <Checkbox
                  sx={{ ...checkboxSx, mb: '5px' }}
                  size="small"
                  checked={type.get('checked', false)}
                  onChange={handleChange('checked', index, typeIndex)}
                  disabled={type.get('isDisabled', false)}
                />
                <div className="flex-grow-1">
                  <MaterialInput
                    size="small"
                    elementConfig={type.get('options', List())}
                    assignedGroup={type.get('assignedGroup', '')}
                    value={type.get('selectedOption', '')}
                    changed={handleChange('selectedOption', index, typeIndex)}
                    disabled={!type.get('checked', false)}
                    MenuProps={menuProps}
                    displayEmpty
                  />
                </div>
              </div>

              {type.get('checked') && (
                <CompletedSessionsCount count={completedSession.get(cacheKey, 0)} />
              )}
            </div>
          );
        })
      )}

      {totalCompletedSession > 0 && (
        <CompletedSessionsStatus status={status} handleStatus={handleStatus} />
      )}
    </>
  );
};
ManualGrouping.propTypes = {
  deliveryGroups: PropTypes.instanceOf(List),
  handleChange: PropTypes.func,
  completedSession: PropTypes.instanceOf(Map),
  totalCompletedSession: PropTypes.number,
  status: PropTypes.string,
  handleStatus: PropTypes.func,
};

const GroupingModal = ({ open, data, groupingData, handleClose, refreshStudents }) => {
  const dispatch = useDispatch();
  const { groupSettings, callGroupSettings } = useCallGroupSettings({
    selectedData: data,
  });
  const [fetchProgramYearLevel] = useCallProgramYearLevel({ filters: data });
  const { genderType, selectedStudents, isAllStudent } = groupingData;
  const [completedSession, fetchCompletedSessionsCount] = useCompletedSessions({
    selectedData: data,
    genderType,
  });
  const [groupingType, setGroupingType] = useState('auto');
  const [groupingMethod, setGroupingMethod] = useState('alphabetical');
  const [deliveryGroups, setDeliveryGroups] = useState(Map());
  const [status, setStatus] = useState('');
  const selectedCount = selectedStudents.size;
  const isAutoDisabled = deliveryGroups.get('auto', List()).isEmpty();

  useEffect(() => {
    if (groupSettings.isEmpty()) callGroupSettings();
    return () => dispatch(setData(fromJS({ totalCompletedSession: {} })));
  }, []);

  useEffect(() => {
    const deliveryGroups = groupSettings.get('deliveryGroups');
    if (!deliveryGroups) return;

    const genderGroup = deliveryGroups.find((group) => group.get('gender') === genderType);
    if (!genderGroup) return;

    const selectedGroups = getSelectedGroups(selectedStudents);
    const processedGroups = getProcessedGroups(genderGroup, selectedGroups);
    const filteredGroups = processedGroups
      .filter((item) => filterUngrouped(item.get('selectedType', List())).size)
      .map((item) => item.update('selectedType', List(), filterUngrouped));

    if (!filteredGroups.size) setGroupingType('manual');
    setDeliveryGroups(
      Map({ ...(filteredGroups.size && { auto: processedGroups }), manual: processedGroups })
    );
  }, [groupSettings, genderType, selectedStudents]);

  const modalSubTile = useMemo(() => getModalSubtitle(data), []);

  const totalCompletedSession = useMemo(() => {
    if (groupingType === 'auto' || completedSession.isEmpty()) return 0;

    const deliveryTypes = deliveryGroups.get(groupingType, List());
    return getCompletedSessionCount(deliveryTypes, completedSession);
  }, [groupingType, deliveryGroups, completedSession]);

  useEffect(() => {
    if (!totalCompletedSession) setStatus('');
  }, [totalCompletedSession]);

  const handleGroupType = (e) => {
    const value = e.target.value;
    if (isAutoDisabled && value === 'auto') return;
    setGroupingType(value);
  };

  const handleChange = (key, index, typeIndex) => (e) => {
    const value = key === 'checked' ? e.target.checked : e.target.value;
    const updatePath = [groupingType, index, 'selectedType', typeIndex];
    const updatedData = deliveryGroups.setIn([...updatePath, key], value);
    setDeliveryGroups(updatedData);

    if (groupingType === 'manual') fetchCompletedSessionsCount(updatedData.getIn(updatePath));
  };

  const handleSave = () => {
    const deliveryTypes = constructDeliveryTypes({ groupingType, deliveryGroups });
    const errorMsg = validateGroups({ groupingType, deliveryTypes, totalCompletedSession, status });
    if (errorMsg) return dispatch(resetMessage(errorMsg));

    const groupSettingId = groupSettings.get('_id', '');
    const requestBody = Map({
      groupSettingId,
      genderType,
      isAllStudent,
      ...(!isAllStudent && { studentIds: selectedStudents.map((s) => s.get('userId')) }),
      groupingType,
      ...(groupingType === 'auto'
        ? { groupingMethod, deliveryTypes }
        : {
            selectedDeliveryType: deliveryTypes,
            ...(totalCompletedSession && { studentAttendanceStatus: status }),
          }),
    });

    dispatch(
      groupStudents({
        requestBody,
        selectedCount,
        callback: () => {
          fetchProgramYearLevel();
          callGroupSettings();
          refreshStudents();
          handleClose();
        },
      })
    );
  };

  return (
    <Dialog open={open} sx={dialogSx} fullWidth>
      <DialogTitle className="border-bottom" sx={titleSx}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center">
            <Box display="flex" sx={titleIconSx}>
              <DriveFileMoveRoundedIcon />
            </Box>
            Choose Grouping Method
            <Typography sx={subTitleSx}>{modalSubTile}</Typography>
          </Box>
          <Typography sx={selectedCountSx}>
            Selected: <span className="bold">{formatToTwoDigitString(selectedCount)}</span>
          </Typography>
        </Box>
      </DialogTitle>
      {/* <IconButton aria-label="close" onClick={handleClose} sx={closeIconSx}>
        <CloseIcon />
      </IconButton> */}
      <DialogContent className="p-3">
        <div className="mb-4">
          <ToggleButtonGroup
            value={groupingType}
            onChange={handleGroupType}
            sx={buttonGroupSx}
            exclusive
            fullWidth
          >
            <ToggleButton value="auto" sx={groupButtonSx}>
              Auto-Grouping
              {isAutoDisabled && (
                <Tooltip
                  title="Auto grouping disabled since students already grouped to all groups"
                  placement="top"
                  arrow
                >
                  <ErrorIcon sx={errorIconSx} onClick={(e) => e.stopPropagation()} />
                </Tooltip>
              )}
            </ToggleButton>
            <ToggleButton value="manual" sx={groupButtonSx}>
              Manual-Grouping
            </ToggleButton>
          </ToggleButtonGroup>
        </div>

        {groupingType === 'auto' && (
          <div>
            <div className="mb-3">
              <p className="f-14 bold gray-neutral mb-1">
                Choose a method:<span className="text-danger">*</span>
              </p>
              <RadioGroup
                value={groupingMethod}
                onChange={(e) => setGroupingMethod(e.target.value)}
              >
                {groupingMethodOptions.map((item, index) => (
                  <MuiRadio
                    key={index}
                    label={
                      <>
                        {item.get('label', '')}
                        {item.get('secondaryText') && (
                          <span className="text-muted"> ({item.get('secondaryText')})</span>
                        )}
                      </>
                    }
                    value={item.get('value', '')}
                    isSelected={groupingMethod === item.get('value')}
                  />
                ))}
              </RadioGroup>
            </div>

            <div className="mb-2">
              <p className="f-14 bold gray-neutral mb-1">
                Choose groups to move:<span className="text-danger">*</span>
              </p>
              {deliveryGroups.get('auto', List()).map((item, index) => {
                const types = item.get('selectedType', List());
                const selected = types.filter((type) => type.get('checked'));
                return (
                  <div key={index}>
                    <Box sx={deliveryGroupNameSx}>
                      <p className="text-uppercase">{item.get('typeName', '')}</p>
                      <p>
                        {selected.size}/{types.size}
                      </p>
                    </Box>
                    {item.get('selectedType', List()).map((type, typeIndex) => (
                      <Box
                        key={`${index}${typeIndex}`}
                        sx={{ ...groupSx, ...(type.get('checked') && selectedLabelSx) }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox sx={{ ...checkboxSx, color: '#6B7280' }} size="small" />
                          }
                          label={getSymbolAndName(type)}
                          checked={type.get('checked', false)}
                          onChange={handleChange('checked', index, typeIndex)}
                          sx={formLabelSx}
                          disabled={type.get('isGrouped', false)}
                        />
                        <p>({type.get('noOfGroups', 0)})</p>
                      </Box>
                    ))}
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {groupingType === 'manual' && (
          <ManualGrouping
            deliveryGroups={deliveryGroups.get('manual', List())}
            handleChange={handleChange}
            completedSession={completedSession}
            totalCompletedSession={totalCompletedSession}
            status={status}
            handleStatus={(e) => setStatus(e.target.value)}
          />
        )}
      </DialogContent>
      <DialogActions className="p-3 border-top">
        <MButton variant="outlined" color="gray" clicked={handleClose} sx={buttonSx}>
          Cancel
        </MButton>
        <MButton variant="contained" color="primary" clicked={handleSave} sx={buttonSx}>
          Group
        </MButton>
      </DialogActions>
    </Dialog>
  );
};
GroupingModal.propTypes = {
  open: PropTypes.bool,
  data: PropTypes.instanceOf(Map),
  groupingData: PropTypes.object,
  handleClose: PropTypes.func,
  refreshStudents: PropTypes.func,
};

export default GroupingModal;
