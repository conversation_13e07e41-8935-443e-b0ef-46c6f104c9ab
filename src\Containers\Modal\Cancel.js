import React from 'react';
import PropTypes from 'prop-types';
import { Trans } from 'react-i18next';
import { useStylesFunction } from 'Modules/Styles/Utils';
import Button from 'Widgets/FormElements/material/Button';
import Modal from '@mui/material/Modal';
import Backdrop from '@mui/material/Backdrop';
import Fade from '@mui/material/Fade';
function Cancel({ showCancel, setCancel, setShow }) {
  const classes = useStylesFunction();

  return (
    <Modal
      aria-labelledby="transition-modal-title"
      aria-describedby="transition-modal-description"
      className={classes.modal}
      open={showCancel}
      onClose={() => setCancel(false)}
      closeAfterTransition
      BackdropComponent={Backdrop}
      BackdropProps={{
        timeout: 500,
      }}
    >
      <Fade in={showCancel}>
        <div className={classes.paper}>
          <p className="mb-3 f-22">
            <Trans i18nKey={'Cancel'}></Trans>{' '}
          </p>

          <div className="mb-3 f-16">
            <Trans i18nKey={'confirm_cancel'}></Trans>
          </div>
          <div className="f-16 mb-2">
            <Trans i18nKey={'changed_not_saved'}></Trans>
          </div>

          <div className="d-flex justify-content-end mt-3">
            <Button
              className="mr-3"
              color="inherit"
              variant="outlined"
              clicked={() => setCancel(false)}
            >
              <Trans i18nKey={'add_colleges.no'}></Trans>
            </Button>
            <Button
              clicked={() => {
                setCancel(false);
                setShow(false);
              }}
            >
              <Trans i18nKey={'add_colleges.yes_cancel'}></Trans>
            </Button>
          </div>
        </div>
      </Fade>
    </Modal>
  );
}

Cancel.propTypes = {
  showCancel: PropTypes.bool,
  setCancel: PropTypes.func,
  setShow: PropTypes.func,
};
export default Cancel;
