import React, { Component } from 'react';
import { connect } from 'react-redux';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { format } from 'date-fns';
import { compose } from 'redux';
import { List, Map } from 'immutable';
import PropTypes from 'prop-types';
import { Dropdown, <PERSON><PERSON>, Badge } from 'react-bootstrap';
import moment from 'moment';
import { t } from 'i18next';
import AlertModal from '../../InfrastructureManagement/modal/AlertModal';
import { capitalize } from '../../InfrastructureManagement/utils';
import * as actions from '../../../_reduxapi/leave_management/actions';
import {
  selectMyLeaveList,
  selectLeaveCategories,
  selectPermissionList,
  selectLeaveOverview,
  selectPermissionOverview,
} from '../../../_reduxapi/leave_management/selectors';
import {
  selectUserId,
  selectUserType,
  selectInstitutionCalendar,
  selectActiveInstitutionCalendar,
} from '../../../_reduxapi/Common/Selectors';
import '../../../Assets/css/leave_management.css';
import ApproveConfirmModal from '../../LeaveManagement/modal/ApproveConfirmModal';
import { CheckPermission } from '../../../Modules/Shared/Permissions';
import Pagination from '../../StudentGrouping/components/Pagination';
import LocalStorageService from 'LocalStorageService';

const TABS = ['Permission', 'Leave', 'On Duty'];
const LEAVE_TYPE = {
  0: 'permission',
  1: 'leave',
  2: 'on_duty',
};
var totalPages;
class MyLeaves extends Component {
  constructor() {
    super();
    this.state = {
      activeTab: 0,
      modalData: { show: false },
      isFetched: false,
      show: false,
      status: null,
      showReject: false,
      leaveList: null,
      pageCount: 10,
      currentPage: 1,
    };
  }

  componentDidMount() {
    this.props.getAllPermissionList('staff');
    if (this.props.location.state) {
      this.indexTrigger(this.props.location.state.activeTab);
    } else {
      let pathName = this.props.location.pathname.split('/');
      if (pathName[2] !== undefined) {
        let findValue = TABS.filter(
          (item) =>
            CheckPermission('tabs', 'Leave Management', 'Staff Leave', '', item, 'View') === true ||
            CheckPermission('tabs', 'Leave Management', 'Student Leave', '', item, 'View') === true
        );
        if (findValue && findValue.length > 0) {
          let findIndex = TABS.findIndex((item) => item === findValue[0]);
          this.indexTrigger(findIndex > -1 ? findIndex : this.state.activeTab);
        }
      }
    }
    this.props.getLeaveCategories();
    this.props.setBreadCrumbName(t('side_nav.menus.Faculty_Academic_Accountability_Management'));
  }

  indexTrigger = (index) => {
    this.interval = setInterval(() => {
      const { activeInstitutionCalendar } = this.props;
      if (activeInstitutionCalendar.get('_id', null) !== null) {
        this.onTabChange(index);
        clearInterval(this.interval);
      }
    }, 500);
  };

  componentDidUpdate(prevProps) {
    if (
      this.props.activeInstitutionCalendar.get('_id') !==
      prevProps.activeInstitutionCalendar.get('_id')
    ) {
      this.fetchMyLeaveList();
    }
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  fetchMyLeaveList() {
    this.props.getMyLeaveList({
      institutionCalendarId: this.props.activeInstitutionCalendar.get('_id'),
      userId: this.props.userId,
      type: LEAVE_TYPE[this.state.activeTab],
    });
  }

  onTabChange(index) {
    this.setState(
      {
        activeTab: index,
        pageCount: 10,
        currentPage: 1,
      },
      () => {
        this.fetchMyLeaveList();
        if (LEAVE_TYPE[this.state.activeTab] === 'permission') {
          this.props.getPermissionOverview({
            userId: this.props.userId,
            userType: 'staff',
            type: LEAVE_TYPE[this.state.activeTab],
            institutionCalendarId: this.props.activeInstitutionCalendar.get('_id'),
          });
          LocalStorageService.setCustomToken('activeLeaveid', this.state.activeTab);
        } else {
          this.props.getLeaveOverview({
            userId: this.props.userId,
            userType: 'staff',
            type: LEAVE_TYPE[this.state.activeTab],
            institutionCalendarId: this.props.activeInstitutionCalendar.get('_id'),
          });
          LocalStorageService.setCustomToken('activeLeaveid', this.state.activeTab);
        }
      }
    );
  }

  navigateToApplyLeave() {
    const type = this.getLeaveType();
    if (!type) return;
    this.props.history.push(`/leave-management/${type}/new`);
  }

  getLeaveType() {
    LocalStorageService.setCustomToken('activeLeaveid', this.state.activeTab);
    const { activeTab } = this.state;
    let type = '';
    if (LEAVE_TYPE[activeTab] === 'on_duty') type = 'on-duty';
    else if (LEAVE_TYPE[activeTab]) type = `${LEAVE_TYPE[activeTab]}`;
    return type;
  }

  editLeave(leave) {
    LocalStorageService.setCustomToken('activeLeaveid', this.state.activeTab);
    const type = this.getLeaveType();
    const id = leave.get('_id');
    if (!type || !id) return;
    this.props.history.push(`/leave-management/${type}/${id}`);
  }

  handleClick(status, data) {
    const datalist = data.toJS();
    if (data.get('review') === 'rejected') {
      this.setState({ show: true, status: status });
    }
    if (data.get('forwarded') === 'rejected') {
      this.setState({ show: true, status: status });
    }
    if (data.get('approval') === 'rejected') {
      this.setState({ show: true, status: status });
    }
    this.props.setLeaveData(datalist);
  }

  setModalData({ show, title, description, variant, confirmButtonLabel, cancelButtonLabel, data }) {
    this.setState({
      modalData: {
        show,
        ...(title && { title }),
        ...(description && { description }),
        ...(variant && { variant }),
        ...(confirmButtonLabel && { confirmButtonLabel }),
        ...(cancelButtonLabel && { cancelButtonLabel }),
        ...(data && { data }),
      },
    });
  }

  onModalClose() {
    this.setState({
      modalData: { show: false },
    });
  }

  onConfirm({ leave }) {
    this.setState(
      {
        modalData: { show: false },
      },
      () => this.cancelLeave(leave, true)
    );
  }

  cancelLeave(leave, confirmed) {
    const title =
      t(`leaveManagement.tabs.${TABS[this.state.activeTab]}`) || t(`leaveManagement.tabs.leave`);
    if (!confirmed) {
      this.setModalData({
        show: true,
        title: `${t('cancel')} ${title}`,
        description: `${t('leaveManagement.confirm_calcel_applied')} ${title.toLowerCase()}? ${
          title.toLowerCase() === 'leave' ? t('leaveManagement.edit_further') : ''
        }`,
        variant: 'confirm',
        confirmButtonLabel: t('confirm_upper'),
        cancelButtonLabel: t('cancel_upper'),
        data: { leave, title },
      });
      return;
    }
    this.props.cancelLeave(
      {
        id: leave.get('_id'),
        title,
        institutionCalendarId: this.props.activeInstitutionCalendar.get('_id'),
        userId: this.props.userId,
        type: LEAVE_TYPE[this.state.activeTab],
      },
      () => {
        const data = {
          userId: this.props.userId,
          userType: 'staff',
          type: LEAVE_TYPE[this.state.activeTab],
          institutionCalendarId: this.props.activeInstitutionCalendar.get('_id'),
        };
        this.props.getLeaveOverview(data);
        this.props.getPermissionOverview(data);
      }
    );
  }

  getCalendarIndex(calendarId) {
    return this.props.institutionCalendar.findIndex((c) => c.get('_id') === calendarId);
  }

  disableEditOrCancel(leave, type) {
    if (!leave.get('isActive')) return true;
    // const calendarIndex = this.getCalendarIndex(leave.get('_institution_calendar_id'));
    // if (calendarIndex === -1 || calendarIndex !== 0) return true;
    // if (isPast(new Date(leave.get('from')))) return true;
    if (type === 'edit' && leave.get('status') !== 'applied') return true;

    return false;
  }
  getleaveStatus(status, leave) {
    if (leave.get('status') === 'rejected') {
      this.setState({ showReject: status, leaveList: leave });
    }
  }

  onNextClick = () => {
    if (this.state.currentPage + 1 >= totalPages) {
      this.setState({
        currentPage: totalPages,
      });
    } else {
      this.setState({
        currentPage: this.state.currentPage + 1,
      });
    }
  };

  onFullLastClick = () => {
    this.setState({
      currentPage: 1,
    });
  };

  onFullForwardClick = () => {
    this.setState({
      currentPage: totalPages,
    });
  };

  onBackClick = () => {
    if (this.state.currentPage - 1 === 0) {
      this.setState({
        currentPage: 1,
      });
    } else {
      this.setState({
        currentPage: this.state.currentPage - 1,
      });
    }
  };

  pagination = (value) => {
    this.setState({
      pageCount: value,
      currentPage: 1,
    });
  };

  render() {
    const { activeTab, modalData, pageCount, currentPage } = this.state;
    const { myLeaveList, leaveOverview, permissionOverview } = this.props;
    const permissionOverviewlist = permissionOverview?.toJS();
    const myLeaveListSize = myLeaveList !== undefined ? myLeaveList.size : 0;
    totalPages =
      myLeaveListSize % pageCount === 0
        ? myLeaveListSize / pageCount
        : Math.floor(myLeaveListSize / pageCount) + 1;

    return (
      <React.Fragment>
        <div className="customize_tab">
          <ul id="menu">
            {TABS.map((tab, i) => {
              if (
                CheckPermission('tabs', 'Leave Management', 'Staff Leave', '', tab, 'View') ===
                  true ||
                CheckPermission('tabs', 'Leave Management', 'Student Leave', '', tab, 'View') ===
                  true
              ) {
                return (
                  <span
                    key={`${tab}-${i}`}
                    onClick={this.onTabChange.bind(this, i)}
                    className={`tabaligment${i === activeTab ? ' tabactive' : ''}`}
                  >
                    {t(`leaveManagement.tabs.${tab}`)}
                  </span>
                );
              } else {
                return <React.Fragment key={`${tab}-${i}`}></React.Fragment>;
              }
            })}
          </ul>
        </div>
        <div className="main pt-3 pb-5 bg-white">
          <div className="container">
            <div className="d-flex justify-content-between">
              <div className="d-flex mb-2 f-15 pt-2 align-items-baseline">
                <div className="no-wrap pr-3">
                  <b className="f-17">{`${t(`leaveManagement.tabs.${TABS[activeTab]}`)} ${t(
                    'add_colleges.overview'
                  ).toLocaleLowerCase()}`}</b>
                </div>
              </div>

              {CheckPermission(
                'tabs',
                'Leave Management',
                'Staff Leave',
                '',
                TABS[activeTab],
                'Add'
              ) && (
                <div>
                  <Button
                    variant="primary"
                    className="f-14"
                    onClick={this.navigateToApplyLeave.bind(this)}
                  >
                    {`${t('apply')} ${
                      LEAVE_TYPE[activeTab] === 'on_duty'
                        ? t('leaveManagement.tabs.On-Duty')
                        : LEAVE_TYPE[activeTab]
                        ? t(
                            `leaveManagement.tabs.${LEAVE_TYPE[activeTab].toLowerCase()}`
                          ).toUpperCase()
                        : ''
                    }`}
                  </Button>
                </div>
              )}
            </div>
            <div className="d-flex">
              <b className="pt-1 f-15 font-weight-normal pr-3">
                {LEAVE_TYPE[activeTab] === 'leave' && (
                  <React.Fragment>
                    {t('leaveManagement.Availed_Status', {
                      type: t('leaveManagement.tabs.Leave'),
                    })}
                  </React.Fragment>
                )}
                {LEAVE_TYPE[activeTab] === 'permission' && (
                  <React.Fragment>
                    {t('leaveManagement.Availed_Status', {
                      type: t('leaveManagement.tabs.Permission'),
                    })}
                  </React.Fragment>
                )}
                {LEAVE_TYPE[activeTab] === 'on_duty' && (
                  <React.Fragment>
                    {' '}
                    {t('leaveManagement.Availed_Status', {
                      type: t('leaveManagement.tabs.on_duty'),
                    })}
                  </React.Fragment>
                )}
              </b>
              <div>
                {LEAVE_TYPE[activeTab] === 'permission' && (
                  <b className="pr-2">
                    <Badge className="bg-lightgreen border-radious-8 f-15 p-2">
                      {/* {permissionOverviewlist?.[0]?.permission_taken} used out of{' '} */}
                      {permissionOverviewlist?.[0]?.permission_hours} {t('configuration.hours')} (
                      {permissionOverviewlist?.[0]?.permission_frequency} {t('dashboard_view.time')}{' '}
                      ) / {permissionOverviewlist?.[0]?.permission_frequency_by}
                    </Badge>
                  </b>
                )}

                {LEAVE_TYPE[activeTab] !== 'permission' &&
                  leaveOverview.map((overview) => (
                    <b key={overview.get('_id')} className="pr-2">
                      <Badge className="bg-lightgreen border-radious-8 f-15 p-2">
                        {`${overview.get('leave_type', '')} ${overview.get('no_of_days', '')} ${t(
                          'global_configuration.day_s'
                        )}/${
                          overview.get('entitlement', '') === 'na'
                            ? t('constant.na')
                            : overview.get('entitlement', '')
                        }`}
                      </Badge>
                    </b>
                  ))}
              </div>
            </div>
            <div className="row pt-4">
              <div className="col-md-12">
                <b className="f-15 d-flex">{`${t('leaveManagement.List_of_all')} ${
                  LEAVE_TYPE[activeTab] === 'on_duty'
                    ? t('leaveManagement.tabs.on_duty').toLocaleLowerCase()
                    : (
                        t(`leaveManagement.tabs.${LEAVE_TYPE[activeTab]}`) + t('leaveManagement.s')
                      ).toLocaleLowerCase()
                } ${t('leaveManagement.applied_and_taken')}`}</b>
                <div className="leaveManage mb-2">
                  <table className="table">
                    <thead className="group_table_top">
                      <tr>
                        <th className="border_color_blue">
                          <div className="aw-50">
                            <b>{t('s_no').toUpperCase()}</b>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div className="aw-100">
                            <div id="icon_space">
                              <li id="icon_space_li">
                                <b>{t('leaveManagement.Applied_on')}</b>
                              </li>
                            </div>
                          </div>
                        </th>
                        <th className="border_color_blue">
                          <div
                            className={LEAVE_TYPE[activeTab] === 'permission' ? 'aw-150' : 'aw-150'}
                          >
                            <div id="icon_space">
                              <li id="icon_space_li">
                                <b>
                                  {LEAVE_TYPE[activeTab] === 'permission'
                                    ? t('leaveManagement.Permission_date')
                                    : t('global_configuration.from')}
                                </b>
                              </li>
                            </div>
                          </div>
                        </th>
                        {LEAVE_TYPE[activeTab] !== 'permission' && (
                          <th className="border_color_blue">
                            <div className="aw-150">
                              <b>{t('program_calendar.to')}</b>
                            </div>
                          </th>
                        )}
                        {LEAVE_TYPE[activeTab] === 'permission' && (
                          <th className="border_color_blue">
                            <div className={'aw-150'}>
                              <b>{t('leaveManagement.Permission_Time')}</b>
                            </div>
                          </th>
                        )}

                        {LEAVE_TYPE[activeTab] !== 'permission' && (
                          <>
                            <th className="border_color_blue">
                              <div className={'aw-150'}>
                                <b>{t(`leaveManagement.Total_No_of_days`)}</b>
                              </div>
                            </th>
                            <th className="border_color_blue">
                              <div className="aw-150">
                                <b>
                                  {LEAVE_TYPE[activeTab] === 'on_duty'
                                    ? t('leaveManagement.tabs.on_duty')
                                    : t('leaveManagement.tabs.Leave')}{' '}
                                  {t('type').toLocaleLowerCase()}
                                </b>
                              </div>
                            </th>
                          </>
                        )}

                        <th className="border_color_blue">
                          <div className="aw-150">
                            <div id="icon_space">
                              <li id="icon_space_li">
                                <b>{t('status')}</b>
                              </li>
                            </div>
                          </div>
                        </th>

                        <th className="border_color_blue">
                          <div className="aw-50"></div>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="leaveManage-height" style={{ display: 'table' }}>
                      {myLeaveList.isEmpty() && (
                        <tr>
                          <td colSpan="13">{t('no_record_found')}</td>
                        </tr>
                      )}
                      {myLeaveList
                        .filter(
                          (data, i) =>
                            i >= pageCount * (currentPage - 1) && i < pageCount * currentPage
                        )
                        .map((leave, i) => (
                          <tr key={leave.get('_id')} className="tr-bottom-border">
                            <td>
                              <div className="aw-50">
                                <b>{i + 1}</b>
                              </div>
                            </td>
                            <td>
                              <div className="aw-100">
                                <b className="">
                                  {format(new Date(leave.get('createdAt')), 'd MMM yyyy')}
                                </b>
                              </div>
                            </td>
                            <td>
                              <div
                                className={
                                  LEAVE_TYPE[activeTab] === 'permission' ? 'aw-150' : 'aw-150'
                                }
                              >
                                <b className="">
                                  {format(new Date(leave.get('from')), 'd MMM yyyy')}
                                </b>
                              </div>
                            </td>
                            {LEAVE_TYPE[activeTab] !== 'permission' && (
                              <td>
                                <div className="aw-150">
                                  <b>{format(new Date(leave.get('to')), 'd MMM yyyy')}</b>
                                </div>
                              </td>
                            )}
                            {LEAVE_TYPE[activeTab] === 'permission' && (
                              <td>
                                <div className={'aw-150'}>
                                  <b>
                                    {moment(leave.get('from')).format('hh:mm A')}-
                                    {moment(leave.get('to')).format('hh:mm A')}
                                  </b>
                                </div>
                              </td>
                            )}

                            {LEAVE_TYPE[activeTab] !== 'permission' && (
                              <>
                                <td>
                                  <div className="aw-150">
                                    <b>{leave.get('days', 0)}</b>
                                  </div>
                                </td>
                                <td>
                                  <div className="aw-150">
                                    <b>{leave.getIn(['leave_type', 'name'], '')}</b>
                                  </div>
                                </td>
                              </>
                            )}

                            {leave.get('status') === 'rejected' && (
                              <Link>
                                <td>
                                  <div
                                    className="aw-150"
                                    onClick={() => this.getleaveStatus(true, leave)}
                                  >
                                    <b>{capitalize(leave.get('status'))}</b>
                                  </div>
                                </td>
                              </Link>
                            )}
                            {leave.get('status') !== 'rejected' && (
                              <td>
                                <div
                                  className="aw-150"
                                  onClick={() => this.getleaveStatus(true, leave)}
                                >
                                  <b>
                                    {capitalize(leave.get('status')) === 'Approve'
                                      ? t('leaveManagement.approved')
                                      : capitalize(leave.get('status'))}
                                  </b>
                                </div>
                              </td>
                            )}

                            <td>
                              <div className="aw-50">
                                {leave.get('status') !== 'rejected' &&
                                  (CheckPermission(
                                    'tabs',
                                    'Leave Management',
                                    'Staff Leave',
                                    '',
                                    TABS[activeTab],
                                    'Edit'
                                  ) ||
                                    CheckPermission(
                                      'tabs',
                                      'Leave Management',
                                      'Staff Leave',
                                      '',
                                      TABS[activeTab],
                                      'Delete'
                                    )) && (
                                    <div className="f-18">
                                      <Dropdown>
                                        <Dropdown.Toggle
                                          variant=""
                                          id="dropdown-table"
                                          className="table-dropdown"
                                          size="sm"
                                        >
                                          <div>
                                            <i className="fa fa-ellipsis-v" aria-hidden="true"></i>
                                          </div>
                                        </Dropdown.Toggle>
                                        <Dropdown.Menu className="dropdown-flex">
                                          {CheckPermission(
                                            'tabs',
                                            'Leave Management',
                                            'Staff Leave',
                                            '',
                                            TABS[activeTab],
                                            'Edit'
                                          ) && (
                                            <Dropdown.Item
                                              onClick={this.editLeave.bind(this, leave)}
                                              disabled={this.disableEditOrCancel(leave, 'edit')}
                                            >
                                              {t('edit')}
                                            </Dropdown.Item>
                                          )}

                                          {CheckPermission(
                                            'tabs',
                                            'Leave Management',
                                            'Staff Leave',
                                            '',
                                            TABS[activeTab],
                                            'Delete'
                                          ) && (
                                            <Dropdown.Item
                                              onClick={this.cancelLeave.bind(this, leave, false)}
                                              disabled={this.disableEditOrCancel(leave, 'cancel')}
                                            >
                                              {t('cancel')}
                                            </Dropdown.Item>
                                          )}
                                        </Dropdown.Menu>
                                      </Dropdown>
                                    </div>
                                  )}
                              </div>
                            </td>
                          </tr>
                        ))}

                      {this.state.showReject && (
                        <ApproveConfirmModal
                          componentName={'MyLeaves'}
                          leavedatas={this.state.leaveList}
                          show={this.state.showReject}
                          active={LEAVE_TYPE[activeTab]}
                          activeTab={this.state.activeTab}
                          closed={this.getleaveStatus.bind(this)}
                        />
                      )}
                    </tbody>
                  </table>
                  {myLeaveList.size > 0 && (
                    <Pagination
                      pagination={this.pagination}
                      onNextClick={this.onNextClick}
                      pagevalue={pageCount}
                      onBackClick={this.onBackClick}
                      onFullLastClick={this.onFullLastClick}
                      onFullForwardClick={this.onFullForwardClick}
                      data={totalPages}
                      currentPage={currentPage}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        <AlertModal
          show={modalData.show}
          title={modalData.title || ''}
          description={modalData.description || ''}
          variant={modalData.variant || 'confirm'}
          confirmButtonLabel={modalData.confirmButtonLabel || 'YES'}
          cancelButtonLabel={modalData.cancelButtonLabel || 'NO'}
          onClose={this.onModalClose.bind(this)}
          onConfirm={this.onConfirm.bind(this)}
          data={modalData.data}
        />
      </React.Fragment>
    );
  }
}

MyLeaves.propTypes = {
  history: PropTypes.object,
  userId: PropTypes.string,
  getMyLeaveList: PropTypes.func,
  myLeaveList: PropTypes.instanceOf(List),
  activeInstitutionCalendar: PropTypes.instanceOf(Map),
  institutionCalendar: PropTypes.instanceOf(List),
  setData: PropTypes.func,
  getAllPermissionList: PropTypes.func,
  getLeaveCategories: PropTypes.func,
  cancelLeave: PropTypes.func,
  getLeaveOverview: PropTypes.func,
  leaveOverview: PropTypes.instanceOf(List),
  getPermissionOverview: PropTypes.func,
  setLeaveData: PropTypes.func,
  location: PropTypes.object,
  setBreadCrumbName: PropTypes.func,
  userType: PropTypes.string,
  permissionOverview: PropTypes.instanceOf(List),
};

const mapStateToProps = (state) => {
  return {
    userId: selectUserId(state),
    userType: selectUserType(state),
    myLeaveList: selectMyLeaveList(state),
    institutionCalendar: selectInstitutionCalendar(state),
    activeInstitutionCalendar: selectActiveInstitutionCalendar(state),
    leaveCategories: selectLeaveCategories(state),
    permissions: selectPermissionList(state),
    leaveOverview: selectLeaveOverview(state),
    permissionOverview: selectPermissionOverview(state),
  };
};

export default compose(withRouter, connect(mapStateToProps, actions))(MyLeaves);
