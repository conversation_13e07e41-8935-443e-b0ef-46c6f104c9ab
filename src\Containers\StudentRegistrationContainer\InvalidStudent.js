/* eslint-disable react/jsx-key */
import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { Table, Modal, Button } from 'react-bootstrap';
import { NotificationManager } from 'react-notifications';
import { Trans } from 'react-i18next';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { t } from 'i18next';
import axios from '../../axios';
import Loader from '../../Widgets/Loader/Loader';
import TableEmptyMessage from '../../Widgets/CustomMessage/TableEmptyMessage';
import Search from '../../Widgets/Search/Search';
import Pagination from '../../Components/StaffManagement/Pagination';
import { CheckPermission } from '../../Modules/Shared/Permissions';
import * as actions from '../../_reduxapi/user_management/action';
import Export from './Export';
import { getEnvCollegeName, isModuleEnabled } from 'utils';
class InvalidStudent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      isLoading: false,
      registrationConfirmationShow: false,
      mailId: '',
      message: '',
      invalidData: [
        { value: t('user_management.school_certificate'), isChecked: false },
        { value: t('user_management.entrance_exam_certificate'), isChecked: false },
        { value: t('user_management.admission_documents'), isChecked: false },
        { value: t('user_management.national_resident_id'), isChecked: false },
        { value: t('user_management.college_id'), isChecked: false },
        { value: t('user_management.national_address'), isChecked: false },
      ],
      searchText: '',
      limit: 10,
      totalPages: null,
      pageLength: 1,
    };
  }

  componentDidMount() {
    this.fetchApi({});
  }

  successTrigger = (res) => {
    const data = res.data.data.map((data) => {
      return {
        id: data._id,
        family: data.name.family,
        academic: data.academic,
        email: data.email,
        first: data.name.first,
        last: data.name.last,
        middle: data.name.middle,
        gender: data.gender,
        nationality_id: data.address.nationality_id,
        enrollment_year: data.enrollment_year,
        program_no: data.program_no,
        isChecked: false,
      };
    });
    this.setState({
      data: data,
      isLoading: false,
      totalPages: res.data.totalPages,
    });
  };

  fetchApi = ({ limit = 10, pageLength = 1 }) => {
    const { getUserManagementList } = this.props;
    const { searchText } = this.state;
    this.setState({
      isLoading: true,
    });
    getUserManagementList(
      {
        searchText: searchText,
        type: 'student',
        status: 'invalid',
        limit: limit,
        pageLength: pageLength,
        slug: 'get_all',
      },
      this.successTrigger,
      () => {
        this.setState({
          data: [],
          isLoading: false,
          totalPages: 1,
          pageLength: 1,
        });
      }
    );
  };

  pagination = (e) => {
    this.setState(
      {
        limit: e.target.value,
        pageLength: 1,
      },
      () => {
        setTimeout(() => {
          this.fetchApi({ limit: this.state.limit });
        }, 500);
      }
    );
  };

  pageCount = (value) => {
    this.setState(
      {
        pageLength: value,
      },
      () => {
        this.fetchApi({ pageLength: value, limit: this.state.limit });
      }
    );
  };

  resetData = () => {
    this.setState({
      invalidData: [
        { value: t('user_management.school_certificate'), isChecked: false },
        { value: 'Entrance Exam Certificate', isChecked: false },
        { value: 'Admission Documents', isChecked: false },
        { value: 'National ID / Resident ID', isChecked: false },
        { value: t('user_management.college_id'), isChecked: false },
        { value: t('user_management.national_address'), isChecked: false },
      ],
    });
  };

  handleClickRegistrationConfirmationClose = () => {
    this.setState(
      {
        registrationConfirmationShow: false,
      },
      () => {
        this.resetData();
      }
    );
  };

  handleAllChecked = (event) => {
    let data = this.state.data;
    data.map((data) => (data.isChecked = event.target.checked));
    this.setState({ data: data });
  };

  handleCheckFieldElement = (event, index) => {
    let data = this.state.data;

    data[index].isChecked = event.target.checked;
    this.setState({ data: data });
  };

  handleClickMailPush = () => {
    let mailData = this.state.data.filter((data) => {
      return data.isChecked === true;
    });
    if (mailData.length > 0) {
      let id = mailData.map((data) => {
        return data.id;
      });

      this.setState({
        registrationConfirmationShow: true,
        mailId: id,
        // message: message,
      });
    } else {
      NotificationManager.error(t(`user_management.Choose_atLeast_one_checkbox`), '', 2000);
    }
  };

  handleSendMail = (e) => {
    const invalidDataFilter = this.state.invalidData.filter((data) => {
      return data.isChecked === true;
    });
    const docSecNeed = isModuleEnabled('DOCUMENT_SEC_NEED');
    if (!docSecNeed || (invalidDataFilter && invalidDataFilter.length > 0)) {
      const invalidData = invalidDataFilter.map((data) => {
        return data.value;
      });

      const message = `<p>Dear User,</p><p>Greetings from ${getEnvCollegeName()}</p>${
        docSecNeed
          ? `<p>The data which you provided is invalid. Kindly visit the Academic Affairs administration office with the following documents.</p><p><b>${
              invalidData[0] !== undefined ? invalidData[0] : ''
            }</b></p><p><b>${invalidData[1] !== undefined ? invalidData[1] : ''}</b></p><p><b>${
              invalidData[2] !== undefined ? invalidData[2] : ''
            }</b></p><p><b>${invalidData[3] !== undefined ? invalidData[3] : ''}</b></p><p><b>${
              invalidData[4] !== undefined ? invalidData[4] : ''
            }</b></p><p><b>${invalidData[5] !== undefined ? invalidData[5] : ''}</b></p>`
          : `<p>The data which you provided is invalid, please visit the registration office with relevant documents.</p>`
      }<br><p>Best Regards<br>${getEnvCollegeName()}</p>`;

      const data = {
        type: 'invalid',
        message: message,
        id: this.state.mailId,
      };

      // return;
      this.setState({
        isLoading: true,
      });
      axios
        .post(`user/user_mail_push`, data)
        .then((res) => {
          this.setState(
            {
              isLoading: false,
              registrationConfirmationShow: false,
            },
            () => {
              this.fetchApi({});
              this.resetData();
            }
          );
          NotificationManager.success(t(`user_management.Mail_Sent_Successfully`));
        })
        .catch((error) => {
          NotificationManager.warning(`${error.response.data.message}`);
          this.setState({
            isLoading: false,
          });
        });
    } else {
      NotificationManager.error(t(`user_management.Choose_atLeast_one_checkbox`), '', 2000);
    }
  };

  handleCheckInvalidData = (event, index) => {
    let invalidData = this.state.invalidData;
    invalidData[index].isChecked = event.target.checked;
    this.setState({ invalidData: invalidData });
  };

  handleClickView = (data) => {
    this.props.history.push({
      pathname: '/studentvalid/all',
      search: '?id=' + data.id + '&tabs=5',
    });
  };

  doSearch = (evt) => {
    if (this.timeout) clearTimeout(this.timeout);
    this.timeout = setTimeout(() => {
      const { limit } = this.state;
      setTimeout(() => {
        this.fetchApi({ limit });
        this.setState({ pageLength: 1 });
      }, 500);
    }, 500);
  };

  handleSearch = (e) => {
    this.setState({ searchText: e.target.value }, () => this.doSearch(e));
  };

  checkProfilePermission = () => {
    return (
      CheckPermission(
        'subTabs',
        'User Management',
        'Student Management',
        '',
        'Registration Pending',
        '',
        'Invalid',
        'Profile View'
      ) ||
      CheckPermission(
        'subTabs',
        'User Management',
        'Student Management',
        '',
        'Registration Pending',
        '',
        'Invalid',
        'Biometric View'
      )
    );
  };

  render() {
    const header = [
      <Trans i18nKey={'academic_no'}></Trans>,
      <Trans i18nKey={'email'}></Trans>,
      <Trans i18nKey={'first_name'}></Trans>,
      'Middle Name',
      'Last Name',
      <Trans i18nKey={'gender'}></Trans>,
      <Trans i18nKey={'national_id'}></Trans>,
    ];

    let mailData = this.state.data.filter((data) => {
      return data.isChecked === true;
    });
    const docSecNeed = isModuleEnabled('DOCUMENT_SEC_NEED');
    return (
      <div className="main bg-gray pt-2 pb-5">
        <Loader isLoading={this.state.isLoading} />
        <div className="container-fluid">
          <div className="">
            {/* <TableTitle title="IBS National College" /> */}
            <div className="float-left  pt-2 pl-3">
              {' '}
              <span className="">
                {CheckPermission(
                  'subTabs',
                  'User Management',
                  'Student Management',
                  '',
                  'Registration Pending',
                  '',
                  'Invalid',
                  'Send Mail'
                ) && (
                  <Button
                    disabled={mailData.length > 0 ? false : true}
                    className={mailData.length > 0 ? '' : 'disabled-icon'}
                    onClick={(e) => this.handleClickMailPush(e)}
                  >
                    <Trans i18nKey={'send_mail'}></Trans>{' '}
                    <i className="fa fa-envelope" aria-hidden="true"></i>{' '}
                  </Button>
                )}
              </span>
              {/* mail funtion start   */}
              <Modal
                show={this.state.registrationConfirmationShow}
                centered
                size="lg"
                onHide={this.handleClickRegistrationConfirmationClose}
              >
                <Modal.Header closeButton>
                  <Modal.Title id="example-modal-sizes-title-sm">
                    <div className="f-18">Registration incomplete</div>
                    <div className="f-14">
                      Please check the below email to be sent for registration invalidation.
                    </div>
                  </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                  <div className="f-14">Dear User,</div>
                  <div className="f-14">Greetings from {getEnvCollegeName()}</div>
                  <div className="f-14">
                    {docSecNeed
                      ? `The data which you provided is invalid. Kindly visit the Academic Affairs
                    administration office with the following documents.`
                      : `The data which you provided is invalid, please visit the registration office with relevant documents.`}
                  </div>
                  <div className="mt-3 mb-3">
                    {docSecNeed &&
                      this.state.invalidData.map((data, index) => (
                        <div>
                          <input
                            type="checkbox"
                            className="calendarFormRadio mr-2"
                            onClick={(event) => this.handleCheckInvalidData(event, index)}
                            value="checkedall"
                          />
                          {data.value}
                        </div>
                      ))}
                  </div>
                  <div className="f-14">Best Regards</div>
                  <div className="f-14">{getEnvCollegeName()}</div>
                </Modal.Body>
                <Modal.Footer>
                  <Button onClick={this.handleClickRegistrationConfirmationClose}>CANCEL</Button>
                  <Button onClick={(e) => this.handleSendMail(e)}>SEND MAIL</Button>
                </Modal.Footer>
              </Modal>
              {/* mail funtion end */}
            </div>
            <div className="float-right p-2 d-flex">
              <Export userType="student" status="invalid" />
              {CheckPermission(
                'subTabs',
                'User Management',
                'Student Management',
                '',
                'Registration Pending',
                '',
                'Invalid',
                'Search'
              ) && (
                <Search
                  value={this.state.searchText}
                  onChange={(e) => this.handleSearch(e)}
                  type="student"
                />
              )}
            </div>
            <div className="clearfix"> </div>
            <React.Fragment>
              <div className="p-3">
                <div className="dash-table">
                  <Table responsive hover>
                    <thead className="th-change">
                      <tr>
                        <th>
                          {' '}
                          <input
                            type="checkbox"
                            className="calendarFormRadio"
                            onClick={this.handleAllChecked}
                            value="checkedall"
                          />
                        </th>
                        {header.map((header, i) => (
                          <th key={i}>{header}</th>
                        ))}
                      </tr>
                    </thead>
                    {this.state.data.length === 0 ? (
                      <TableEmptyMessage />
                    ) : (
                      <tbody>
                        {this.state.data.map((data, index) => (
                          <tr
                            className="tr-change"
                            style={{
                              background: data.isChecked === true ? '#D1F4FF' : '',
                            }}
                          >
                            {data.isChecked !== true ? (
                              <td>
                                <input
                                  type="checkbox"
                                  className="calendarFormRadio"
                                  onClick={(event) => this.handleCheckFieldElement(event, index)}
                                  value="checkedall"
                                />
                              </td>
                            ) : (
                              <td>
                                <input
                                  type="checkbox"
                                  className="calendarFormRadio"
                                  onClick={(event) => this.handleCheckFieldElement(event, index)}
                                  value="checkedall"
                                  checked
                                />
                              </td>
                            )}
                            <td
                              className={this.checkProfilePermission() ? 'profile_view' : ''}
                              onClick={
                                this.checkProfilePermission()
                                  ? (e) => this.handleClickView(data)
                                  : () => {}
                              }
                            >
                              {data.academic}
                            </td>
                            <td>{data.email}</td>
                            <td>{data.first}</td>
                            <td>{data.middle}</td>
                            <td>{data.last}</td>
                            <td>{data.gender}</td>
                            <td>{data.nationality_id}</td>
                          </tr>
                        ))}
                      </tbody>
                    )}
                  </Table>
                </div>
                <Pagination
                  totalPages={this.state.totalPages}
                  switchPagination={this.pagination}
                  switchPageCount={this.pageCount}
                  pageLength={this.state.pageLength}
                />
              </div>
            </React.Fragment>
          </div>
        </div>
      </div>
    );
  }
}

InvalidStudent.propTypes = {
  history: PropTypes.object,
  getUserManagementList: PropTypes.func,
};

export default connect(null, actions)(withRouter(InvalidStudent));
